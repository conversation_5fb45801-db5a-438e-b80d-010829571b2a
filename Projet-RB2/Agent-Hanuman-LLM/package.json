{"name": "agent-hanuman-llm", "version": "1.0.0", "description": "🐒 Agent <PERSON>uman LLM - Gardien Central du Projet Retreat & Be", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "docker:build": "docker build -t agent-hanuman-llm .", "docker:run": "docker run -p 5003:5003 agent-hanuman-llm", "docker:compose": "docker-compose up -d", "docker:down": "docker-compose down", "deploy:dev": "npm run build && npm run start", "deploy:prod": "npm run clean && npm run build && NODE_ENV=production npm start", "health": "curl -f http://localhost:5003/health || exit 1", "logs": "tail -f logs/hanuman.log", "setup": "npm install && npm run build", "hanuman:start": "echo '🐒 <PERSON><PERSON><PERSON><PERSON>...' && npm run dev", "hanuman:stop": "echo '🙏 <PERSON><PERSON><PERSON><PERSON>...' && pkill -f 'ts-node-dev.*hanuman'", "hanuman:restart": "npm run hanuman:stop && sleep 2 && npm run hanuman:start", "hanuman:status": "ps aux | grep hanuman || echo 'Hanuman non actif'"}, "keywords": ["hanuman", "llm", "ai", "agent", "retreat-and-be", "microservices", "orchestration", "monitoring", "spiritual-ai"], "author": "Retreat & Be Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "axios": "^1.6.2", "redis": "^4.6.10", "ioredis": "^5.3.2", "kafkajs": "^2.2.4", "pg": "^8.11.3", "typeorm": "^0.3.17", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "openai": "^4.20.1", "@anthropic-ai/sdk": "^0.9.1", "ollama": "^0.4.6", "node-cron": "^3.0.3", "ws": "^8.14.2", "socket.io": "^4.7.4", "prometheus-api-metrics": "^3.2.2", "prom-client": "^15.0.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/ws": "^8.5.10", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "nodemon": "^3.0.2", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/retreat-and-be/agent-hanuman-llm.git"}, "bugs": {"url": "https://github.com/retreat-and-be/agent-hanuman-llm/issues"}, "homepage": "https://github.com/retreat-and-be/agent-hanuman-llm#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "prefer-const": "error", "no-var": "error"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2}, "nodemonConfig": {"watch": ["src"], "ext": "ts", "ignore": ["src/**/*.spec.ts"], "exec": "ts-node src/index.ts"}}