/**
 * 🌐 Hanuman API - Interface REST pour l'Agent <PERSON> LLM
 * Expose les fonctionnalités de Hanuman via une API REST
 */

import express, { Express, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { EventEmitter } from 'events';
import { Logger } from '../utils/Logger';
import { HanumanCore } from '../core/HanumanCore';
import { 
  UserMessage, 
  HanumanResponse, 
  SystemStatus,
  AgentInfo,
  SystemAlert
} from '../types/HanumanTypes';

export class HanumanAPI extends EventEmitter {
  private app: Express;
  private server: any;
  private logger: Logger;
  private core: HanumanCore;
  private port: number;
  private isRunning: boolean = false;

  constructor(core: HanumanCore) {
    super();
    this.core = core;
    this.logger = new Logger('HanumanAPI');
    this.port = parseInt(process.env.HANUMAN_API_PORT || '5003');
    
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
    
    this.logger.info('🌐 Hanuman API initialisé');
  }

  /**
   * Configure les middlewares
   */
  private setupMiddleware(): void {
    // Sécurité
    this.app.use(helmet());
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limite chaque IP à 100 requêtes par fenêtre
      message: {
        error: 'Trop de requêtes, veuillez réessayer plus tard',
        retryAfter: '15 minutes'
      }
    });
    this.app.use('/api/', limiter);

    // Parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Logging des requêtes
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      this.logger.debug(`${req.method} ${req.path} - ${req.ip}`);
      next();
    });
  }

  /**
   * Configure les routes
   */
  private setupRoutes(): void {
    // Route de santé
    this.app.get('/health', this.handleHealthCheck.bind(this));

    // Routes de l'API principale
    const apiRouter = express.Router();

    // Chat avec Hanuman
    apiRouter.post('/chat', this.handleChatMessage.bind(this));
    apiRouter.get('/chat/history/:userId', this.handleChatHistory.bind(this));

    // Statut système
    apiRouter.get('/system/status', this.handleSystemStatus.bind(this));
    apiRouter.get('/system/metrics', this.handleSystemMetrics.bind(this));
    apiRouter.get('/system/alerts', this.handleSystemAlerts.bind(this));

    // Agents
    apiRouter.get('/agents', this.handleGetAgents.bind(this));
    apiRouter.get('/agents/:agentId', this.handleGetAgent.bind(this));
    apiRouter.post('/agents/:agentId/command', this.handleAgentCommand.bind(this));

    // Workflows
    apiRouter.get('/workflows', this.handleGetWorkflows.bind(this));
    apiRouter.post('/workflows', this.handleCreateWorkflow.bind(this));
    apiRouter.get('/workflows/:workflowId', this.handleGetWorkflow.bind(this));

    // Administration
    apiRouter.get('/admin/config', this.handleGetConfig.bind(this));
    apiRouter.post('/admin/restart', this.handleRestart.bind(this));
    apiRouter.get('/admin/logs', this.handleGetLogs.bind(this));

    this.app.use('/api', apiRouter);

    // Route par défaut
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        message: '🐒 Hanuman LLM Agent API',
        version: '1.0.0',
        status: 'active',
        endpoints: {
          health: '/health',
          chat: '/api/chat',
          system: '/api/system/status',
          agents: '/api/agents',
          workflows: '/api/workflows'
        },
        documentation: '/api/docs'
      });
    });
  }

  /**
   * Configure la gestion d'erreurs
   */
  private setupErrorHandling(): void {
    // Gestionnaire d'erreurs 404
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({
        error: 'Endpoint non trouvé',
        path: req.originalUrl,
        method: req.method
      });
    });

    // Gestionnaire d'erreurs global
    this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      this.logger.error('❌ Erreur API:', error);
      
      res.status(500).json({
        error: 'Erreur interne du serveur',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Une erreur est survenue'
      });
    });
  }

  /**
   * Démarre l'API
   */
  public async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(this.port, () => {
          this.isRunning = true;
          this.logger.info(`🚀 API Hanuman démarrée sur le port ${this.port}`);
          this.emit('apiStarted');
          resolve();
        });

        this.server.on('error', (error: Error) => {
          this.logger.error('❌ Erreur serveur API:', error);
          this.emit('apiError', error);
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Arrête l'API
   */
  public async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          this.isRunning = false;
          this.logger.info('🛑 API Hanuman arrêtée');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * Gestionnaires de routes
   */
  private async handleHealthCheck(req: Request, res: Response): Promise<void> {
    try {
      const systemStatus = await this.core.getSystemContext();
      const componentStatus = this.core.getComponentStatus();

      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        hanuman: {
          active: this.core.isHanumanActive(),
          components: componentStatus
        },
        system: {
          health: systemStatus.systemHealth,
          load: systemStatus.currentLoad,
          activeAgents: systemStatus.activeAgents.length
        }
      });
    } catch (error) {
      res.status(500).json({
        status: 'unhealthy',
        error: error.message
      });
    }
  }

  private async handleChatMessage(req: Request, res: Response): Promise<void> {
    try {
      const { message, userId, context } = req.body;

      if (!message || !userId) {
        res.status(400).json({
          error: 'Message et userId requis'
        });
        return;
      }

      const userMessage: UserMessage = {
        id: `msg_${Date.now()}`,
        userId,
        content: message,
        timestamp: new Date(),
        context,
        metadata: {
          ip: req.ip,
          userAgent: req.get('User-Agent')
        }
      };

      // Traitement asynchrone du message
      this.core.emit('userMessage', {
        id: userMessage.id,
        type: 'userMessage',
        source: 'HanumanAPI',
        data: userMessage,
        timestamp: new Date(),
        priority: 'medium'
      });

      // Attente de la réponse (avec timeout)
      const response = await this.waitForResponse(userMessage.id, 30000);

      res.json({
        success: true,
        response: response.content,
        agents: response.agentsInvolved,
        confidence: response.confidence,
        processingTime: response.processingTime,
        timestamp: response.timestamp
      });

    } catch (error) {
      this.logger.error('❌ Erreur lors du traitement du message:', error);
      res.status(500).json({
        error: 'Erreur lors du traitement du message',
        message: error.message
      });
    }
  }

  private async handleChatHistory(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const limit = parseInt(req.query.limit as string) || 50;

      // Récupération de l'historique depuis le composant Voice
      const history = this.core.getSystemContext();

      res.json({
        userId,
        messages: [], // TODO: Implémenter la récupération de l'historique
        total: 0,
        limit
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération de l\'historique'
      });
    }
  }

  private async handleSystemStatus(req: Request, res: Response): Promise<void> {
    try {
      const systemContext = this.core.getSystemContext();
      
      res.json({
        timestamp: systemContext.timestamp,
        health: systemContext.systemHealth,
        load: systemContext.currentLoad,
        activeAgents: systemContext.activeAgents,
        alerts: systemContext.alerts.filter(a => !a.resolved),
        performance: systemContext.performance
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération du statut système'
      });
    }
  }

  private async handleSystemMetrics(req: Request, res: Response): Promise<void> {
    try {
      const systemContext = this.core.getSystemContext();
      
      res.json({
        timestamp: new Date(),
        metrics: systemContext.performance,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération des métriques'
      });
    }
  }

  private async handleSystemAlerts(req: Request, res: Response): Promise<void> {
    try {
      const systemContext = this.core.getSystemContext();
      const activeOnly = req.query.active === 'true';
      
      let alerts = systemContext.alerts;
      if (activeOnly) {
        alerts = alerts.filter(alert => !alert.resolved);
      }

      res.json({
        alerts,
        total: alerts.length,
        active: alerts.filter(a => !a.resolved).length
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération des alertes'
      });
    }
  }

  private async handleGetAgents(req: Request, res: Response): Promise<void> {
    try {
      const systemContext = this.core.getSystemContext();
      
      res.json({
        agents: systemContext.activeAgents,
        total: systemContext.activeAgents.length,
        timestamp: new Date()
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération des agents'
      });
    }
  }

  private async handleGetAgent(req: Request, res: Response): Promise<void> {
    try {
      const { agentId } = req.params;
      const systemContext = this.core.getSystemContext();
      
      const agent = systemContext.activeAgents.find(a => a.id === agentId);
      
      if (!agent) {
        res.status(404).json({
          error: 'Agent non trouvé'
        });
        return;
      }

      res.json(agent);
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération de l\'agent'
      });
    }
  }

  private async handleAgentCommand(req: Request, res: Response): Promise<void> {
    try {
      const { agentId } = req.params;
      const { command, payload } = req.body;

      // TODO: Implémenter l'envoi de commandes aux agents
      res.json({
        message: `Commande ${command} envoyée à l'agent ${agentId}`,
        status: 'pending'
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de l\'envoi de la commande'
      });
    }
  }

  private async handleGetWorkflows(req: Request, res: Response): Promise<void> {
    try {
      // TODO: Implémenter la récupération des workflows
      res.json({
        workflows: [],
        total: 0
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération des workflows'
      });
    }
  }

  private async handleCreateWorkflow(req: Request, res: Response): Promise<void> {
    try {
      const { name, steps } = req.body;

      // TODO: Implémenter la création de workflows
      res.json({
        message: 'Workflow créé',
        workflowId: `wf_${Date.now()}`
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la création du workflow'
      });
    }
  }

  private async handleGetWorkflow(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId } = req.params;

      // TODO: Implémenter la récupération d'un workflow spécifique
      res.json({
        id: workflowId,
        status: 'not_found'
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération du workflow'
      });
    }
  }

  private async handleGetConfig(req: Request, res: Response): Promise<void> {
    try {
      // Retourne une version sécurisée de la configuration
      res.json({
        voice: {
          provider: process.env.LLM_PROVIDER,
          model: process.env.LLM_MODEL,
          languages: ['fr', 'en', 'es']
        },
        monitoring: {
          enabled: true,
          interval: process.env.METRICS_INTERVAL
        },
        version: '1.0.0'
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération de la configuration'
      });
    }
  }

  private async handleRestart(req: Request, res: Response): Promise<void> {
    try {
      res.json({
        message: 'Redémarrage de Hanuman programmé',
        status: 'scheduled'
      });

      // Redémarrage après un délai
      setTimeout(() => {
        process.exit(0);
      }, 1000);
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors du redémarrage'
      });
    }
  }

  private async handleGetLogs(req: Request, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 100;
      const level = req.query.level as string || 'info';

      // TODO: Implémenter la récupération des logs
      res.json({
        logs: [],
        total: 0,
        limit,
        level
      });
    } catch (error) {
      res.status(500).json({
        error: 'Erreur lors de la récupération des logs'
      });
    }
  }

  /**
   * Utilitaires
   */
  private async waitForResponse(messageId: string, timeout: number): Promise<HanumanResponse> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Timeout lors de l\'attente de la réponse'));
      }, timeout);

      const responseHandler = (event: any) => {
        if (event.data && event.data.messageId === messageId) {
          clearTimeout(timer);
          this.core.off('responseGenerated', responseHandler);
          resolve(event.data.response);
        }
      };

      this.core.on('responseGenerated', responseHandler);
    });
  }

  /**
   * Getters publics
   */
  public isAPIRunning(): boolean {
    return this.isRunning;
  }

  public getPort(): number {
    return this.port;
  }
}

export default HanumanAPI;
