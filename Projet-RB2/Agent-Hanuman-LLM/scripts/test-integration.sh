#!/bin/bash

# 🧪 Script de Test d'Intégration - Agent <PERSON>
# Vérifie l'intégration complète avec l'écosystème Retreat & Be

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
HANUMAN_API_PORT=5003
CHAT_INTERFACE_PORT=3001

# Fonction d'affichage
print_header() {
    echo -e "${PURPLE}🧪 ========================================${NC}"
    echo -e "${PURPLE}🐒 TEST INTÉGRATION AGENT HANUMAN LLM${NC}"
    echo -e "${PURPLE}🕉️ AUM HANUMATE NAMAHA${NC}"
    echo -e "${PURPLE}🔬 Vérification Écosystème Retreat & Be${NC}"
    echo -e "${PURPLE}🧪 ========================================${NC}"
    echo ""
}

print_step() {
    echo -e "${CYAN}🔍 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Compteurs de tests
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Fonction de test générique
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${CYAN}🧪 Test: $test_name${NC}"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_success "$test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "$test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Test de l'API Hanuman
test_hanuman_api() {
    print_step "Test de l'API Hanuman LLM..."
    
    # Test health check
    run_test "Health Check API" "curl -f http://localhost:$HANUMAN_API_PORT/health"
    
    # Test endpoint principal
    run_test "Endpoint Principal" "curl -f http://localhost:$HANUMAN_API_PORT/"
    
    # Test système status
    run_test "System Status" "curl -f http://localhost:$HANUMAN_API_PORT/api/system/status"
    
    # Test agents endpoint
    run_test "Agents Endpoint" "curl -f http://localhost:$HANUMAN_API_PORT/api/agents"
    
    # Test chat endpoint (POST)
    run_test "Chat Endpoint" "curl -f -X POST http://localhost:$HANUMAN_API_PORT/api/chat -H 'Content-Type: application/json' -d '{\"message\":\"Test\",\"userId\":\"test\"}'"
}

# Test de l'interface chat
test_chat_interface() {
    print_step "Test de l'interface chat..."
    
    # Test disponibilité interface
    run_test "Interface Chat Disponible" "curl -f http://localhost:$CHAT_INTERFACE_PORT/"
    
    # Test API chat locale
    run_test "API Chat Locale" "curl -f http://localhost:$CHAT_INTERFACE_PORT/api/chat -X POST -H 'Content-Type: application/json' -d '{\"message\":\"Bonjour Hanuman\"}'"
    
    # Test configuration Hanuman
    local config_file="$PROJECT_DIR/../../hanuman-working/hanuman-config.json"
    if [ -f "$config_file" ]; then
        print_success "Configuration Hanuman trouvée"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_warning "Configuration Hanuman non trouvée"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

# Test des services Retreat & Be
test_retreat_services() {
    print_step "Test des services Retreat & Be..."
    
    # Services à tester
    declare -A services=(
        ["Frontend React"]="localhost:3000"
        ["Backend NestJS"]="localhost:3001"
        ["Agent-RB"]="localhost:5000"
        ["Agent-IA"]="localhost:5002"
        ["SuperAgent"]="localhost:5001"
    )
    
    for service in "${!services[@]}"; do
        local endpoint="${services[$service]}"
        local host=$(echo "$endpoint" | cut -d':' -f1)
        local port=$(echo "$endpoint" | cut -d':' -f2)
        
        run_test "$service ($endpoint)" "timeout 3 bash -c '</dev/tcp/$host/$port'"
    done
}

# Test de communication inter-agents
test_agent_communication() {
    print_step "Test de communication inter-agents..."
    
    # Test de coordination via Hanuman
    local test_payload='{
        "message": "Test coordination agents",
        "userId": "integration-test",
        "context": {
            "test": true,
            "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"
        }
    }'
    
    run_test "Coordination Agents" "curl -f -X POST http://localhost:$HANUMAN_API_PORT/api/chat -H 'Content-Type: application/json' -d '$test_payload'"
    
    # Test workflows
    run_test "Workflows Endpoint" "curl -f http://localhost:$HANUMAN_API_PORT/api/workflows"
}

# Test de monitoring
test_monitoring() {
    print_step "Test du système de monitoring..."
    
    # Test métriques
    run_test "Métriques Système" "curl -f http://localhost:$HANUMAN_API_PORT/api/system/metrics"
    
    # Test alertes
    run_test "Alertes Système" "curl -f http://localhost:$HANUMAN_API_PORT/api/system/alerts"
    
    # Test logs (si disponible)
    run_test "Logs Endpoint" "curl -f http://localhost:$HANUMAN_API_PORT/api/admin/logs"
}

# Test de sécurité
test_security() {
    print_step "Test de sécurité..."
    
    # Test rate limiting
    print_info "Test du rate limiting..."
    local rate_limit_passed=true
    for i in {1..10}; do
        if ! curl -f http://localhost:$HANUMAN_API_PORT/health > /dev/null 2>&1; then
            rate_limit_passed=false
            break
        fi
        sleep 0.1
    done
    
    if [ "$rate_limit_passed" = true ]; then
        print_success "Rate Limiting"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_error "Rate Limiting"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Test CORS headers
    run_test "CORS Headers" "curl -f -H 'Origin: http://localhost:3000' http://localhost:$HANUMAN_API_PORT/health"
}

# Test de performance
test_performance() {
    print_step "Test de performance..."
    
    # Test temps de réponse API
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:$HANUMAN_API_PORT/health)
    local response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d'.' -f1)
    
    if [ "$response_time_ms" -lt 1000 ]; then
        print_success "Temps de réponse API ($response_time_ms ms)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_warning "Temps de réponse API lent ($response_time_ms ms)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Test charge CPU/Mémoire
    local cpu_usage=$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//' 2>/dev/null || echo "0")
    if [ "${cpu_usage%.*}" -lt 80 ]; then
        print_success "Usage CPU acceptable ($cpu_usage%)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_warning "Usage CPU élevé ($cpu_usage%)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

# Test d'intégration complète
test_full_integration() {
    print_step "Test d'intégration complète..."
    
    # Scénario complet : Question utilisateur -> Réponse Hanuman
    local integration_test='{
        "message": "Bonjour Hanuman, peux-tu me recommander une retraite yoga en Inde pour débutant ?",
        "userId": "integration-test-user",
        "context": {
            "preferences": {
                "level": "beginner",
                "type": "yoga",
                "location": "india"
            }
        }
    }'
    
    print_info "Test du scénario complet..."
    local response=$(curl -s -X POST http://localhost:$HANUMAN_API_PORT/api/chat \
        -H 'Content-Type: application/json' \
        -d "$integration_test")
    
    if echo "$response" | grep -q "response" && echo "$response" | grep -q "confidence"; then
        print_success "Scénario d'intégration complet"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_error "Scénario d'intégration complet"
        print_info "Réponse reçue: $response"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

# Génération du rapport
generate_report() {
    echo ""
    echo -e "${PURPLE}📊 ========================================${NC}"
    echo -e "${PURPLE}📋 RAPPORT DE TEST D'INTÉGRATION${NC}"
    echo -e "${PURPLE}📊 ========================================${NC}"
    
    echo -e "${CYAN}📈 Statistiques:${NC}"
    echo -e "   🧪 Tests totaux: $TOTAL_TESTS"
    echo -e "   ✅ Tests réussis: $PASSED_TESTS"
    echo -e "   ❌ Tests échoués: $FAILED_TESTS"
    
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "   📊 Taux de réussite: $success_rate%"
    
    echo ""
    if [ "$FAILED_TESTS" -eq 0 ]; then
        echo -e "${GREEN}🎉 TOUS LES TESTS SONT PASSÉS !${NC}"
        echo -e "${GREEN}🐒 Hanuman est parfaitement intégré !${NC}"
        echo -e "${GREEN}🕉️ L'écosystème Retreat & Be est opérationnel${NC}"
    elif [ "$success_rate" -ge 80 ]; then
        echo -e "${YELLOW}⚠️ Intégration majoritairement fonctionnelle${NC}"
        echo -e "${YELLOW}🔧 Quelques ajustements nécessaires${NC}"
    else
        echo -e "${RED}❌ Problèmes d'intégration détectés${NC}"
        echo -e "${RED}🛠️ Intervention requise${NC}"
    fi
    
    echo ""
    echo -e "${CYAN}🔗 Endpoints testés:${NC}"
    echo -e "   📡 API Hanuman: http://localhost:$HANUMAN_API_PORT"
    echo -e "   💬 Chat Interface: http://localhost:$CHAT_INTERFACE_PORT"
    echo -e "   📊 Health Check: http://localhost:$HANUMAN_API_PORT/health"
    echo -e "   🎯 System Status: http://localhost:$HANUMAN_API_PORT/api/system/status"
    
    echo ""
    echo -e "${PURPLE}🕉️ AUM HANUMATE NAMAHA - Tests terminés ✨${NC}"
}

# Fonction principale
main() {
    print_header
    
    # Vérification que Hanuman est démarré
    if ! curl -f http://localhost:$HANUMAN_API_PORT/health > /dev/null 2>&1; then
        print_error "Agent Hanuman LLM non accessible sur le port $HANUMAN_API_PORT"
        print_info "Démarrez Hanuman avec: ./scripts/start-hanuman.sh"
        exit 1
    fi
    
    print_success "Agent Hanuman LLM détecté"
    echo ""
    
    # Exécution des tests
    test_hanuman_api
    echo ""
    
    test_chat_interface
    echo ""
    
    test_retreat_services
    echo ""
    
    test_agent_communication
    echo ""
    
    test_monitoring
    echo ""
    
    test_security
    echo ""
    
    test_performance
    echo ""
    
    test_full_integration
    
    # Génération du rapport final
    generate_report
    
    # Code de sortie basé sur les résultats
    if [ "$FAILED_TESTS" -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Gestion des signaux
trap 'print_error "Test interrompu"; exit 1' INT TERM

# Exécution du script principal
main "$@"
