{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Agentic-Coding-Framework-%20RB2/hanuman-final/hanuman_vision_interface.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Eye, Search, Globe, TrendingUp, AlertCircle, CheckCircle, Activity, Wifi, WifiOff, Zap, Database, Clock, BarChart3 } from 'lucide-react';\n\n// Interface pour l'Agent Web Research\ninterface WebSearchResult {\n  id: string;\n  query: string;\n  results: Array<{\n    title: string;\n    url: string;\n    snippet: string;\n    relevance: number;\n    timestamp: Date;\n  }>;\n  quality: number;\n  sources: string[];\n  metadata: Record<string, any>;\n}\n\ninterface DataSource {\n  id: string;\n  name: string;\n  url: string;\n  status: 'active' | 'inactive' | 'error';\n  lastCheck: Date;\n  responseTime: number;\n  reliability: number;\n}\n\ninterface WeakSignal {\n  id: string;\n  description: string;\n  confidence: number;\n  source: string;\n  timestamp: Date;\n  category: 'trend' | 'anomaly' | 'opportunity' | 'threat';\n}\n\nconst HanumanVisionInterface = ({ darkMode = true }) => {\n  const [webSearches, setWebSearches] = useState<WebSearchResult[]>([]);\n  const [activeQueries, setActiveQueries] = useState<string[]>([]);\n  const [dataQuality, setDataQuality] = useState(87.5);\n  const [sourcesMonitored, setSourcesMonitored] = useState<DataSource[]>([]);\n  const [weakSignals, setWeakSignals] = useState<WeakSignal[]>([]);\n  const [visionMetrics, setVisionMetrics] = useState({\n    totalSearches: 1247,\n    activeConnections: 23,\n    averageQuality: 87.5,\n    responseTime: 142,\n    successRate: 98.7\n  });\n  const [isConnected, setIsConnected] = useState(false);\n  const wsRef = useRef<WebSocket | null>(null);\n\n  // Connexion WebSocket avec l'Agent Web Research\n  useEffect(() => {\n    const connectToWebResearchAgent = () => {\n      try {\n        // Connexion à l'agent web-research sur le port configuré\n        wsRef.current = new WebSocket('ws://localhost:3001/vision');\n        \n        wsRef.current.onopen = () => {\n          console.log('🔗 Connexion établie avec Agent Web Research');\n          setIsConnected(true);\n          \n          // Demander le statut initial\n          wsRef.current?.send(JSON.stringify({\n            type: 'GET_STATUS',\n            timestamp: Date.now()\n          }));\n        };\n\n        wsRef.current.onmessage = (event) => {\n          const data = JSON.parse(event.data);\n          handleWebResearchMessage(data);\n        };\n\n        wsRef.current.onclose = () => {\n          console.log('❌ Connexion fermée avec Agent Web Research');\n          setIsConnected(false);\n          \n          // Tentative de reconnexion après 5 secondes\n          setTimeout(connectToWebResearchAgent, 5000);\n        };\n\n        wsRef.current.onerror = (error) => {\n          console.error('🚨 Erreur WebSocket:', error);\n          setIsConnected(false);\n        };\n\n      } catch (error) {\n        console.error('🚨 Erreur de connexion:', error);\n        setIsConnected(false);\n      }\n    };\n\n    connectToWebResearchAgent();\n\n    // Simulation de données en attendant la connexion réelle\n    const simulationInterval = setInterval(() => {\n      if (!isConnected) {\n        simulateVisionActivity();\n      }\n    }, 3000);\n\n    return () => {\n      clearInterval(simulationInterval);\n      wsRef.current?.close();\n    };\n  }, []);\n\n  const handleWebResearchMessage = (data: any) => {\n    switch (data.type) {\n      case 'SEARCH_STARTED':\n        setActiveQueries(prev => [...prev, data.query]);\n        break;\n        \n      case 'SEARCH_COMPLETED':\n        setWebSearches(prev => [data.result, ...prev.slice(0, 49)]);\n        setActiveQueries(prev => prev.filter(q => q !== data.query));\n        updateDataQuality(data.result.quality);\n        break;\n        \n      case 'SOURCES_UPDATE':\n        setSourcesMonitored(data.sources);\n        break;\n        \n      case 'WEAK_SIGNAL_DETECTED':\n        setWeakSignals(prev => [data.signal, ...prev.slice(0, 9)]);\n        break;\n        \n      case 'METRICS_UPDATE':\n        setVisionMetrics(data.metrics);\n        break;\n        \n      default:\n        console.log('📨 Message non géré:', data);\n    }\n  };\n\n  const simulateVisionActivity = () => {\n    // Simulation d'activité de recherche\n    const queries = [\n      'AI trends 2024',\n      'React best practices',\n      'Microservices architecture',\n      'Machine learning algorithms',\n      'Web development frameworks'\n    ];\n    \n    const randomQuery = queries[Math.floor(Math.random() * queries.length)];\n    \n    // Simuler une recherche\n    setActiveQueries(prev => [...prev, randomQuery]);\n    \n    setTimeout(() => {\n      const mockResult: WebSearchResult = {\n        id: `search_${Date.now()}`,\n        query: randomQuery,\n        results: [\n          {\n            title: `Results for ${randomQuery}`,\n            url: 'https://example.com',\n            snippet: 'Mock search result snippet...',\n            relevance: Math.random() * 100,\n            timestamp: new Date()\n          }\n        ],\n        quality: 70 + Math.random() * 30,\n        sources: ['web', 'news'],\n        metadata: {}\n      };\n      \n      setWebSearches(prev => [mockResult, ...prev.slice(0, 49)]);\n      setActiveQueries(prev => prev.filter(q => q !== randomQuery));\n      updateDataQuality(mockResult.quality);\n    }, 2000);\n  };\n\n  const updateDataQuality = (newQuality: number) => {\n    setDataQuality(prev => (prev * 0.9 + newQuality * 0.1));\n  };\n\n  const getQualityColor = (quality: number) => {\n    if (quality > 80) return 'text-green-400';\n    if (quality > 60) return 'text-yellow-400';\n    return 'text-red-400';\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'active': return <CheckCircle className=\"text-green-400\" size={16} />;\n      case 'inactive': return <AlertCircle className=\"text-yellow-400\" size={16} />;\n      case 'error': return <AlertCircle className=\"text-red-400\" size={16} />;\n      default: return <AlertCircle className=\"text-gray-400\" size={16} />;\n    }\n  };\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'trend': return <TrendingUp className=\"text-blue-400\" size={16} />;\n      case 'anomaly': return <AlertCircle className=\"text-orange-400\" size={16} />;\n      case 'opportunity': return <CheckCircle className=\"text-green-400\" size={16} />;\n      case 'threat': return <AlertCircle className=\"text-red-400\" size={16} />;\n      default: return <Activity className=\"text-gray-400\" size={16} />;\n    }\n  };\n\n  return (\n    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>\n      <div className=\"container mx-auto p-6\">\n        \n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\">\n              <Eye className=\"text-white\" size={24} />\n            </div>\n            <div>\n              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                Vision d'Hanuman\n              </h1>\n              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n                Organe de Recherche Web • Agent Web Research\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-3\">\n            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${\n              isConnected \n                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' \n                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'\n            }`}>\n              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}\n              <span className=\"text-sm font-medium\">\n                {isConnected ? 'Connecté' : 'Déconnecté'}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Métriques Globales */}\n        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n            Métriques de Vision\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-5 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-400\">\n                {visionMetrics.totalSearches.toLocaleString()}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Recherches Totales\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-green-400\">\n                {visionMetrics.activeConnections}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Connexions Actives\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className={`text-3xl font-bold ${getQualityColor(dataQuality)}`}>\n                {dataQuality.toFixed(1)}%\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Qualité Moyenne\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-purple-400\">\n                {visionMetrics.responseTime}ms\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Temps Réponse\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-orange-400\">\n                {visionMetrics.successRate}%\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Taux Succès\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n          \n          {/* Activité de Recherche */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n              🔍 Activité de Recherche\n            </h3>\n            \n            {activeQueries.length > 0 && (\n              <div className=\"mb-4\">\n                <h4 className={`text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>\n                  Recherches en cours:\n                </h4>\n                {activeQueries.map((query, index) => (\n                  <div key={index} className={`flex items-center space-x-2 p-2 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} mb-2`}>\n                    <Search className=\"animate-spin text-blue-400\" size={16} />\n                    <span className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>{query}</span>\n                  </div>\n                ))}\n              </div>\n            )}\n            \n            <div className=\"space-y-3\">\n              <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>\n                Recherches récentes:\n              </h4>\n              {webSearches.slice(0, 5).map((search) => (\n                <div key={search.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                      {search.query}\n                    </span>\n                    <span className={`text-xs ${getQualityColor(search.quality)}`}>\n                      {search.quality.toFixed(0)}%\n                    </span>\n                  </div>\n                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                    {search.results.length} résultats • {search.sources.join(', ')}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Sources Surveillées */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n              🌐 Sources Surveillées\n            </h3>\n            <div className=\"space-y-3\">\n              {sourcesMonitored.length === 0 ? (\n                // Sources simulées\n                [\n                  { id: '1', name: 'Google Search', status: 'active', responseTime: 120, reliability: 98 },\n                  { id: '2', name: 'Bing Search', status: 'active', responseTime: 150, reliability: 95 },\n                  { id: '3', name: 'DuckDuckGo', status: 'active', responseTime: 180, reliability: 92 },\n                  { id: '4', name: 'Reddit API', status: 'inactive', responseTime: 0, reliability: 0 },\n                  { id: '5', name: 'News API', status: 'active', responseTime: 200, reliability: 89 }\n                ].map(source => (\n                  <div key={source.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <Globe size={16} className=\"text-blue-400\" />\n                        <div>\n                          <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                            {source.name}\n                          </span>\n                          <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                            {source.responseTime}ms • {source.reliability}% fiabilité\n                          </div>\n                        </div>\n                      </div>\n                      {getStatusIcon(source.status)}\n                    </div>\n                  </div>\n                ))\n              ) : (\n                sourcesMonitored.map(source => (\n                  <div key={source.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <Globe size={16} className=\"text-blue-400\" />\n                        <div>\n                          <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                            {source.name}\n                          </span>\n                          <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                            {source.responseTime}ms • {source.reliability}% fiabilité\n                          </div>\n                        </div>\n                      </div>\n                      {getStatusIcon(source.status)}\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Signaux Faibles Détectés */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n              📡 Signaux Faibles\n            </h3>\n            <div className=\"space-y-3\">\n              {weakSignals.length === 0 ? (\n                // Signaux simulés\n                [\n                  { id: '1', description: 'Augmentation recherches IA', confidence: 85, category: 'trend', source: 'Google Trends' },\n                  { id: '2', description: 'Anomalie trafic API', confidence: 72, category: 'anomaly', source: 'Monitoring' },\n                  { id: '3', description: 'Opportunité marché émergent', confidence: 68, category: 'opportunity', source: 'News Analysis' }\n                ].map(signal => (\n                  <div key={signal.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                    <div className=\"flex items-center space-x-3 mb-2\">\n                      {getCategoryIcon(signal.category)}\n                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                        {signal.description}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                        {signal.source}\n                      </span>\n                      <span className={`text-xs font-medium ${\n                        signal.confidence > 80 ? 'text-green-400' :\n                        signal.confidence > 60 ? 'text-yellow-400' : 'text-red-400'\n                      }`}>\n                        {signal.confidence}% confiance\n                      </span>\n                    </div>\n                  </div>\n                ))\n              ) : (\n                weakSignals.map(signal => (\n                  <div key={signal.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                    <div className=\"flex items-center space-x-3 mb-2\">\n                      {getCategoryIcon(signal.category)}\n                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                        {signal.description}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                        {signal.source}\n                      </span>\n                      <span className={`text-xs font-medium ${\n                        signal.confidence > 80 ? 'text-green-400' :\n                        signal.confidence > 60 ? 'text-yellow-400' : 'text-red-400'\n                      }`}>\n                        {signal.confidence}% confiance\n                      </span>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HanumanVisionInterface;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAqCA,MAAM,yBAAyB,CAAC,EAAE,WAAW,IAAI,EAAE;;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,eAAe;QACf,mBAAmB;QACnB,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEvC,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;8EAA4B;oBAChC,IAAI;wBACF,yDAAyD;wBACzD,MAAM,OAAO,GAAG,IAAI,UAAU;wBAE9B,MAAM,OAAO,CAAC,MAAM;0FAAG;gCACrB,QAAQ,GAAG,CAAC;gCACZ,eAAe;gCAEf,6BAA6B;gCAC7B,MAAM,OAAO,EAAE,KAAK,KAAK,SAAS,CAAC;oCACjC,MAAM;oCACN,WAAW,KAAK,GAAG;gCACrB;4BACF;;wBAEA,MAAM,OAAO,CAAC,SAAS;0FAAG,CAAC;gCACzB,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gCAClC,yBAAyB;4BAC3B;;wBAEA,MAAM,OAAO,CAAC,OAAO;0FAAG;gCACtB,QAAQ,GAAG,CAAC;gCACZ,eAAe;gCAEf,4CAA4C;gCAC5C,WAAW,2BAA2B;4BACxC;;wBAEA,MAAM,OAAO,CAAC,OAAO;0FAAG,CAAC;gCACvB,QAAQ,KAAK,CAAC,wBAAwB;gCACtC,eAAe;4BACjB;;oBAEF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,eAAe;oBACjB;gBACF;;YAEA;YAEA,yDAAyD;YACzD,MAAM,qBAAqB;uEAAY;oBACrC,IAAI,CAAC,aAAa;wBAChB;oBACF;gBACF;sEAAG;YAEH;oDAAO;oBACL,cAAc;oBACd,MAAM,OAAO,EAAE;gBACjB;;QACF;2CAAG,EAAE;IAEL,MAAM,2BAA2B,CAAC;QAChC,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM,KAAK,KAAK;qBAAC;gBAC9C;YAEF,KAAK;gBACH,eAAe,CAAA,OAAQ;wBAAC,KAAK,MAAM;2BAAK,KAAK,KAAK,CAAC,GAAG;qBAAI;gBAC1D,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,KAAK,KAAK;gBAC1D,kBAAkB,KAAK,MAAM,CAAC,OAAO;gBACrC;YAEF,KAAK;gBACH,oBAAoB,KAAK,OAAO;gBAChC;YAEF,KAAK;gBACH,eAAe,CAAA,OAAQ;wBAAC,KAAK,MAAM;2BAAK,KAAK,KAAK,CAAC,GAAG;qBAAG;gBACzD;YAEF,KAAK;gBACH,iBAAiB,KAAK,OAAO;gBAC7B;YAEF;gBACE,QAAQ,GAAG,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,yBAAyB;QAC7B,qCAAqC;QACrC,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,cAAc,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;QAEvE,wBAAwB;QACxB,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAE/C,WAAW;YACT,MAAM,aAA8B;gBAClC,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;gBAC1B,OAAO;gBACP,SAAS;oBACP;wBACE,OAAO,CAAC,YAAY,EAAE,aAAa;wBACnC,KAAK;wBACL,SAAS;wBACT,WAAW,KAAK,MAAM,KAAK;wBAC3B,WAAW,IAAI;oBACjB;iBACD;gBACD,SAAS,KAAK,KAAK,MAAM,KAAK;gBAC9B,SAAS;oBAAC;oBAAO;iBAAO;gBACxB,UAAU,CAAC;YACb;YAEA,eAAe,CAAA,OAAQ;oBAAC;uBAAe,KAAK,KAAK,CAAC,GAAG;iBAAI;YACzD,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM;YAChD,kBAAkB,WAAW,OAAO;QACtC,GAAG;IACL;IAEA,MAAM,oBAAoB,CAAC;QACzB,eAAe,CAAA,OAAS,OAAO,MAAM,aAAa;IACpD;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,UAAU,IAAI,OAAO;QACzB,IAAI,UAAU,IAAI,OAAO;QACzB,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACpE,KAAK;gBAAY,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACvE,KAAK;gBAAS,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjE;gBAAS,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QAC/D;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAS,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;oBAAgB,MAAM;;;;;;YACjE,KAAK;gBAAW,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACtE,KAAK;gBAAe,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACzE,KAAK;gBAAU,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;oBAAe,MAAM;;;;;;YAClE;gBAAS,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QAC5D;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,4CAA4C,EAAE,WAAW,qBAAqB,cAAc;kBAC3G,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;wCAAa,MAAM;;;;;;;;;;;8CAEpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAW,CAAC,mBAAmB,EAAE,WAAW,eAAe,iBAAiB;sDAAE;;;;;;sDAGlF,6LAAC;4CAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;;;;;;;sCAM7E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,CAAC,iDAAiD,EAChE,cACI,sEACA,6DACJ;;oCACC,4BAAc,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;6DAAS,6LAAC,+MAAA,CAAA,UAAO;wCAAC,MAAM;;;;;;kDACnD,6LAAC;wCAAK,WAAU;kDACb,cAAc,aAAa;;;;;;;;;;;;;;;;;;;;;;;8BAOpC,6LAAC;oBAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;sCACvF,6LAAC;4BAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;sCAAE;;;;;;sCAGtF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,cAAc,aAAa,CAAC,cAAc;;;;;;sDAE7C,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,cAAc,iBAAiB;;;;;;sDAElC,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,mBAAmB,EAAE,gBAAgB,cAAc;;gDACjE,YAAY,OAAO,CAAC;gDAAG;;;;;;;sDAE1B,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,cAAc,YAAY;gDAAC;;;;;;;sDAE9B,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,cAAc,WAAW;gDAAC;;;;;;;sDAE7B,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAOjF,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;8CAClF,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;8CAAE;;;;;;gCAIrF,cAAc,MAAM,GAAG,mBACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,CAAC,yBAAyB,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;wCAG1F,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;gDAAgB,WAAW,CAAC,wCAAwC,EAAE,WAAW,gBAAgB,cAAc,KAAK,CAAC;;kEACpH,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;wDAA6B,MAAM;;;;;;kEACrD,6LAAC;wDAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,eAAe,iBAAiB;kEAAG;;;;;;;+CAFlE;;;;;;;;;;;8CAQhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,CAAC,oBAAoB,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;wCAGrF,YAAY,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBAC5B,6LAAC;gDAAoB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;;kEAC1F,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;0EAChF,OAAO,KAAK;;;;;;0EAEf,6LAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,gBAAgB,OAAO,OAAO,GAAG;;oEAC1D,OAAO,OAAO,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAG/B,6LAAC;wDAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;;4DACtE,OAAO,OAAO,CAAC,MAAM;4DAAC;4DAAc,OAAO,OAAO,CAAC,IAAI,CAAC;;;;;;;;+CAVnD,OAAO,EAAE;;;;;;;;;;;;;;;;;sCAkBzB,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;8CAClF,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;8CAAE;;;;;;8CAGtF,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,MAAM,KAAK,IAC3B,mBAAmB;oCACnB;wCACE;4CAAE,IAAI;4CAAK,MAAM;4CAAiB,QAAQ;4CAAU,cAAc;4CAAK,aAAa;wCAAG;wCACvF;4CAAE,IAAI;4CAAK,MAAM;4CAAe,QAAQ;4CAAU,cAAc;4CAAK,aAAa;wCAAG;wCACrF;4CAAE,IAAI;4CAAK,MAAM;4CAAc,QAAQ;4CAAU,cAAc;4CAAK,aAAa;wCAAG;wCACpF;4CAAE,IAAI;4CAAK,MAAM;4CAAc,QAAQ;4CAAY,cAAc;4CAAG,aAAa;wCAAE;wCACnF;4CAAE,IAAI;4CAAK,MAAM;4CAAY,QAAQ;4CAAU,cAAc;4CAAK,aAAa;wCAAG;qCACnF,CAAC,GAAG,CAAC,CAAA,uBACJ,6LAAC;4CAAoB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;sDAC1F,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,MAAM;gEAAI,WAAU;;;;;;0EAC3B,6LAAC;;kFACC,6LAAC;wEAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;kFAChF,OAAO,IAAI;;;;;;kFAEd,6LAAC;wEAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;;4EACtE,OAAO,YAAY;4EAAC;4EAAM,OAAO,WAAW;4EAAC;;;;;;;;;;;;;;;;;;;oDAInD,cAAc,OAAO,MAAM;;;;;;;2CAbtB,OAAO,EAAE;;;;oDAkBrB,iBAAiB,GAAG,CAAC,CAAA,uBACnB,6LAAC;4CAAoB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;sDAC1F,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,MAAM;gEAAI,WAAU;;;;;;0EAC3B,6LAAC;;kFACC,6LAAC;wEAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;kFAChF,OAAO,IAAI;;;;;;kFAEd,6LAAC;wEAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;;4EACtE,OAAO,YAAY;4EAAC;4EAAM,OAAO,WAAW;4EAAC;;;;;;;;;;;;;;;;;;;oDAInD,cAAc,OAAO,MAAM;;;;;;;2CAbtB,OAAO,EAAE;;;;;;;;;;;;;;;;sCAsB3B,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;8CAClF,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;8CAAE;;;;;;8CAGtF,6LAAC;oCAAI,WAAU;8CACZ,YAAY,MAAM,KAAK,IACtB,kBAAkB;oCAClB;wCACE;4CAAE,IAAI;4CAAK,aAAa;4CAA8B,YAAY;4CAAI,UAAU;4CAAS,QAAQ;wCAAgB;wCACjH;4CAAE,IAAI;4CAAK,aAAa;4CAAuB,YAAY;4CAAI,UAAU;4CAAW,QAAQ;wCAAa;wCACzG;4CAAE,IAAI;4CAAK,aAAa;4CAA+B,YAAY;4CAAI,UAAU;4CAAe,QAAQ;wCAAgB;qCACzH,CAAC,GAAG,CAAC,CAAA,uBACJ,6LAAC;4CAAoB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;;8DAC1F,6LAAC;oDAAI,WAAU;;wDACZ,gBAAgB,OAAO,QAAQ;sEAChC,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;sEAChF,OAAO,WAAW;;;;;;;;;;;;8DAGvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sEACvE,OAAO,MAAM;;;;;;sEAEhB,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,UAAU,GAAG,KAAK,mBACzB,OAAO,UAAU,GAAG,KAAK,oBAAoB,gBAC7C;;gEACC,OAAO,UAAU;gEAAC;;;;;;;;;;;;;;2CAff,OAAO,EAAE;;;;oDAqBrB,YAAY,GAAG,CAAC,CAAA,uBACd,6LAAC;4CAAoB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;;8DAC1F,6LAAC;oDAAI,WAAU;;wDACZ,gBAAgB,OAAO,QAAQ;sEAChC,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;sEAChF,OAAO,WAAW;;;;;;;;;;;;8DAGvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sEACvE,OAAO,MAAM;;;;;;sEAEhB,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,UAAU,GAAG,KAAK,mBACzB,OAAO,UAAU,GAAG,KAAK,oBAAoB,gBAC7C;;gEACC,OAAO,UAAU;gEAAC;;;;;;;;;;;;;;2CAff,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BrC;GAlaM;KAAA;uCAoaS", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Agentic-Coding-Framework-%20RB2/hanuman-final/hanuman_hearing_interface.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Ear, Radio, Waves, Volume2, VolumeX, Activity, Zap, AlertTriangle, CheckCircle, Database, TrendingUp, BarChart3, Wifi, WifiOff } from 'lucide-react';\n\n// Interfaces pour l'écoute des données\ninterface DataStream {\n  id: string;\n  name: string;\n  source: string;\n  intensity: number;\n  frequency: number;\n  status: 'active' | 'inactive' | 'error';\n  dataRate: number;\n  lastUpdate: Date;\n  type: 'kafka' | 'redis' | 'websocket' | 'api' | 'webhook';\n}\n\ninterface WeakSignal {\n  id: string;\n  description: string;\n  confidence: number;\n  source: string;\n  timestamp: Date;\n  category: 'anomaly' | 'pattern' | 'trend' | 'alert';\n  severity: 'low' | 'medium' | 'high' | 'critical';\n}\n\ninterface APIHealth {\n  endpoint: string;\n  status: 'healthy' | 'degraded' | 'down';\n  latency: number;\n  uptime: number;\n  errorRate: number;\n  lastCheck: Date;\n}\n\ninterface HearingMetrics {\n  totalStreams: number;\n  activeStreams: number;\n  dataProcessed: number;\n  averageLatency: number;\n  signalsDetected: number;\n  alertsTriggered: number;\n}\n\nconst HanumanHearingInterface = ({ darkMode = true }) => {\n  const [dataStreams, setDataStreams] = useState<DataStream[]>([]);\n  const [weakSignals, setWeakSignals] = useState<WeakSignal[]>([]);\n  const [apiHealth, setApiHealth] = useState<APIHealth[]>([]);\n  const [hearingMetrics, setHearingMetrics] = useState<HearingMetrics>({\n    totalStreams: 15,\n    activeStreams: 12,\n    dataProcessed: 2847,\n    averageLatency: 89,\n    signalsDetected: 23,\n    alertsTriggered: 3\n  });\n  const [isConnected, setIsConnected] = useState(false);\n  const [audioVisualization, setAudioVisualization] = useState<number[]>(new Array(20).fill(0));\n  const wsRef = useRef<WebSocket | null>(null);\n\n  // Connexion aux services de collecte de données\n  useEffect(() => {\n    const connectToDataServices = () => {\n      try {\n        // Connexion WebSocket pour l'écoute en temps réel\n        wsRef.current = new WebSocket('ws://localhost:3002/hearing');\n        \n        wsRef.current.onopen = () => {\n          console.log('🔗 Connexion établie avec les services de données');\n          setIsConnected(true);\n          \n          // Demander le statut initial\n          wsRef.current?.send(JSON.stringify({\n            type: 'GET_STREAMS_STATUS',\n            timestamp: Date.now()\n          }));\n        };\n\n        wsRef.current.onmessage = (event) => {\n          const data = JSON.parse(event.data);\n          handleDataMessage(data);\n        };\n\n        wsRef.current.onclose = () => {\n          console.log('❌ Connexion fermée avec les services de données');\n          setIsConnected(false);\n          \n          // Tentative de reconnexion\n          setTimeout(connectToDataServices, 5000);\n        };\n\n        wsRef.current.onerror = (error) => {\n          console.error('🚨 Erreur WebSocket:', error);\n          setIsConnected(false);\n        };\n\n      } catch (error) {\n        console.error('🚨 Erreur de connexion:', error);\n        setIsConnected(false);\n      }\n    };\n\n    connectToDataServices();\n\n    // Simulation de données en attendant la connexion réelle\n    const simulationInterval = setInterval(() => {\n      if (!isConnected) {\n        simulateHearingActivity();\n      }\n    }, 2000);\n\n    // Mise à jour de la visualisation audio\n    const visualizationInterval = setInterval(() => {\n      updateAudioVisualization();\n    }, 100);\n\n    return () => {\n      clearInterval(simulationInterval);\n      clearInterval(visualizationInterval);\n      wsRef.current?.close();\n    };\n  }, []);\n\n  const handleDataMessage = (data: any) => {\n    switch (data.type) {\n      case 'STREAM_DATA':\n        updateDataStream(data.stream);\n        break;\n        \n      case 'WEAK_SIGNAL_DETECTED':\n        setWeakSignals(prev => [data.signal, ...prev.slice(0, 9)]);\n        break;\n        \n      case 'API_HEALTH_UPDATE':\n        setApiHealth(data.healthData);\n        break;\n        \n      case 'METRICS_UPDATE':\n        setHearingMetrics(data.metrics);\n        break;\n        \n      case 'STREAMS_STATUS':\n        setDataStreams(data.streams);\n        break;\n        \n      default:\n        console.log('📨 Message non géré:', data);\n    }\n  };\n\n  const simulateHearingActivity = () => {\n    // Simulation des flux de données\n    const mockStreams: DataStream[] = [\n      {\n        id: 'kafka-events',\n        name: 'Kafka Events',\n        source: 'kafka:9092',\n        intensity: 0.3 + Math.random() * 0.7,\n        frequency: 50 + Math.random() * 100,\n        status: 'active',\n        dataRate: 1200 + Math.random() * 800,\n        lastUpdate: new Date(),\n        type: 'kafka'\n      },\n      {\n        id: 'redis-cache',\n        name: 'Redis Cache',\n        source: 'redis:6379',\n        intensity: 0.2 + Math.random() * 0.6,\n        frequency: 30 + Math.random() * 70,\n        status: 'active',\n        dataRate: 800 + Math.random() * 600,\n        lastUpdate: new Date(),\n        type: 'redis'\n      },\n      {\n        id: 'api-webhooks',\n        name: 'API Webhooks',\n        source: 'webhooks.api',\n        intensity: 0.1 + Math.random() * 0.4,\n        frequency: 10 + Math.random() * 40,\n        status: Math.random() > 0.8 ? 'error' : 'active',\n        dataRate: 200 + Math.random() * 300,\n        lastUpdate: new Date(),\n        type: 'webhook'\n      },\n      {\n        id: 'websocket-live',\n        name: 'WebSocket Live',\n        source: 'ws://live-data',\n        intensity: 0.4 + Math.random() * 0.6,\n        frequency: 80 + Math.random() * 120,\n        status: 'active',\n        dataRate: 1500 + Math.random() * 1000,\n        lastUpdate: new Date(),\n        type: 'websocket'\n      }\n    ];\n\n    setDataStreams(mockStreams);\n\n    // Simulation des signaux faibles\n    if (Math.random() > 0.7) {\n      const categories = ['anomaly', 'pattern', 'trend', 'alert'] as const;\n      const severities = ['low', 'medium', 'high', 'critical'] as const;\n      \n      const newSignal: WeakSignal = {\n        id: `signal_${Date.now()}`,\n        description: `Signal détecté dans ${mockStreams[Math.floor(Math.random() * mockStreams.length)].name}`,\n        confidence: 60 + Math.random() * 40,\n        source: 'Pattern Analysis Engine',\n        timestamp: new Date(),\n        category: categories[Math.floor(Math.random() * categories.length)],\n        severity: severities[Math.floor(Math.random() * severities.length)]\n      };\n      \n      setWeakSignals(prev => [newSignal, ...prev.slice(0, 9)]);\n    }\n\n    // Simulation de la santé des APIs\n    const mockAPIHealth: APIHealth[] = [\n      {\n        endpoint: '/api/agents/status',\n        status: Math.random() > 0.9 ? 'degraded' : 'healthy',\n        latency: 50 + Math.random() * 100,\n        uptime: 99.5 + Math.random() * 0.5,\n        errorRate: Math.random() * 2,\n        lastCheck: new Date()\n      },\n      {\n        endpoint: '/api/data/stream',\n        status: Math.random() > 0.95 ? 'down' : 'healthy',\n        latency: 80 + Math.random() * 120,\n        uptime: 98.8 + Math.random() * 1.2,\n        errorRate: Math.random() * 3,\n        lastCheck: new Date()\n      },\n      {\n        endpoint: '/api/monitoring/metrics',\n        status: 'healthy',\n        latency: 30 + Math.random() * 50,\n        uptime: 99.9,\n        errorRate: Math.random() * 0.5,\n        lastCheck: new Date()\n      }\n    ];\n\n    setApiHealth(mockAPIHealth);\n  };\n\n  const updateDataStream = (streamData: DataStream) => {\n    setDataStreams(prev => {\n      const index = prev.findIndex(s => s.id === streamData.id);\n      if (index >= 0) {\n        const updated = [...prev];\n        updated[index] = streamData;\n        return updated;\n      }\n      return [streamData, ...prev];\n    });\n  };\n\n  const updateAudioVisualization = () => {\n    setAudioVisualization(prev => {\n      const newVisualization = prev.map((_, index) => {\n        const baseIntensity = dataStreams.reduce((sum, stream) => \n          sum + (stream.status === 'active' ? stream.intensity : 0), 0) / Math.max(dataStreams.length, 1);\n        \n        return Math.max(0, Math.min(1, baseIntensity + (Math.random() - 0.5) * 0.3));\n      });\n      return newVisualization;\n    });\n  };\n\n  const getStreamTypeIcon = (type: string) => {\n    switch (type) {\n      case 'kafka': return <Database className=\"text-purple-400\" size={16} />;\n      case 'redis': return <Zap className=\"text-red-400\" size={16} />;\n      case 'websocket': return <Activity className=\"text-blue-400\" size={16} />;\n      case 'api': return <Radio className=\"text-green-400\" size={16} />;\n      case 'webhook': return <Waves className=\"text-orange-400\" size={16} />;\n      default: return <Radio className=\"text-gray-400\" size={16} />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active': case 'healthy': return 'text-green-400';\n      case 'degraded': return 'text-yellow-400';\n      case 'inactive': case 'down': case 'error': return 'text-red-400';\n      default: return 'text-gray-400';\n    }\n  };\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'low': return 'text-blue-400';\n      case 'medium': return 'text-yellow-400';\n      case 'high': return 'text-orange-400';\n      case 'critical': return 'text-red-400';\n      default: return 'text-gray-400';\n    }\n  };\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'anomaly': return <AlertTriangle className=\"text-orange-400\" size={16} />;\n      case 'pattern': return <BarChart3 className=\"text-blue-400\" size={16} />;\n      case 'trend': return <TrendingUp className=\"text-green-400\" size={16} />;\n      case 'alert': return <AlertTriangle className=\"text-red-400\" size={16} />;\n      default: return <Activity className=\"text-gray-400\" size={16} />;\n    }\n  };\n\n  return (\n    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>\n      <div className=\"container mx-auto p-6\">\n        \n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center\">\n              <Ear className=\"text-white\" size={24} />\n            </div>\n            <div>\n              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                Ouïe d'Hanuman\n              </h1>\n              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n                Organe d'Écoute • Collecte de Données Temps Réel\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-3\">\n            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${\n              isConnected \n                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' \n                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'\n            }`}>\n              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}\n              <span className=\"text-sm font-medium\">\n                {isConnected ? 'Écoute Active' : 'Hors Ligne'}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Métriques Globales */}\n        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n            Métriques d'Écoute\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-6 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-400\">\n                {hearingMetrics.totalStreams}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Flux Totaux\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-green-400\">\n                {hearingMetrics.activeStreams}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Flux Actifs\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-purple-400\">\n                {hearingMetrics.dataProcessed.toLocaleString()}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Données Traitées\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-orange-400\">\n                {hearingMetrics.averageLatency}ms\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Latence Moyenne\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">\n                {hearingMetrics.signalsDetected}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Signaux Détectés\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-red-400\">\n                {hearingMetrics.alertsTriggered}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Alertes Déclenchées\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n          \n          {/* Visualisation Audio */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n              👂 Écoute Active\n            </h3>\n            <div className=\"flex items-end justify-center space-x-1 h-32 mb-4\">\n              {audioVisualization.map((intensity, index) => (\n                <div\n                  key={index}\n                  className=\"bg-gradient-to-t from-blue-500 to-purple-500 rounded-t transition-all duration-100\"\n                  style={{\n                    height: `${Math.max(4, intensity * 100)}%`,\n                    width: '12px'\n                  }}\n                />\n              ))}\n            </div>\n            <div className=\"text-center\">\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Intensité des flux de données en temps réel\n              </div>\n            </div>\n          </div>\n\n          {/* Flux de Données */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n              📊 Flux de Données\n            </h3>\n            <div className=\"space-y-3\">\n              {dataStreams.map((stream) => (\n                <div key={stream.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center space-x-3\">\n                      {getStreamTypeIcon(stream.type)}\n                      <div>\n                        <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                          {stream.name}\n                        </span>\n                        <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                          {stream.source}\n                        </div>\n                      </div>\n                    </div>\n                    <div className={`text-xs font-medium ${getStatusColor(stream.status)}`}>\n                      {stream.status}\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between text-xs\">\n                    <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>\n                      {stream.dataRate.toFixed(0)} msg/s\n                    </span>\n                    <div className=\"w-16 bg-gray-300 dark:bg-gray-600 rounded-full h-1\">\n                      <div \n                        className=\"h-1 rounded-full bg-gradient-to-r from-blue-500 to-purple-500\"\n                        style={{ width: `${stream.intensity * 100}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Signaux Faibles */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n              📡 Signaux Faibles\n            </h3>\n            <div className=\"space-y-3\">\n              {weakSignals.map((signal) => (\n                <div key={signal.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    {getCategoryIcon(signal.category)}\n                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                      {signal.description}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                        {signal.source}\n                      </span>\n                      <span className={`text-xs px-2 py-1 rounded ${getSeverityColor(signal.severity)} bg-opacity-20`}>\n                        {signal.severity}\n                      </span>\n                    </div>\n                    <span className={`text-xs font-medium ${\n                      signal.confidence > 80 ? 'text-green-400' :\n                      signal.confidence > 60 ? 'text-yellow-400' : 'text-red-400'\n                    }`}>\n                      {signal.confidence.toFixed(0)}%\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Santé des APIs */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-2`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n              🔗 Santé des APIs\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {apiHealth.map((api, index) => (\n                <div key={index} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                      {api.endpoint}\n                    </span>\n                    <div className=\"flex items-center space-x-2\">\n                      {api.status === 'healthy' ? <Volume2 className=\"text-green-400\" size={16} /> : \n                       api.status === 'degraded' ? <Volume2 className=\"text-yellow-400\" size={16} /> :\n                       <VolumeX className=\"text-red-400\" size={16} />}\n                      <span className={`text-xs font-medium ${getStatusColor(api.status)}`}>\n                        {api.status}\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-3 gap-2 text-xs\">\n                    <div>\n                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Latence:</span>\n                      <div className=\"font-medium\">{api.latency.toFixed(0)}ms</div>\n                    </div>\n                    <div>\n                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Uptime:</span>\n                      <div className=\"font-medium\">{api.uptime.toFixed(1)}%</div>\n                    </div>\n                    <div>\n                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Erreurs:</span>\n                      <div className=\"font-medium\">{api.errorRate.toFixed(1)}%</div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HanumanHearingInterface;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AA2CA,MAAM,0BAA0B,CAAC,EAAE,WAAW,IAAI,EAAE;;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACnE,cAAc;QACd,eAAe;QACf,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;IACnB;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,IAAI,MAAM,IAAI,IAAI,CAAC;IAC1F,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEvC,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM;2EAAwB;oBAC5B,IAAI;wBACF,kDAAkD;wBAClD,MAAM,OAAO,GAAG,IAAI,UAAU;wBAE9B,MAAM,OAAO,CAAC,MAAM;uFAAG;gCACrB,QAAQ,GAAG,CAAC;gCACZ,eAAe;gCAEf,6BAA6B;gCAC7B,MAAM,OAAO,EAAE,KAAK,KAAK,SAAS,CAAC;oCACjC,MAAM;oCACN,WAAW,KAAK,GAAG;gCACrB;4BACF;;wBAEA,MAAM,OAAO,CAAC,SAAS;uFAAG,CAAC;gCACzB,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gCAClC,kBAAkB;4BACpB;;wBAEA,MAAM,OAAO,CAAC,OAAO;uFAAG;gCACtB,QAAQ,GAAG,CAAC;gCACZ,eAAe;gCAEf,2BAA2B;gCAC3B,WAAW,uBAAuB;4BACpC;;wBAEA,MAAM,OAAO,CAAC,OAAO;uFAAG,CAAC;gCACvB,QAAQ,KAAK,CAAC,wBAAwB;gCACtC,eAAe;4BACjB;;oBAEF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,eAAe;oBACjB;gBACF;;YAEA;YAEA,yDAAyD;YACzD,MAAM,qBAAqB;wEAAY;oBACrC,IAAI,CAAC,aAAa;wBAChB;oBACF;gBACF;uEAAG;YAEH,wCAAwC;YACxC,MAAM,wBAAwB;2EAAY;oBACxC;gBACF;0EAAG;YAEH;qDAAO;oBACL,cAAc;oBACd,cAAc;oBACd,MAAM,OAAO,EAAE;gBACjB;;QACF;4CAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,iBAAiB,KAAK,MAAM;gBAC5B;YAEF,KAAK;gBACH,eAAe,CAAA,OAAQ;wBAAC,KAAK,MAAM;2BAAK,KAAK,KAAK,CAAC,GAAG;qBAAG;gBACzD;YAEF,KAAK;gBACH,aAAa,KAAK,UAAU;gBAC5B;YAEF,KAAK;gBACH,kBAAkB,KAAK,OAAO;gBAC9B;YAEF,KAAK;gBACH,eAAe,KAAK,OAAO;gBAC3B;YAEF;gBACE,QAAQ,GAAG,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,0BAA0B;QAC9B,iCAAiC;QACjC,MAAM,cAA4B;YAChC;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;gBACR,WAAW,MAAM,KAAK,MAAM,KAAK;gBACjC,WAAW,KAAK,KAAK,MAAM,KAAK;gBAChC,QAAQ;gBACR,UAAU,OAAO,KAAK,MAAM,KAAK;gBACjC,YAAY,IAAI;gBAChB,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;gBACR,WAAW,MAAM,KAAK,MAAM,KAAK;gBACjC,WAAW,KAAK,KAAK,MAAM,KAAK;gBAChC,QAAQ;gBACR,UAAU,MAAM,KAAK,MAAM,KAAK;gBAChC,YAAY,IAAI;gBAChB,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;gBACR,WAAW,MAAM,KAAK,MAAM,KAAK;gBACjC,WAAW,KAAK,KAAK,MAAM,KAAK;gBAChC,QAAQ,KAAK,MAAM,KAAK,MAAM,UAAU;gBACxC,UAAU,MAAM,KAAK,MAAM,KAAK;gBAChC,YAAY,IAAI;gBAChB,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;gBACR,WAAW,MAAM,KAAK,MAAM,KAAK;gBACjC,WAAW,KAAK,KAAK,MAAM,KAAK;gBAChC,QAAQ;gBACR,UAAU,OAAO,KAAK,MAAM,KAAK;gBACjC,YAAY,IAAI;gBAChB,MAAM;YACR;SACD;QAED,eAAe;QAEf,iCAAiC;QACjC,IAAI,KAAK,MAAM,KAAK,KAAK;YACvB,MAAM,aAAa;gBAAC;gBAAW;gBAAW;gBAAS;aAAQ;YAC3D,MAAM,aAAa;gBAAC;gBAAO;gBAAU;gBAAQ;aAAW;YAExD,MAAM,YAAwB;gBAC5B,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;gBAC1B,aAAa,CAAC,oBAAoB,EAAE,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE,CAAC,IAAI,EAAE;gBACtG,YAAY,KAAK,KAAK,MAAM,KAAK;gBACjC,QAAQ;gBACR,WAAW,IAAI;gBACf,UAAU,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;gBACnE,UAAU,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;YACrE;YAEA,eAAe,CAAA,OAAQ;oBAAC;uBAAc,KAAK,KAAK,CAAC,GAAG;iBAAG;QACzD;QAEA,kCAAkC;QAClC,MAAM,gBAA6B;YACjC;gBACE,UAAU;gBACV,QAAQ,KAAK,MAAM,KAAK,MAAM,aAAa;gBAC3C,SAAS,KAAK,KAAK,MAAM,KAAK;gBAC9B,QAAQ,OAAO,KAAK,MAAM,KAAK;gBAC/B,WAAW,KAAK,MAAM,KAAK;gBAC3B,WAAW,IAAI;YACjB;YACA;gBACE,UAAU;gBACV,QAAQ,KAAK,MAAM,KAAK,OAAO,SAAS;gBACxC,SAAS,KAAK,KAAK,MAAM,KAAK;gBAC9B,QAAQ,OAAO,KAAK,MAAM,KAAK;gBAC/B,WAAW,KAAK,MAAM,KAAK;gBAC3B,WAAW,IAAI;YACjB;YACA;gBACE,UAAU;gBACV,QAAQ;gBACR,SAAS,KAAK,KAAK,MAAM,KAAK;gBAC9B,QAAQ;gBACR,WAAW,KAAK,MAAM,KAAK;gBAC3B,WAAW,IAAI;YACjB;SACD;QAED,aAAa;IACf;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,CAAA;YACb,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;YACxD,IAAI,SAAS,GAAG;gBACd,MAAM,UAAU;uBAAI;iBAAK;gBACzB,OAAO,CAAC,MAAM,GAAG;gBACjB,OAAO;YACT;YACA,OAAO;gBAAC;mBAAe;aAAK;QAC9B;IACF;IAEA,MAAM,2BAA2B;QAC/B,sBAAsB,CAAA;YACpB,MAAM,mBAAmB,KAAK,GAAG,CAAC,CAAC,GAAG;gBACpC,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAC,KAAK,SAC7C,MAAM,CAAC,OAAO,MAAM,KAAK,WAAW,OAAO,SAAS,GAAG,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,YAAY,MAAM,EAAE;gBAE/F,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,gBAAgB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACzE;YACA,OAAO;QACT;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAS,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACjE,KAAK;gBAAS,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACzD,KAAK;gBAAa,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;oBAAgB,MAAM;;;;;;YACnE,KAAK;gBAAO,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YAC3D,KAAK;gBAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAChE;gBAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACzD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YAAU,KAAK;gBAAW,OAAO;YACtC,KAAK;gBAAY,OAAO;YACxB,KAAK;YAAY,KAAK;YAAQ,KAAK;gBAAS,OAAO;YACnD;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAW,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACxE,KAAK;gBAAW,qBAAO,6LAAC,qNAAA,CAAA,YAAS;oBAAC,WAAU;oBAAgB,MAAM;;;;;;YAClE,KAAK;gBAAS,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YAClE,KAAK;gBAAS,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACnE;gBAAS,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QAC5D;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,4CAA4C,EAAE,WAAW,qBAAqB,cAAc;kBAC3G,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;wCAAa,MAAM;;;;;;;;;;;8CAEpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAW,CAAC,mBAAmB,EAAE,WAAW,eAAe,iBAAiB;sDAAE;;;;;;sDAGlF,6LAAC;4CAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;;;;;;;sCAM7E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,CAAC,iDAAiD,EAChE,cACI,sEACA,6DACJ;;oCACC,4BAAc,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;6DAAS,6LAAC,+MAAA,CAAA,UAAO;wCAAC,MAAM;;;;;;kDACnD,6LAAC;wCAAK,WAAU;kDACb,cAAc,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,6LAAC;oBAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;sCACvF,6LAAC;4BAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;sCAAE;;;;;;sCAGtF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,eAAe,YAAY;;;;;;sDAE9B,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,eAAe,aAAa;;;;;;sDAE/B,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,eAAe,aAAa,CAAC,cAAc;;;;;;sDAE9C,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,eAAe,cAAc;gDAAC;;;;;;;sDAEjC,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,eAAe,eAAe;;;;;;sDAEjC,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,eAAe,eAAe;;;;;;sDAEjC,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAOjF,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;8CAClF,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;8CAAE;;;;;;8CAGtF,6LAAC;oCAAI,WAAU;8CACZ,mBAAmB,GAAG,CAAC,CAAC,WAAW,sBAClC,6LAAC;4CAEC,WAAU;4CACV,OAAO;gDACL,QAAQ,GAAG,KAAK,GAAG,CAAC,GAAG,YAAY,KAAK,CAAC,CAAC;gDAC1C,OAAO;4CACT;2CALK;;;;;;;;;;8CASX,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;kDAAE;;;;;;;;;;;;;;;;;sCAO/E,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;8CAClF,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;8CAAE;;;;;;8CAGtF,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;4CAAoB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;;8DAC1F,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,kBAAkB,OAAO,IAAI;8EAC9B,6LAAC;;sFACC,6LAAC;4EAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;sFAChF,OAAO,IAAI;;;;;;sFAEd,6LAAC;4EAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sFACtE,OAAO,MAAM;;;;;;;;;;;;;;;;;;sEAIpB,6LAAC;4DAAI,WAAW,CAAC,oBAAoB,EAAE,eAAe,OAAO,MAAM,GAAG;sEACnE,OAAO,MAAM;;;;;;;;;;;;8DAIlB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,WAAW,kBAAkB;;gEAC3C,OAAO,QAAQ,CAAC,OAAO,CAAC;gEAAG;;;;;;;sEAE9B,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,OAAO,SAAS,GAAG,IAAI,CAAC,CAAC;gEAAC;;;;;;;;;;;;;;;;;;2CAzB3C,OAAO,EAAE;;;;;;;;;;;;;;;;sCAmCzB,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;8CAClF,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;8CAAE;;;;;;8CAGtF,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;4CAAoB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;;8DAC1F,6LAAC;oDAAI,WAAU;;wDACZ,gBAAgB,OAAO,QAAQ;sEAChC,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;sEAChF,OAAO,WAAW;;;;;;;;;;;;8DAGvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;8EACvE,OAAO,MAAM;;;;;;8EAEhB,6LAAC;oEAAK,WAAW,CAAC,0BAA0B,EAAE,iBAAiB,OAAO,QAAQ,EAAE,cAAc,CAAC;8EAC5F,OAAO,QAAQ;;;;;;;;;;;;sEAGpB,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,UAAU,GAAG,KAAK,mBACzB,OAAO,UAAU,GAAG,KAAK,oBAAoB,gBAC7C;;gEACC,OAAO,UAAU,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;2CApB1B,OAAO,EAAE;;;;;;;;;;;;;;;;sCA6BzB,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,wBAAwB,CAAC;;8CAChG,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;8CAAE;;;;;;8CAGtF,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;4CAAgB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;;8DACtF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;sEAChF,IAAI,QAAQ;;;;;;sEAEf,6LAAC;4DAAI,WAAU;;gEACZ,IAAI,MAAM,KAAK,0BAAY,6LAAC,+MAAA,CAAA,UAAO;oEAAC,WAAU;oEAAiB,MAAM;;;;;2EACrE,IAAI,MAAM,KAAK,2BAAa,6LAAC,+MAAA,CAAA,UAAO;oEAAC,WAAU;oEAAkB,MAAM;;;;;yFACvE,6LAAC,+MAAA,CAAA,UAAO;oEAAC,WAAU;oEAAe,MAAM;;;;;;8EACzC,6LAAC;oEAAK,WAAW,CAAC,oBAAoB,EAAE,eAAe,IAAI,MAAM,GAAG;8EACjE,IAAI,MAAM;;;;;;;;;;;;;;;;;;8DAKjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAW,WAAW,kBAAkB;8EAAiB;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;;wEAAe,IAAI,OAAO,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAEvD,6LAAC;;8EACC,6LAAC;oEAAK,WAAW,WAAW,kBAAkB;8EAAiB;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;;wEAAe,IAAI,MAAM,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAEtD,6LAAC;;8EACC,6LAAC;oEAAK,WAAW,WAAW,kBAAkB;8EAAiB;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;;wEAAe,IAAI,SAAS,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;2CA1BnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqC1B;GAhgBM;KAAA;uCAkgBS", "debugId": null}}, {"offset": {"line": 2193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Agentic-Coding-Framework-%20RB2/hanuman-final/hanuman_touch_interface.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Hand, Zap, AlertTriangle, <PERSON>fresh<PERSON><PERSON>, CheckCircle2, Activity, Wifi, WifiOff, Link, Unlink, Timer, TrendingUp, <PERSON><PERSON><PERSON><PERSON>, Settings } from 'lucide-react';\n\n// Interfaces pour les connexions API\ninterface APIConnection {\n  id: string;\n  name: string;\n  endpoint: string;\n  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';\n  status: 'connected' | 'disconnected' | 'error' | 'testing';\n  latency: number;\n  uptime: number;\n  requestCount: number;\n  errorCount: number;\n  successRate: number;\n  lastTest: Date;\n  responseTime: number;\n  headers?: Record<string, string>;\n  authentication?: 'none' | 'bearer' | 'basic' | 'api-key';\n}\n\ninterface RetryOperation {\n  id: string;\n  api: string;\n  endpoint: string;\n  attempt: number;\n  maxAttempts: number;\n  nextRetry: Date;\n  reason: string;\n  priority: 'low' | 'medium' | 'high' | 'critical';\n}\n\ninterface TouchMetrics {\n  totalConnections: number;\n  activeConnections: number;\n  totalRequests: number;\n  successfulRequests: number;\n  failedRequests: number;\n  averageLatency: number;\n  retryQueueSize: number;\n  touchSensitivity: number;\n}\n\ninterface ConnectionTest {\n  connectionId: string;\n  status: 'running' | 'completed' | 'failed';\n  startTime: Date;\n  endTime?: Date;\n  result?: {\n    latency: number;\n    statusCode: number;\n    responseSize: number;\n    error?: string;\n  };\n}\n\nconst HanumanTouchInterface = ({ darkMode = true }) => {\n  const [apiConnections, setApiConnections] = useState<APIConnection[]>([]);\n  const [retryQueue, setRetryQueue] = useState<RetryOperation[]>([]);\n  const [touchSensitivity, setTouchSensitivity] = useState(0.8);\n  const [touchMetrics, setTouchMetrics] = useState<TouchMetrics>({\n    totalConnections: 18,\n    activeConnections: 15,\n    totalRequests: 5847,\n    successfulRequests: 5623,\n    failedRequests: 224,\n    averageLatency: 127,\n    retryQueueSize: 3,\n    touchSensitivity: 0.8\n  });\n  const [isConnected, setIsConnected] = useState(false);\n  const [activeTests, setActiveTests] = useState<ConnectionTest[]>([]);\n  const [connectionHistory, setConnectionHistory] = useState<Array<{timestamp: Date, latency: number, success: boolean}>>([]);\n  const wsRef = useRef<WebSocket | null>(null);\n\n  // Connexion au gestionnaire d'APIs\n  useEffect(() => {\n    const connectToAPIManager = () => {\n      try {\n        // Connexion WebSocket pour le monitoring des APIs\n        wsRef.current = new WebSocket('ws://localhost:3003/touch');\n        \n        wsRef.current.onopen = () => {\n          console.log('🔗 Connexion établie avec le gestionnaire d\\'APIs');\n          setIsConnected(true);\n          \n          // Demander le statut initial\n          wsRef.current?.send(JSON.stringify({\n            type: 'GET_CONNECTIONS_STATUS',\n            timestamp: Date.now()\n          }));\n        };\n\n        wsRef.current.onmessage = (event) => {\n          const data = JSON.parse(event.data);\n          handleAPIMessage(data);\n        };\n\n        wsRef.current.onclose = () => {\n          console.log('❌ Connexion fermée avec le gestionnaire d\\'APIs');\n          setIsConnected(false);\n          \n          // Tentative de reconnexion\n          setTimeout(connectToAPIManager, 5000);\n        };\n\n        wsRef.current.onerror = (error) => {\n          console.error('🚨 Erreur WebSocket:', error);\n          setIsConnected(false);\n        };\n\n      } catch (error) {\n        console.error('🚨 Erreur de connexion:', error);\n        setIsConnected(false);\n      }\n    };\n\n    connectToAPIManager();\n\n    // Simulation de données en attendant la connexion réelle\n    const simulationInterval = setInterval(() => {\n      if (!isConnected) {\n        simulateTouchActivity();\n      }\n    }, 3000);\n\n    // Mise à jour des métriques\n    const metricsInterval = setInterval(() => {\n      updateTouchMetrics();\n    }, 1000);\n\n    return () => {\n      clearInterval(simulationInterval);\n      clearInterval(metricsInterval);\n      wsRef.current?.close();\n    };\n  }, []);\n\n  const handleAPIMessage = (data: any) => {\n    switch (data.type) {\n      case 'CONNECTION_UPDATE':\n        updateAPIConnection(data.connection);\n        break;\n        \n      case 'RETRY_QUEUED':\n        setRetryQueue(prev => [data.retry, ...prev.slice(0, 9)]);\n        break;\n        \n      case 'TEST_STARTED':\n        setActiveTests(prev => [data.test, ...prev]);\n        break;\n        \n      case 'TEST_COMPLETED':\n        completeConnectionTest(data.test);\n        break;\n        \n      case 'CONNECTIONS_STATUS':\n        setApiConnections(data.connections);\n        break;\n        \n      case 'METRICS_UPDATE':\n        setTouchMetrics(data.metrics);\n        break;\n        \n      default:\n        console.log('📨 Message non géré:', data);\n    }\n  };\n\n  const simulateTouchActivity = () => {\n    // Simulation des connexions API\n    const mockConnections: APIConnection[] = [\n      {\n        id: 'agent-frontend',\n        name: 'Agent Frontend',\n        endpoint: 'http://localhost:3001/api',\n        method: 'GET',\n        status: Math.random() > 0.9 ? 'error' : 'connected',\n        latency: 50 + Math.random() * 100,\n        uptime: 98.5 + Math.random() * 1.5,\n        requestCount: 1247 + Math.floor(Math.random() * 100),\n        errorCount: Math.floor(Math.random() * 10),\n        successRate: 95 + Math.random() * 5,\n        lastTest: new Date(),\n        responseTime: 45 + Math.random() * 80,\n        authentication: 'bearer'\n      },\n      {\n        id: 'agent-backend',\n        name: 'Agent Backend',\n        endpoint: 'http://localhost:3002/api',\n        method: 'POST',\n        status: 'connected',\n        latency: 80 + Math.random() * 120,\n        uptime: 99.2,\n        requestCount: 2156 + Math.floor(Math.random() * 150),\n        errorCount: Math.floor(Math.random() * 5),\n        successRate: 97 + Math.random() * 3,\n        lastTest: new Date(),\n        responseTime: 75 + Math.random() * 100,\n        authentication: 'api-key'\n      },\n      {\n        id: 'agent-security',\n        name: 'Agent Security',\n        endpoint: 'http://localhost:3007/api',\n        method: 'GET',\n        status: 'connected',\n        latency: 30 + Math.random() * 60,\n        uptime: 99.8,\n        requestCount: 856 + Math.floor(Math.random() * 80),\n        errorCount: Math.floor(Math.random() * 3),\n        successRate: 99 + Math.random() * 1,\n        lastTest: new Date(),\n        responseTime: 25 + Math.random() * 50,\n        authentication: 'bearer'\n      },\n      {\n        id: 'agent-devops',\n        name: 'Agent DevOps',\n        endpoint: 'http://localhost:3004/api',\n        method: 'PUT',\n        status: Math.random() > 0.95 ? 'disconnected' : 'connected',\n        latency: 100 + Math.random() * 150,\n        uptime: 97.8 + Math.random() * 2,\n        requestCount: 634 + Math.floor(Math.random() * 60),\n        errorCount: Math.floor(Math.random() * 8),\n        successRate: 92 + Math.random() * 6,\n        lastTest: new Date(),\n        responseTime: 95 + Math.random() * 120,\n        authentication: 'basic'\n      },\n      {\n        id: 'weaviate-db',\n        name: 'Weaviate Database',\n        endpoint: 'http://localhost:8080/v1',\n        method: 'GET',\n        status: 'connected',\n        latency: 20 + Math.random() * 40,\n        uptime: 99.9,\n        requestCount: 3247 + Math.floor(Math.random() * 200),\n        errorCount: Math.floor(Math.random() * 2),\n        successRate: 99.5 + Math.random() * 0.5,\n        lastTest: new Date(),\n        responseTime: 15 + Math.random() * 30,\n        authentication: 'api-key'\n      }\n    ];\n\n    setApiConnections(mockConnections);\n\n    // Simulation des opérations de retry\n    if (Math.random() > 0.8) {\n      const failedConnection = mockConnections.find(c => c.status === 'error' || c.status === 'disconnected');\n      if (failedConnection) {\n        const newRetry: RetryOperation = {\n          id: `retry_${Date.now()}`,\n          api: failedConnection.name,\n          endpoint: failedConnection.endpoint,\n          attempt: Math.floor(Math.random() * 3) + 1,\n          maxAttempts: 3,\n          nextRetry: new Date(Date.now() + 5000),\n          reason: 'Connection timeout',\n          priority: Math.random() > 0.7 ? 'high' : 'medium'\n        };\n        \n        setRetryQueue(prev => [newRetry, ...prev.slice(0, 9)]);\n      }\n    }\n  };\n\n  const updateAPIConnection = (connectionData: APIConnection) => {\n    setApiConnections(prev => {\n      const index = prev.findIndex(c => c.id === connectionData.id);\n      if (index >= 0) {\n        const updated = [...prev];\n        updated[index] = connectionData;\n        return updated;\n      }\n      return [connectionData, ...prev];\n    });\n  };\n\n  const completeConnectionTest = (testData: ConnectionTest) => {\n    setActiveTests(prev => prev.filter(t => t.connectionId !== testData.connectionId));\n    \n    if (testData.result) {\n      setConnectionHistory(prev => [\n        {\n          timestamp: new Date(),\n          latency: testData.result!.latency,\n          success: testData.status === 'completed'\n        },\n        ...prev.slice(0, 49)\n      ]);\n    }\n  };\n\n  const updateTouchMetrics = () => {\n    setTouchMetrics(prev => ({\n      ...prev,\n      touchSensitivity: touchSensitivity,\n      activeConnections: apiConnections.filter(c => c.status === 'connected').length,\n      totalConnections: apiConnections.length,\n      retryQueueSize: retryQueue.length,\n      averageLatency: apiConnections.reduce((sum, c) => sum + c.latency, 0) / Math.max(apiConnections.length, 1)\n    }));\n  };\n\n  const testConnection = async (connectionId: string) => {\n    const connection = apiConnections.find(c => c.id === connectionId);\n    if (!connection) return;\n\n    const test: ConnectionTest = {\n      connectionId,\n      status: 'running',\n      startTime: new Date()\n    };\n\n    setActiveTests(prev => [test, ...prev]);\n\n    // Simuler un test de connexion\n    setTimeout(() => {\n      const success = Math.random() > 0.1; // 90% de succès\n      const completedTest: ConnectionTest = {\n        ...test,\n        status: success ? 'completed' : 'failed',\n        endTime: new Date(),\n        result: success ? {\n          latency: 50 + Math.random() * 100,\n          statusCode: 200,\n          responseSize: 1024 + Math.random() * 2048\n        } : {\n          latency: 0,\n          statusCode: 500,\n          responseSize: 0,\n          error: 'Connection timeout'\n        }\n      };\n\n      completeConnectionTest(completedTest);\n\n      // Mettre à jour le statut de la connexion\n      if (success) {\n        updateAPIConnection({\n          ...connection,\n          status: 'connected',\n          latency: completedTest.result!.latency,\n          lastTest: new Date()\n        });\n      }\n    }, 2000 + Math.random() * 3000);\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'connected': return <CheckCircle2 className=\"text-green-400\" size={16} />;\n      case 'disconnected': return <Unlink className=\"text-yellow-400\" size={16} />;\n      case 'error': return <AlertTriangle className=\"text-red-400\" size={16} />;\n      case 'testing': return <RefreshCw className=\"animate-spin text-blue-400\" size={16} />;\n      default: return <AlertTriangle className=\"text-gray-400\" size={16} />;\n    }\n  };\n\n  const getMethodColor = (method: string) => {\n    switch (method) {\n      case 'GET': return 'text-green-400';\n      case 'POST': return 'text-blue-400';\n      case 'PUT': return 'text-yellow-400';\n      case 'DELETE': return 'text-red-400';\n      case 'PATCH': return 'text-purple-400';\n      default: return 'text-gray-400';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'low': return 'text-blue-400';\n      case 'medium': return 'text-yellow-400';\n      case 'high': return 'text-orange-400';\n      case 'critical': return 'text-red-400';\n      default: return 'text-gray-400';\n    }\n  };\n\n  return (\n    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>\n      <div className=\"container mx-auto p-6\">\n        \n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center\">\n              <Hand className=\"text-white\" size={24} />\n            </div>\n            <div>\n              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                Toucher d'Hanuman\n              </h1>\n              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n                Organe Tactile • Intégrations API & Connectivité\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-3\">\n            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${\n              isConnected \n                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' \n                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'\n            }`}>\n              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}\n              <span className=\"text-sm font-medium\">\n                {isConnected ? 'Tactile Actif' : 'Hors Ligne'}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Contrôles de Sensibilité */}\n        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                🤲 Sensibilité Tactile\n              </h3>\n              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Ajustez la sensibilité de détection des connexions\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Faible\n              </span>\n              <input \n                type=\"range\" \n                min=\"0\" \n                max=\"1\" \n                step=\"0.1\"\n                value={touchSensitivity}\n                onChange={(e) => setTouchSensitivity(parseFloat(e.target.value))}\n                className=\"w-32 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700\"\n              />\n              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Élevée\n              </span>\n              <span className=\"text-lg font-bold text-orange-400\">\n                {(touchSensitivity * 100).toFixed(0)}%\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Métriques Globales */}\n        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n            Métriques Tactiles\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 lg:grid-cols-7 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-400\">\n                {touchMetrics.totalConnections}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Connexions Totales\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-green-400\">\n                {touchMetrics.activeConnections}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Connexions Actives\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-purple-400\">\n                {touchMetrics.totalRequests.toLocaleString()}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Requêtes Totales\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-green-400\">\n                {touchMetrics.successfulRequests.toLocaleString()}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Succès\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-red-400\">\n                {touchMetrics.failedRequests}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Échecs\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-orange-400\">\n                {touchMetrics.averageLatency.toFixed(0)}ms\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                Latence Moyenne\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">\n                {touchMetrics.retryQueueSize}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                File de Retry\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n          \n          {/* Connexions API */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-2`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n              🔌 Connexions API\n            </h3>\n            <div className=\"space-y-4\">\n              {apiConnections.map((connection) => (\n                <div key={connection.id} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} transition-all hover:scale-105`}>\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center space-x-3\">\n                      <Link className=\"text-blue-400\" size={20} />\n                      <div>\n                        <h4 className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                          {connection.name}\n                        </h4>\n                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                          {connection.endpoint}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`text-xs px-2 py-1 rounded ${getMethodColor(connection.method)} bg-opacity-20`}>\n                        {connection.method}\n                      </span>\n                      <button \n                        onClick={() => testConnection(connection.id)}\n                        disabled={activeTests.some(t => t.connectionId === connection.id)}\n                        className=\"p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50\"\n                      >\n                        <RefreshCw \n                          size={16} \n                          className={activeTests.some(t => t.connectionId === connection.id) ? 'animate-spin' : ''} \n                        />\n                      </button>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      {getStatusIcon(connection.status)}\n                      <span className={`text-sm font-medium ${\n                        connection.status === 'connected' ? 'text-green-400' :\n                        connection.status === 'disconnected' ? 'text-yellow-400' : 'text-red-400'\n                      }`}>\n                        {connection.status}\n                      </span>\n                    </div>\n                    <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                      {connection.latency.toFixed(0)}ms\n                    </span>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-3 gap-4 text-xs\">\n                    <div>\n                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Requêtes:</span>\n                      <div className=\"font-medium\">{connection.requestCount.toLocaleString()}</div>\n                    </div>\n                    <div>\n                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Erreurs:</span>\n                      <div className=\"font-medium\">{connection.errorCount}</div>\n                    </div>\n                    <div>\n                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Succès:</span>\n                      <div className=\"font-medium\">{connection.successRate.toFixed(1)}%</div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* File de Retry */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n              🔄 File de Retry\n            </h3>\n            <div className=\"space-y-3\">\n              {retryQueue.length === 0 ? (\n                <div className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                  <CheckCircle2 size={32} className=\"mx-auto mb-2 text-green-400\" />\n                  <p>Aucune opération en attente</p>\n                </div>\n              ) : (\n                retryQueue.map((retry) => (\n                  <div key={retry.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                    <div className=\"flex items-center space-x-3 mb-2\">\n                      <RefreshCw className=\"animate-spin text-blue-400\" size={16} />\n                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                        {retry.api}\n                      </span>\n                      <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(retry.priority)} bg-opacity-20`}>\n                        {retry.priority}\n                      </span>\n                    </div>\n                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>\n                      {retry.endpoint}\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                        Tentative {retry.attempt}/{retry.maxAttempts}\n                      </span>\n                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                        {retry.reason}\n                      </span>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HanumanTouchInterface;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAuDA,MAAM,wBAAwB,CAAC,EAAE,WAAW,IAAI,EAAE;;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,kBAAkB;QAClB,mBAAmB;QACnB,eAAe;QACf,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;IACpB;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+D,EAAE;IAC1H,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEvC,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM;uEAAsB;oBAC1B,IAAI;wBACF,kDAAkD;wBAClD,MAAM,OAAO,GAAG,IAAI,UAAU;wBAE9B,MAAM,OAAO,CAAC,MAAM;mFAAG;gCACrB,QAAQ,GAAG,CAAC;gCACZ,eAAe;gCAEf,6BAA6B;gCAC7B,MAAM,OAAO,EAAE,KAAK,KAAK,SAAS,CAAC;oCACjC,MAAM;oCACN,WAAW,KAAK,GAAG;gCACrB;4BACF;;wBAEA,MAAM,OAAO,CAAC,SAAS;mFAAG,CAAC;gCACzB,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gCAClC,iBAAiB;4BACnB;;wBAEA,MAAM,OAAO,CAAC,OAAO;mFAAG;gCACtB,QAAQ,GAAG,CAAC;gCACZ,eAAe;gCAEf,2BAA2B;gCAC3B,WAAW,qBAAqB;4BAClC;;wBAEA,MAAM,OAAO,CAAC,OAAO;mFAAG,CAAC;gCACvB,QAAQ,KAAK,CAAC,wBAAwB;gCACtC,eAAe;4BACjB;;oBAEF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,eAAe;oBACjB;gBACF;;YAEA;YAEA,yDAAyD;YACzD,MAAM,qBAAqB;sEAAY;oBACrC,IAAI,CAAC,aAAa;wBAChB;oBACF;gBACF;qEAAG;YAEH,4BAA4B;YAC5B,MAAM,kBAAkB;mEAAY;oBAClC;gBACF;kEAAG;YAEH;mDAAO;oBACL,cAAc;oBACd,cAAc;oBACd,MAAM,OAAO,EAAE;gBACjB;;QACF;0CAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,oBAAoB,KAAK,UAAU;gBACnC;YAEF,KAAK;gBACH,cAAc,CAAA,OAAQ;wBAAC,KAAK,KAAK;2BAAK,KAAK,KAAK,CAAC,GAAG;qBAAG;gBACvD;YAEF,KAAK;gBACH,eAAe,CAAA,OAAQ;wBAAC,KAAK,IAAI;2BAAK;qBAAK;gBAC3C;YAEF,KAAK;gBACH,uBAAuB,KAAK,IAAI;gBAChC;YAEF,KAAK;gBACH,kBAAkB,KAAK,WAAW;gBAClC;YAEF,KAAK;gBACH,gBAAgB,KAAK,OAAO;gBAC5B;YAEF;gBACE,QAAQ,GAAG,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,wBAAwB;QAC5B,gCAAgC;QAChC,MAAM,kBAAmC;YACvC;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,QAAQ,KAAK,MAAM,KAAK,MAAM,UAAU;gBACxC,SAAS,KAAK,KAAK,MAAM,KAAK;gBAC9B,QAAQ,OAAO,KAAK,MAAM,KAAK;gBAC/B,cAAc,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAChD,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACvC,aAAa,KAAK,KAAK,MAAM,KAAK;gBAClC,UAAU,IAAI;gBACd,cAAc,KAAK,KAAK,MAAM,KAAK;gBACnC,gBAAgB;YAClB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,SAAS,KAAK,KAAK,MAAM,KAAK;gBAC9B,QAAQ;gBACR,cAAc,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAChD,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACvC,aAAa,KAAK,KAAK,MAAM,KAAK;gBAClC,UAAU,IAAI;gBACd,cAAc,KAAK,KAAK,MAAM,KAAK;gBACnC,gBAAgB;YAClB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,SAAS,KAAK,KAAK,MAAM,KAAK;gBAC9B,QAAQ;gBACR,cAAc,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAC/C,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACvC,aAAa,KAAK,KAAK,MAAM,KAAK;gBAClC,UAAU,IAAI;gBACd,cAAc,KAAK,KAAK,MAAM,KAAK;gBACnC,gBAAgB;YAClB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,QAAQ,KAAK,MAAM,KAAK,OAAO,iBAAiB;gBAChD,SAAS,MAAM,KAAK,MAAM,KAAK;gBAC/B,QAAQ,OAAO,KAAK,MAAM,KAAK;gBAC/B,cAAc,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAC/C,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACvC,aAAa,KAAK,KAAK,MAAM,KAAK;gBAClC,UAAU,IAAI;gBACd,cAAc,KAAK,KAAK,MAAM,KAAK;gBACnC,gBAAgB;YAClB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,SAAS,KAAK,KAAK,MAAM,KAAK;gBAC9B,QAAQ;gBACR,cAAc,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAChD,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACvC,aAAa,OAAO,KAAK,MAAM,KAAK;gBACpC,UAAU,IAAI;gBACd,cAAc,KAAK,KAAK,MAAM,KAAK;gBACnC,gBAAgB;YAClB;SACD;QAED,kBAAkB;QAElB,qCAAqC;QACrC,IAAI,KAAK,MAAM,KAAK,KAAK;YACvB,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,EAAE,MAAM,KAAK;YACxF,IAAI,kBAAkB;gBACpB,MAAM,WAA2B;oBAC/B,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;oBACzB,KAAK,iBAAiB,IAAI;oBAC1B,UAAU,iBAAiB,QAAQ;oBACnC,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;oBACzC,aAAa;oBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;oBACjC,QAAQ;oBACR,UAAU,KAAK,MAAM,KAAK,MAAM,SAAS;gBAC3C;gBAEA,cAAc,CAAA,OAAQ;wBAAC;2BAAa,KAAK,KAAK,CAAC,GAAG;qBAAG;YACvD;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,CAAA;YAChB,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE;YAC5D,IAAI,SAAS,GAAG;gBACd,MAAM,UAAU;uBAAI;iBAAK;gBACzB,OAAO,CAAC,MAAM,GAAG;gBACjB,OAAO;YACT;YACA,OAAO;gBAAC;mBAAmB;aAAK;QAClC;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,SAAS,YAAY;QAEhF,IAAI,SAAS,MAAM,EAAE;YACnB,qBAAqB,CAAA,OAAQ;oBAC3B;wBACE,WAAW,IAAI;wBACf,SAAS,SAAS,MAAM,CAAE,OAAO;wBACjC,SAAS,SAAS,MAAM,KAAK;oBAC/B;uBACG,KAAK,KAAK,CAAC,GAAG;iBAClB;QACH;IACF;IAEA,MAAM,qBAAqB;QACzB,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,kBAAkB;gBAClB,mBAAmB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;gBAC9E,kBAAkB,eAAe,MAAM;gBACvC,gBAAgB,WAAW,MAAM;gBACjC,gBAAgB,eAAe,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO,EAAE,KAAK,KAAK,GAAG,CAAC,eAAe,MAAM,EAAE;YAC1G,CAAC;IACH;IAEA,MAAM,iBAAiB,OAAO;QAC5B,MAAM,aAAa,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrD,IAAI,CAAC,YAAY;QAEjB,MAAM,OAAuB;YAC3B;YACA,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,eAAe,CAAA,OAAQ;gBAAC;mBAAS;aAAK;QAEtC,+BAA+B;QAC/B,WAAW;YACT,MAAM,UAAU,KAAK,MAAM,KAAK,KAAK,gBAAgB;YACrD,MAAM,gBAAgC;gBACpC,GAAG,IAAI;gBACP,QAAQ,UAAU,cAAc;gBAChC,SAAS,IAAI;gBACb,QAAQ,UAAU;oBAChB,SAAS,KAAK,KAAK,MAAM,KAAK;oBAC9B,YAAY;oBACZ,cAAc,OAAO,KAAK,MAAM,KAAK;gBACvC,IAAI;oBACF,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,OAAO;gBACT;YACF;YAEA,uBAAuB;YAEvB,0CAA0C;YAC1C,IAAI,SAAS;gBACX,oBAAoB;oBAClB,GAAG,UAAU;oBACb,QAAQ;oBACR,SAAS,cAAc,MAAM,CAAE,OAAO;oBACtC,UAAU,IAAI;gBAChB;YACF;QACF,GAAG,OAAO,KAAK,MAAM,KAAK;IAC5B;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,qBAAO,6LAAC,wNAAA,CAAA,eAAY;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACxE,KAAK;gBAAgB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACtE,KAAK;gBAAS,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACnE,KAAK;gBAAW,qBAAO,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;oBAA6B,MAAM;;;;;;YAC/E;gBAAS,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACjE;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,4CAA4C,EAAE,WAAW,qBAAqB,cAAc;kBAC3G,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;wCAAa,MAAM;;;;;;;;;;;8CAErC,6LAAC;;sDACC,6LAAC;4CAAG,WAAW,CAAC,mBAAmB,EAAE,WAAW,eAAe,iBAAiB;sDAAE;;;;;;sDAGlF,6LAAC;4CAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;;;;;;;sCAM7E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,CAAC,iDAAiD,EAChE,cACI,sEACA,6DACJ;;oCACC,4BAAc,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;6DAAS,6LAAC,+MAAA,CAAA,UAAO;wCAAC,MAAM;;;;;;kDACnD,6LAAC;wCAAK,WAAU;kDACb,cAAc,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,6LAAC;oBAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;8BACvF,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAW,CAAC,kBAAkB,EAAE,WAAW,eAAe,iBAAiB;kDAAE;;;;;;kDAGjF,6LAAC;wCAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;kDAAE;;;;;;;;;;;;0CAI3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;kDAAE;;;;;;kDAG5E,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC9D,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;kDAAE;;;;;;kDAG5E,6LAAC;wCAAK,WAAU;;4CACb,CAAC,mBAAmB,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAO7C,6LAAC;oBAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;sCACvF,6LAAC;4BAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;sCAAE;;;;;;sCAGtF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,aAAa,gBAAgB;;;;;;sDAEhC,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,aAAa,iBAAiB;;;;;;sDAEjC,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,aAAa,aAAa,CAAC,cAAc;;;;;;sDAE5C,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,aAAa,kBAAkB,CAAC,cAAc;;;;;;sDAEjD,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,aAAa,cAAc;;;;;;sDAE9B,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,aAAa,cAAc,CAAC,OAAO,CAAC;gDAAG;;;;;;;sDAE1C,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,aAAa,cAAc;;;;;;sDAE9B,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAOjF,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,wBAAwB,CAAC;;8CAChG,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;8CAAE;;;;;;8CAGtF,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,2BACnB,6LAAC;4CAAwB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,cAAc,+BAA+B,CAAC;;8DAC7H,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;oEAAgB,MAAM;;;;;;8EACtC,6LAAC;;sFACC,6LAAC;4EAAG,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;sFAC9E,WAAW,IAAI;;;;;;sFAElB,6LAAC;4EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sFACpE,WAAW,QAAQ;;;;;;;;;;;;;;;;;;sEAI1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAC,0BAA0B,EAAE,eAAe,WAAW,MAAM,EAAE,cAAc,CAAC;8EAC5F,WAAW,MAAM;;;;;;8EAEpB,6LAAC;oEACC,SAAS,IAAM,eAAe,WAAW,EAAE;oEAC3C,UAAU,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,WAAW,EAAE;oEAChE,WAAU;8EAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;wEACR,MAAM;wEACN,WAAW,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,WAAW,EAAE,IAAI,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;8DAM9F,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,WAAW,MAAM;8EAChC,6LAAC;oEAAK,WAAW,CAAC,oBAAoB,EACpC,WAAW,MAAM,KAAK,cAAc,mBACpC,WAAW,MAAM,KAAK,iBAAiB,oBAAoB,gBAC3D;8EACC,WAAW,MAAM;;;;;;;;;;;;sEAGtB,6LAAC;4DAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;;gEACvE,WAAW,OAAO,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAInC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAW,WAAW,kBAAkB;8EAAiB;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EAAe,WAAW,YAAY,CAAC,cAAc;;;;;;;;;;;;sEAEtE,6LAAC;;8EACC,6LAAC;oEAAK,WAAW,WAAW,kBAAkB;8EAAiB;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EAAe,WAAW,UAAU;;;;;;;;;;;;sEAErD,6LAAC;;8EACC,6LAAC;oEAAK,WAAW,WAAW,kBAAkB;8EAAiB;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;;wEAAe,WAAW,WAAW,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;2CAxD5D,WAAW,EAAE;;;;;;;;;;;;;;;;sCAiE7B,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;8CAClF,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,iBAAiB;8CAAE;;;;;;8CAGtF,6LAAC;oCAAI,WAAU;8CACZ,WAAW,MAAM,KAAK,kBACrB,6LAAC;wCAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW,kBAAkB,iBAAiB;;0DAChF,6LAAC,wNAAA,CAAA,eAAY;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAClC,6LAAC;0DAAE;;;;;;;;;;;+CAGL,WAAW,GAAG,CAAC,CAAC,sBACd,6LAAC;4CAAmB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;;8DACzF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;4DAA6B,MAAM;;;;;;sEACxD,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;sEAChF,MAAM,GAAG;;;;;;sEAEZ,6LAAC;4DAAK,WAAW,CAAC,0BAA0B,EAAE,iBAAiB,MAAM,QAAQ,EAAE,cAAc,CAAC;sEAC3F,MAAM,QAAQ;;;;;;;;;;;;8DAGnB,6LAAC;oDAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,gBAAgB,KAAK,CAAC;8DAC3E,MAAM,QAAQ;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;;gEAAE;gEAC/D,MAAM,OAAO;gEAAC;gEAAE,MAAM,WAAW;;;;;;;sEAE9C,6LAAC;4DAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sEACvE,MAAM,MAAM;;;;;;;;;;;;;2CAlBT,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BpC;GAxkBM;KAAA;uCA0kBS", "debugId": null}}, {"offset": {"line": 3382, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Agentic-Coding-Framework-%20RB2/hanuman-final/services/AgentConnectionManager.ts"], "sourcesContent": ["import { EventEmitter } from 'events';\nimport WebSocket from 'ws';\nimport axios, { AxiosResponse } from 'axios';\n\n// Types pour les connexions d'agents\nexport interface AgentConfig {\n  id: string;\n  name: string;\n  type: 'frontend' | 'backend' | 'devops' | 'security' | 'qa' | 'web-research' | 'documentation' | 'marketing' | 'uiux' | 'performance';\n  host: string;\n  port: number;\n  apiPath: string;\n  wsPath?: string;\n  status: 'active' | 'inactive' | 'error' | 'connecting';\n  lastHeartbeat?: Date;\n  capabilities: string[];\n  metadata?: Record<string, any>;\n}\n\nexport interface AgentMessage {\n  type: string;\n  agentId: string;\n  timestamp: number;\n  data: any;\n  correlationId?: string;\n}\n\nexport interface AgentResponse {\n  success: boolean;\n  data?: any;\n  error?: string;\n  timestamp: number;\n  agentId: string;\n}\n\n/**\n * Gestionnaire central des connexions avec tous les agents Hanuman\n * Intègre les organes sensoriels avec l'architecture neuronale distribuée\n */\nexport class AgentConnectionManager extends EventEmitter {\n  private agents: Map<string, AgentConfig> = new Map();\n  private wsConnections: Map<string, WebSocket> = new Map();\n  private heartbeatIntervals: Map<string, NodeJS.Timeout> = new Map();\n  private reconnectAttempts: Map<string, number> = new Map();\n  private maxReconnectAttempts = 5;\n  private heartbeatInterval = 30000; // 30 secondes\n\n  constructor() {\n    super();\n    this.initializeAgents();\n    this.startHealthMonitoring();\n  }\n\n  /**\n   * Initialise la configuration des agents basée sur l'architecture existante\n   */\n  private initializeAgents(): void {\n    const agentConfigs: AgentConfig[] = [\n      {\n        id: 'agent-frontend',\n        name: 'Agent Frontend',\n        type: 'frontend',\n        host: 'localhost',\n        port: 3001,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['ui-generation', 'component-creation', 'styling', 'responsive-design']\n      },\n      {\n        id: 'agent-backend',\n        name: 'Agent Backend',\n        type: 'backend',\n        host: 'localhost',\n        port: 3002,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['api-development', 'database-design', 'microservices', 'authentication']\n      },\n      {\n        id: 'agent-web-research',\n        name: 'Agent Web Research',\n        type: 'web-research',\n        host: 'localhost',\n        port: 3003,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['web-scraping', 'data-collection', 'trend-analysis', 'content-discovery']\n      },\n      {\n        id: 'agent-devops',\n        name: 'Agent DevOps',\n        type: 'devops',\n        host: 'localhost',\n        port: 3004,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['deployment', 'infrastructure', 'monitoring', 'ci-cd']\n      },\n      {\n        id: 'agent-security',\n        name: 'Agent Security',\n        type: 'security',\n        host: 'localhost',\n        port: 3007,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['vulnerability-scanning', 'threat-detection', 'compliance', 'encryption']\n      },\n      {\n        id: 'agent-qa',\n        name: 'Agent QA',\n        type: 'qa',\n        host: 'localhost',\n        port: 3005,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['testing', 'quality-assurance', 'automation', 'performance-testing']\n      },\n      {\n        id: 'agent-documentation',\n        name: 'Agent Documentation',\n        type: 'documentation',\n        host: 'localhost',\n        port: 3006,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['documentation-generation', 'api-docs', 'user-guides', 'technical-writing']\n      },\n      {\n        id: 'agent-marketing',\n        name: 'Agent Marketing',\n        type: 'marketing',\n        host: 'localhost',\n        port: 3008,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['content-marketing', 'seo', 'analytics', 'campaign-management']\n      },\n      {\n        id: 'agent-uiux',\n        name: 'Agent UI/UX',\n        type: 'uiux',\n        host: 'localhost',\n        port: 3009,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['design-thinking', 'user-research', 'prototyping', 'accessibility']\n      },\n      {\n        id: 'agent-performance',\n        name: 'Agent Performance',\n        type: 'performance',\n        host: 'localhost',\n        port: 3010,\n        apiPath: '/api',\n        wsPath: '/ws',\n        status: 'inactive',\n        capabilities: ['performance-monitoring', 'optimization', 'metrics-analysis', 'alerting']\n      }\n    ];\n\n    agentConfigs.forEach(config => {\n      this.agents.set(config.id, config);\n    });\n\n    console.log(`🧠 Initialized ${agentConfigs.length} agents in Hanuman's neural network`);\n  }\n\n  /**\n   * Démarre le monitoring de santé de tous les agents\n   */\n  private startHealthMonitoring(): void {\n    setInterval(() => {\n      this.checkAllAgentsHealth();\n    }, this.heartbeatInterval);\n\n    // Tentative de connexion initiale à tous les agents\n    this.connectToAllAgents();\n  }\n\n  /**\n   * Connecte à tous les agents disponibles\n   */\n  public async connectToAllAgents(): Promise<void> {\n    const connectionPromises = Array.from(this.agents.values()).map(agent => \n      this.connectToAgent(agent.id)\n    );\n\n    await Promise.allSettled(connectionPromises);\n    this.emit('agents:connection-attempt-completed');\n  }\n\n  /**\n   * Connecte à un agent spécifique\n   */\n  public async connectToAgent(agentId: string): Promise<boolean> {\n    const agent = this.agents.get(agentId);\n    if (!agent) {\n      console.error(`❌ Agent ${agentId} not found`);\n      return false;\n    }\n\n    try {\n      // Test de connexion HTTP d'abord\n      const healthCheck = await this.performHealthCheck(agent);\n      if (!healthCheck) {\n        agent.status = 'error';\n        this.agents.set(agentId, agent);\n        return false;\n      }\n\n      // Connexion WebSocket si disponible\n      if (agent.wsPath) {\n        await this.establishWebSocketConnection(agent);\n      }\n\n      agent.status = 'active';\n      agent.lastHeartbeat = new Date();\n      this.agents.set(agentId, agent);\n      \n      console.log(`✅ Connected to ${agent.name}`);\n      this.emit('agent:connected', agent);\n      \n      return true;\n\n    } catch (error) {\n      console.error(`❌ Failed to connect to ${agent.name}:`, error);\n      agent.status = 'error';\n      this.agents.set(agentId, agent);\n      \n      // Programmer une reconnexion\n      this.scheduleReconnection(agentId);\n      \n      return false;\n    }\n  }\n\n  /**\n   * Effectue un health check HTTP sur un agent\n   */\n  private async performHealthCheck(agent: AgentConfig): Promise<boolean> {\n    try {\n      const url = `http://${agent.host}:${agent.port}${agent.apiPath}/health`;\n      const response: AxiosResponse = await axios.get(url, { \n        timeout: 5000,\n        headers: {\n          'User-Agent': 'Hanuman-Neural-Network/1.0'\n        }\n      });\n      \n      return response.status === 200;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Établit une connexion WebSocket avec un agent\n   */\n  private async establishWebSocketConnection(agent: AgentConfig): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const wsUrl = `ws://${agent.host}:${agent.port}${agent.wsPath}`;\n      const ws = new WebSocket(wsUrl);\n\n      ws.on('open', () => {\n        console.log(`🔗 WebSocket connected to ${agent.name}`);\n        this.wsConnections.set(agent.id, ws);\n        \n        // Envoyer un message d'identification\n        ws.send(JSON.stringify({\n          type: 'HANUMAN_IDENTIFY',\n          source: 'hanuman-neural-network',\n          timestamp: Date.now()\n        }));\n        \n        resolve();\n      });\n\n      ws.on('message', (data: string) => {\n        try {\n          const message: AgentMessage = JSON.parse(data);\n          this.handleAgentMessage(agent.id, message);\n        } catch (error) {\n          console.error(`❌ Invalid message from ${agent.name}:`, error);\n        }\n      });\n\n      ws.on('close', () => {\n        console.log(`❌ WebSocket disconnected from ${agent.name}`);\n        this.wsConnections.delete(agent.id);\n        agent.status = 'inactive';\n        this.agents.set(agent.id, agent);\n        this.emit('agent:disconnected', agent);\n        \n        // Programmer une reconnexion\n        this.scheduleReconnection(agent.id);\n      });\n\n      ws.on('error', (error) => {\n        console.error(`❌ WebSocket error with ${agent.name}:`, error);\n        reject(error);\n      });\n\n      // Timeout de connexion\n      setTimeout(() => {\n        if (ws.readyState !== WebSocket.OPEN) {\n          ws.close();\n          reject(new Error('WebSocket connection timeout'));\n        }\n      }, 10000);\n    });\n  }\n\n  /**\n   * Gère les messages reçus des agents\n   */\n  private handleAgentMessage(agentId: string, message: AgentMessage): void {\n    const agent = this.agents.get(agentId);\n    if (!agent) return;\n\n    // Mettre à jour le heartbeat\n    agent.lastHeartbeat = new Date();\n    this.agents.set(agentId, agent);\n\n    // Émettre le message pour les interfaces\n    this.emit('agent:message', {\n      agentId,\n      agent,\n      message\n    });\n\n    // Traitement spécifique selon le type de message\n    switch (message.type) {\n      case 'HEARTBEAT':\n        this.emit('agent:heartbeat', { agentId, agent });\n        break;\n        \n      case 'STATUS_UPDATE':\n        this.emit('agent:status-update', { agentId, agent, data: message.data });\n        break;\n        \n      case 'CAPABILITY_UPDATE':\n        agent.capabilities = message.data.capabilities || agent.capabilities;\n        this.agents.set(agentId, agent);\n        this.emit('agent:capability-update', { agentId, agent });\n        break;\n        \n      case 'ERROR':\n        this.emit('agent:error', { agentId, agent, error: message.data });\n        break;\n        \n      default:\n        this.emit('agent:custom-message', { agentId, agent, message });\n    }\n  }\n\n  /**\n   * Envoie un message à un agent spécifique\n   */\n  public async sendToAgent(agentId: string, message: any): Promise<AgentResponse> {\n    const agent = this.agents.get(agentId);\n    if (!agent) {\n      throw new Error(`Agent ${agentId} not found`);\n    }\n\n    const ws = this.wsConnections.get(agentId);\n    if (ws && ws.readyState === WebSocket.OPEN) {\n      // Envoi via WebSocket\n      return new Promise((resolve, reject) => {\n        const correlationId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        \n        const messageWithId = {\n          ...message,\n          correlationId,\n          timestamp: Date.now(),\n          source: 'hanuman-neural-network'\n        };\n\n        // Écouter la réponse\n        const responseHandler = (data: any) => {\n          if (data.agentId === agentId && data.message.correlationId === correlationId) {\n            this.off('agent:message', responseHandler);\n            resolve({\n              success: true,\n              data: data.message.data,\n              timestamp: Date.now(),\n              agentId\n            });\n          }\n        };\n\n        this.on('agent:message', responseHandler);\n\n        // Timeout\n        setTimeout(() => {\n          this.off('agent:message', responseHandler);\n          reject(new Error('Message timeout'));\n        }, 30000);\n\n        ws.send(JSON.stringify(messageWithId));\n      });\n    } else {\n      // Fallback vers HTTP\n      try {\n        const url = `http://${agent.host}:${agent.port}${agent.apiPath}/message`;\n        const response = await axios.post(url, message, { timeout: 30000 });\n        \n        return {\n          success: true,\n          data: response.data,\n          timestamp: Date.now(),\n          agentId\n        };\n      } catch (error) {\n        return {\n          success: false,\n          error: error instanceof Error ? error.message : 'Unknown error',\n          timestamp: Date.now(),\n          agentId\n        };\n      }\n    }\n  }\n\n  /**\n   * Diffuse un message à tous les agents actifs\n   */\n  public async broadcastToAllAgents(message: any): Promise<Map<string, AgentResponse>> {\n    const responses = new Map<string, AgentResponse>();\n    const activeAgents = Array.from(this.agents.values()).filter(agent => agent.status === 'active');\n\n    const promises = activeAgents.map(async (agent) => {\n      try {\n        const response = await this.sendToAgent(agent.id, message);\n        responses.set(agent.id, response);\n      } catch (error) {\n        responses.set(agent.id, {\n          success: false,\n          error: error instanceof Error ? error.message : 'Unknown error',\n          timestamp: Date.now(),\n          agentId: agent.id\n        });\n      }\n    });\n\n    await Promise.allSettled(promises);\n    return responses;\n  }\n\n  /**\n   * Vérifie la santé de tous les agents\n   */\n  private async checkAllAgentsHealth(): Promise<void> {\n    const healthPromises = Array.from(this.agents.values()).map(async (agent) => {\n      if (agent.status === 'active') {\n        const isHealthy = await this.performHealthCheck(agent);\n        if (!isHealthy) {\n          agent.status = 'error';\n          this.agents.set(agent.id, agent);\n          this.emit('agent:health-check-failed', agent);\n        }\n      }\n    });\n\n    await Promise.allSettled(healthPromises);\n  }\n\n  /**\n   * Programme une reconnexion pour un agent\n   */\n  private scheduleReconnection(agentId: string): void {\n    const attempts = this.reconnectAttempts.get(agentId) || 0;\n    if (attempts >= this.maxReconnectAttempts) {\n      console.log(`❌ Max reconnection attempts reached for ${agentId}`);\n      return;\n    }\n\n    const delay = Math.min(1000 * Math.pow(2, attempts), 30000); // Exponential backoff, max 30s\n    \n    setTimeout(async () => {\n      console.log(`🔄 Attempting to reconnect to ${agentId} (attempt ${attempts + 1})`);\n      const success = await this.connectToAgent(agentId);\n      \n      if (success) {\n        this.reconnectAttempts.delete(agentId);\n      } else {\n        this.reconnectAttempts.set(agentId, attempts + 1);\n      }\n    }, delay);\n  }\n\n  /**\n   * Obtient le statut de tous les agents\n   */\n  public getAllAgentsStatus(): AgentConfig[] {\n    return Array.from(this.agents.values());\n  }\n\n  /**\n   * Obtient le statut d'un agent spécifique\n   */\n  public getAgentStatus(agentId: string): AgentConfig | undefined {\n    return this.agents.get(agentId);\n  }\n\n  /**\n   * Obtient les agents par type\n   */\n  public getAgentsByType(type: AgentConfig['type']): AgentConfig[] {\n    return Array.from(this.agents.values()).filter(agent => agent.type === type);\n  }\n\n  /**\n   * Obtient les agents actifs\n   */\n  public getActiveAgents(): AgentConfig[] {\n    return Array.from(this.agents.values()).filter(agent => agent.status === 'active');\n  }\n\n  /**\n   * Ferme toutes les connexions\n   */\n  public async disconnect(): Promise<void> {\n    // Fermer toutes les connexions WebSocket\n    this.wsConnections.forEach((ws, agentId) => {\n      ws.close();\n    });\n    this.wsConnections.clear();\n\n    // Arrêter tous les intervalles de heartbeat\n    this.heartbeatIntervals.forEach((interval) => {\n      clearInterval(interval);\n    });\n    this.heartbeatIntervals.clear();\n\n    // Mettre à jour le statut de tous les agents\n    this.agents.forEach((agent, agentId) => {\n      agent.status = 'inactive';\n      this.agents.set(agentId, agent);\n    });\n\n    console.log('🔌 Disconnected from all agents');\n  }\n}\n\n// Instance singleton pour l'utilisation globale\nexport const agentConnectionManager = new AgentConnectionManager();\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAqCO,MAAM,+BAA+B,+JAAA,CAAA,eAAY;IAC9C,SAAmC,IAAI,MAAM;IAC7C,gBAAwC,IAAI,MAAM;IAClD,qBAAkD,IAAI,MAAM;IAC5D,oBAAyC,IAAI,MAAM;IACnD,uBAAuB,EAAE;IACzB,oBAAoB,MAAM;IAElC,aAAc;QACZ,KAAK;QACL,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,qBAAqB;IAC5B;IAEA;;GAEC,GACD,AAAQ,mBAAyB;QAC/B,MAAM,eAA8B;YAClC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAAiB;oBAAsB;oBAAW;iBAAoB;YACvF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAAmB;oBAAmB;oBAAiB;iBAAiB;YACzF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAAgB;oBAAmB;oBAAkB;iBAAoB;YAC1F;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAAc;oBAAkB;oBAAc;iBAAQ;YACvE;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAA0B;oBAAoB;oBAAc;iBAAa;YAC1F;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAAW;oBAAqB;oBAAc;iBAAsB;YACrF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAA4B;oBAAY;oBAAe;iBAAoB;YAC5F;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAAqB;oBAAO;oBAAa;iBAAsB;YAChF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAAmB;oBAAiB;oBAAe;iBAAgB;YACpF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,cAAc;oBAAC;oBAA0B;oBAAgB;oBAAoB;iBAAW;YAC1F;SACD;QAED,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE;QAC7B;QAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,aAAa,MAAM,CAAC,mCAAmC,CAAC;IACxF;IAEA;;GAEC,GACD,AAAQ,wBAA8B;QACpC,YAAY;YACV,IAAI,CAAC,oBAAoB;QAC3B,GAAG,IAAI,CAAC,iBAAiB;QAEzB,oDAAoD;QACpD,IAAI,CAAC,kBAAkB;IACzB;IAEA;;GAEC,GACD,MAAa,qBAAoC;QAC/C,MAAM,qBAAqB,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,CAAA,QAC9D,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;QAG9B,MAAM,QAAQ,UAAU,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA;;GAEC,GACD,MAAa,eAAe,OAAe,EAAoB;QAC7D,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,UAAU,CAAC;YAC5C,OAAO;QACT;QAEA,IAAI;YACF,iCAAiC;YACjC,MAAM,cAAc,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAClD,IAAI,CAAC,aAAa;gBAChB,MAAM,MAAM,GAAG;gBACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;gBACzB,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,MAAM,MAAM,EAAE;gBAChB,MAAM,IAAI,CAAC,4BAA4B,CAAC;YAC1C;YAEA,MAAM,MAAM,GAAG;YACf,MAAM,aAAa,GAAG,IAAI;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;YAEzB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,MAAM,IAAI,EAAE;YAC1C,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAE7B,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;YACvD,MAAM,MAAM,GAAG;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;YAEzB,6BAA6B;YAC7B,IAAI,CAAC,oBAAoB,CAAC;YAE1B,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAc,mBAAmB,KAAkB,EAAoB;QACrE,IAAI;YACF,MAAM,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC;YACvE,MAAM,WAA0B,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBACnD,SAAS;gBACT,SAAS;oBACP,cAAc;gBAChB;YACF;YAEA,OAAO,SAAS,MAAM,KAAK;QAC7B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAc,6BAA6B,KAAkB,EAAiB;QAC5E,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,QAAQ,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,EAAE;YAC/D,MAAM,KAAK,IAAI,gIAAA,CAAA,UAAS,CAAC;YAEzB,GAAG,EAAE,CAAC,QAAQ;gBACZ,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,MAAM,IAAI,EAAE;gBACrD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE;gBAEjC,sCAAsC;gBACtC,GAAG,IAAI,CAAC,KAAK,SAAS,CAAC;oBACrB,MAAM;oBACN,QAAQ;oBACR,WAAW,KAAK,GAAG;gBACrB;gBAEA;YACF;YAEA,GAAG,EAAE,CAAC,WAAW,CAAC;gBAChB,IAAI;oBACF,MAAM,UAAwB,KAAK,KAAK,CAAC;oBACzC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE;gBACpC,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;gBACzD;YACF;YAEA,GAAG,EAAE,CAAC,SAAS;gBACb,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,MAAM,IAAI,EAAE;gBACzD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE;gBAClC,MAAM,MAAM,GAAG;gBACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,sBAAsB;gBAEhC,6BAA6B;gBAC7B,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;YACpC;YAEA,GAAG,EAAE,CAAC,SAAS,CAAC;gBACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;gBACvD,OAAO;YACT;YAEA,uBAAuB;YACvB,WAAW;gBACT,IAAI,GAAG,UAAU,KAAK,gIAAA,CAAA,UAAS,CAAC,IAAI,EAAE;oBACpC,GAAG,KAAK;oBACR,OAAO,IAAI,MAAM;gBACnB;YACF,GAAG;QACL;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,OAAe,EAAE,OAAqB,EAAQ;QACvE,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,OAAO;QAEZ,6BAA6B;QAC7B,MAAM,aAAa,GAAG,IAAI;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;QAEzB,yCAAyC;QACzC,IAAI,CAAC,IAAI,CAAC,iBAAiB;YACzB;YACA;YACA;QACF;QAEA,iDAAiD;QACjD,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,IAAI,CAAC,IAAI,CAAC,mBAAmB;oBAAE;oBAAS;gBAAM;gBAC9C;YAEF,KAAK;gBACH,IAAI,CAAC,IAAI,CAAC,uBAAuB;oBAAE;oBAAS;oBAAO,MAAM,QAAQ,IAAI;gBAAC;gBACtE;YAEF,KAAK;gBACH,MAAM,YAAY,GAAG,QAAQ,IAAI,CAAC,YAAY,IAAI,MAAM,YAAY;gBACpE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;gBACzB,IAAI,CAAC,IAAI,CAAC,2BAA2B;oBAAE;oBAAS;gBAAM;gBACtD;YAEF,KAAK;gBACH,IAAI,CAAC,IAAI,CAAC,eAAe;oBAAE;oBAAS;oBAAO,OAAO,QAAQ,IAAI;gBAAC;gBAC/D;YAEF;gBACE,IAAI,CAAC,IAAI,CAAC,wBAAwB;oBAAE;oBAAS;oBAAO;gBAAQ;QAChE;IACF;IAEA;;GAEC,GACD,MAAa,YAAY,OAAe,EAAE,OAAY,EAA0B;QAC9E,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,QAAQ,UAAU,CAAC;QAC9C;QAEA,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAClC,IAAI,MAAM,GAAG,UAAU,KAAK,gIAAA,CAAA,UAAS,CAAC,IAAI,EAAE;YAC1C,sBAAsB;YACtB,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,MAAM,gBAAgB,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBAEpF,MAAM,gBAAgB;oBACpB,GAAG,OAAO;oBACV;oBACA,WAAW,KAAK,GAAG;oBACnB,QAAQ;gBACV;gBAEA,qBAAqB;gBACrB,MAAM,kBAAkB,CAAC;oBACvB,IAAI,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,CAAC,aAAa,KAAK,eAAe;wBAC5E,IAAI,CAAC,GAAG,CAAC,iBAAiB;wBAC1B,QAAQ;4BACN,SAAS;4BACT,MAAM,KAAK,OAAO,CAAC,IAAI;4BACvB,WAAW,KAAK,GAAG;4BACnB;wBACF;oBACF;gBACF;gBAEA,IAAI,CAAC,EAAE,CAAC,iBAAiB;gBAEzB,UAAU;gBACV,WAAW;oBACT,IAAI,CAAC,GAAG,CAAC,iBAAiB;oBAC1B,OAAO,IAAI,MAAM;gBACnB,GAAG;gBAEH,GAAG,IAAI,CAAC,KAAK,SAAS,CAAC;YACzB;QACF,OAAO;YACL,qBAAqB;YACrB,IAAI;gBACF,MAAM,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC;gBACxE,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK,SAAS;oBAAE,SAAS;gBAAM;gBAEjE,OAAO;oBACL,SAAS;oBACT,MAAM,SAAS,IAAI;oBACnB,WAAW,KAAK,GAAG;oBACnB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;oBACL,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW,KAAK,GAAG;oBACnB;gBACF;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAa,qBAAqB,OAAY,EAAuC;QACnF,MAAM,YAAY,IAAI;QACtB,MAAM,eAAe,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAEvF,MAAM,WAAW,aAAa,GAAG,CAAC,OAAO;YACvC,IAAI;gBACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE;gBAClD,UAAU,GAAG,CAAC,MAAM,EAAE,EAAE;YAC1B,EAAE,OAAO,OAAO;gBACd,UAAU,GAAG,CAAC,MAAM,EAAE,EAAE;oBACtB,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW,KAAK,GAAG;oBACnB,SAAS,MAAM,EAAE;gBACnB;YACF;QACF;QAEA,MAAM,QAAQ,UAAU,CAAC;QACzB,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,uBAAsC;QAClD,MAAM,iBAAiB,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO;YACjE,IAAI,MAAM,MAAM,KAAK,UAAU;gBAC7B,MAAM,YAAY,MAAM,IAAI,CAAC,kBAAkB,CAAC;gBAChD,IAAI,CAAC,WAAW;oBACd,MAAM,MAAM,GAAG;oBACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE;oBAC1B,IAAI,CAAC,IAAI,CAAC,6BAA6B;gBACzC;YACF;QACF;QAEA,MAAM,QAAQ,UAAU,CAAC;IAC3B;IAEA;;GAEC,GACD,AAAQ,qBAAqB,OAAe,EAAQ;QAClD,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY;QACxD,IAAI,YAAY,IAAI,CAAC,oBAAoB,EAAE;YACzC,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,SAAS;YAChE;QACF;QAEA,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,WAAW,QAAQ,+BAA+B;QAE5F,WAAW;YACT,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,QAAQ,UAAU,EAAE,WAAW,EAAE,CAAC,CAAC;YAChF,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,CAAC;YAE1C,IAAI,SAAS;gBACX,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAChC,OAAO;gBACL,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,WAAW;YACjD;QACF,GAAG;IACL;IAEA;;GAEC,GACD,AAAO,qBAAoC;QACzC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IACtC;IAEA;;GAEC,GACD,AAAO,eAAe,OAAe,EAA2B;QAC9D,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;IACzB;IAEA;;GAEC,GACD,AAAO,gBAAgB,IAAyB,EAAiB;QAC/D,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;IACzE;IAEA;;GAEC,GACD,AAAO,kBAAiC;QACtC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IAC3E;IAEA;;GAEC,GACD,MAAa,aAA4B;QACvC,yCAAyC;QACzC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI;YAC9B,GAAG,KAAK;QACV;QACA,IAAI,CAAC,aAAa,CAAC,KAAK;QAExB,4CAA4C;QAC5C,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC/B,cAAc;QAChB;QACA,IAAI,CAAC,kBAAkB,CAAC,KAAK;QAE7B,6CAA6C;QAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO;YAC1B,MAAM,MAAM,GAAG;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;QAC3B;QAEA,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,MAAM,yBAAyB,IAAI", "debugId": null}}, {"offset": {"line": 3907, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Agentic-Coding-Framework-%20RB2/hanuman-final/services/TrimurtiController.ts"], "sourcesContent": ["import { EventEmitter } from 'events';\n\n// Types pour le Framework Trimurti\nexport interface CosmicEnergy {\n  brahma: number;    // Énergie créatrice (0-1)\n  vishnu: number;    // Énergie conservatrice (0-1)\n  shiva: number;     // Énergie transformatrice (0-1)\n}\n\nexport interface CosmicPhase {\n  dominant: 'brahma' | 'vishnu' | 'shiva' | 'equilibrium';\n  intensity: number;\n  duration: number;\n  startTime: Date;\n  nextTransition: Date;\n}\n\nexport interface CosmicContext {\n  type: 'new_project' | 'maintenance' | 'crisis' | 'optimization' | 'innovation';\n  complexity: 'low' | 'medium' | 'high' | 'cosmic';\n  creativity_required: number;\n  stability_impact: 'low' | 'medium' | 'high';\n  transformation_needed: boolean;\n  urgency: 'low' | 'medium' | 'high' | 'critical';\n}\n\nexport interface CosmicAgent {\n  id: string;\n  name: string;\n  cosmicAffinity: 'brahma' | 'vishnu' | 'shiva';\n  energyLevel: number;\n  cosmicMode: boolean;\n  lastActivation: Date;\n  cosmicContributions: {\n    creation: number;\n    preservation: number;\n    transformation: number;\n  };\n}\n\nexport interface CosmicWorkflow {\n  id: string;\n  name: string;\n  phases: Array<{\n    principle: 'brahma' | 'vishnu' | 'shiva';\n    duration: number;\n    energyLevel: number;\n    agents: string[];\n    activities: string[];\n    mantras: string[];\n    successMetrics: string[];\n  }>;\n  totalDuration: number;\n  expectedOutcome: string;\n}\n\n/**\n * 🕉️ Contrôleur Trimurti - Gardien de l'Équilibre Cosmique\n * Orchestrateur des trois énergies fondamentales d'Hanuman\n */\nexport class TrimurtiController extends EventEmitter {\n  private cosmicEnergy: CosmicEnergy;\n  private currentPhase: CosmicPhase;\n  private cosmicAgents: Map<string, CosmicAgent> = new Map();\n  private activeWorkflows: Map<string, CosmicWorkflow> = new Map();\n  private cosmicCycles: {\n    daily: NodeJS.Timeout | null;\n    weekly: NodeJS.Timeout | null;\n    monthly: NodeJS.Timeout | null;\n  };\n\n  constructor() {\n    super();\n    \n    // Initialisation équilibre cosmique parfait\n    this.cosmicEnergy = {\n      brahma: 0.33,   // Création\n      vishnu: 0.33,   // Conservation\n      shiva: 0.33     // Transformation\n    };\n\n    this.currentPhase = {\n      dominant: 'equilibrium',\n      intensity: 0.5,\n      duration: 3600000, // 1 heure\n      startTime: new Date(),\n      nextTransition: new Date(Date.now() + 3600000)\n    };\n\n    this.cosmicCycles = {\n      daily: null,\n      weekly: null,\n      monthly: null\n    };\n\n    this.initializeCosmicAgents();\n    this.startCosmicCycles();\n    this.bindCosmicEvents();\n\n    console.log('🕉️ TrimurtiController initialisé - Équilibre cosmique établi');\n  }\n\n  /**\n   * Initialise les agents avec leurs affinités cosmiques\n   */\n  private initializeCosmicAgents(): void {\n    const agentConfigurations: Array<Omit<CosmicAgent, 'energyLevel' | 'cosmicMode' | 'lastActivation' | 'cosmicContributions'>> = [\n      // 🌅 Agents à Dominante BRAHMA (Créateurs)\n      { id: 'cortex-creatif', name: 'Cortex Créatif', cosmicAffinity: 'brahma' },\n      { id: 'agent-evolution', name: 'Agent Evolution', cosmicAffinity: 'brahma' },\n      { id: 'agent-web-research', name: 'Agent Web Research', cosmicAffinity: 'brahma' },\n      { id: 'cortex-central', name: 'Cortex Central', cosmicAffinity: 'brahma' },\n      { id: 'agent-frontend', name: 'Agent Frontend', cosmicAffinity: 'brahma' },\n\n      // 🌊 Agents à Dominante VISHNU (Conservateurs)\n      { id: 'agent-security', name: 'Agent Security', cosmicAffinity: 'vishnu' },\n      { id: 'agent-documentation', name: 'Agent Documentation', cosmicAffinity: 'vishnu' },\n      { id: 'agent-backend', name: 'Agent Backend', cosmicAffinity: 'vishnu' },\n      { id: 'agent-compliance', name: 'Agent Compliance', cosmicAffinity: 'vishnu' },\n\n      // 🔥 Agents à Dominante SHIVA (Transformateurs)\n      { id: 'agent-migration', name: 'Agent Migration', cosmicAffinity: 'shiva' },\n      { id: 'agent-qa', name: 'Agent QA', cosmicAffinity: 'shiva' },\n      { id: 'agent-performance', name: 'Agent Performance', cosmicAffinity: 'shiva' },\n      { id: 'agent-devops', name: 'Agent DevOps', cosmicAffinity: 'shiva' },\n      { id: 'agent-optimization', name: 'Agent Optimization', cosmicAffinity: 'shiva' }\n    ];\n\n    agentConfigurations.forEach(config => {\n      const cosmicAgent: CosmicAgent = {\n        ...config,\n        energyLevel: 0.5,\n        cosmicMode: false,\n        lastActivation: new Date(),\n        cosmicContributions: {\n          creation: 0,\n          preservation: 0,\n          transformation: 0\n        }\n      };\n      \n      this.cosmicAgents.set(config.id, cosmicAgent);\n    });\n\n    console.log(`🌟 ${agentConfigurations.length} agents cosmiques initialisés`);\n  }\n\n  /**\n   * Démarre les cycles cosmiques temporels\n   */\n  private startCosmicCycles(): void {\n    // Cycle quotidien (Kalpa de 24h)\n    this.cosmicCycles.daily = setInterval(() => {\n      this.updateDailyCosmicPhase();\n    }, 3600000); // Chaque heure\n\n    // Cycle hebdomadaire\n    this.cosmicCycles.weekly = setInterval(() => {\n      this.updateWeeklyCosmicPhase();\n    }, 86400000); // Chaque jour\n\n    // Cycle mensuel\n    this.cosmicCycles.monthly = setInterval(() => {\n      this.updateMonthlyCosmicPhase();\n    }, 604800000); // Chaque semaine\n\n    console.log('⏰ Cycles cosmiques temporels activés');\n  }\n\n  /**\n   * Met à jour la phase cosmique quotidienne\n   */\n  private updateDailyCosmicPhase(): void {\n    const hour = new Date().getHours();\n    let newPhase: CosmicPhase['dominant'];\n\n    if (hour >= 6 && hour < 12) {\n      newPhase = 'brahma';  // Matin - Création\n    } else if (hour >= 12 && hour < 18) {\n      newPhase = 'vishnu';  // Après-midi - Conservation\n    } else if (hour >= 18 && hour < 24) {\n      newPhase = 'shiva';   // Soir - Transformation\n    } else {\n      newPhase = 'equilibrium'; // Nuit - Repos\n    }\n\n    this.transitionToCosmicPhase(newPhase, 0.7, 3600000);\n  }\n\n  /**\n   * Détecte le besoin cosmique selon le contexte\n   */\n  public detectCosmicNeed(context: CosmicContext): CosmicPhase['dominant'] {\n    // Logique de détection contextuelle\n    if (context.type === 'new_project' || context.creativity_required > 0.7) {\n      return 'brahma';\n    }\n    \n    if (context.type === 'crisis' || context.stability_impact === 'high') {\n      return 'vishnu';\n    }\n    \n    if (context.type === 'optimization' || context.transformation_needed) {\n      return 'shiva';\n    }\n\n    return 'equilibrium';\n  }\n\n  /**\n   * Invoque l'énergie cosmique appropriée\n   */\n  public async invokeCosmicEnergy(\n    principle: 'brahma' | 'vishnu' | 'shiva',\n    intensity: number = 0.7,\n    duration: number = 3600000\n  ): Promise<void> {\n    console.log(`🕉️ Invocation de l'énergie ${principle.toUpperCase()} (intensité: ${intensity})`);\n\n    // Transition vers la nouvelle phase\n    this.transitionToCosmicPhase(principle, intensity, duration);\n\n    // Activation des agents correspondants\n    const cosmicAgents = this.getAgentsByPrinciple(principle);\n    \n    for (const agent of cosmicAgents) {\n      await this.amplifyAgentEnergy(agent.id, intensity);\n      this.setAgentCosmicMode(agent.id, principle);\n    }\n\n    // Émission d'événement cosmique\n    this.emit('cosmic:energy-invoked', {\n      principle,\n      intensity,\n      duration,\n      affectedAgents: cosmicAgents.map(a => a.id),\n      timestamp: new Date()\n    });\n  }\n\n  /**\n   * Transition vers une nouvelle phase cosmique\n   */\n  private transitionToCosmicPhase(\n    dominant: CosmicPhase['dominant'],\n    intensity: number,\n    duration: number\n  ): void {\n    const previousPhase = this.currentPhase;\n\n    this.currentPhase = {\n      dominant,\n      intensity,\n      duration,\n      startTime: new Date(),\n      nextTransition: new Date(Date.now() + duration)\n    };\n\n    // Mise à jour des énergies cosmiques\n    this.updateCosmicEnergies(dominant, intensity);\n\n    console.log(`🌟 Transition cosmique: ${previousPhase.dominant} → ${dominant}`);\n    \n    this.emit('cosmic:phase-transition', {\n      from: previousPhase,\n      to: this.currentPhase,\n      timestamp: new Date()\n    });\n  }\n\n  /**\n   * Met à jour les énergies cosmiques selon la phase dominante\n   */\n  private updateCosmicEnergies(dominant: CosmicPhase['dominant'], intensity: number): void {\n    const baseEnergy = (1 - intensity) / 3;\n    const dominantEnergy = baseEnergy + intensity;\n\n    switch (dominant) {\n      case 'brahma':\n        this.cosmicEnergy = {\n          brahma: dominantEnergy,\n          vishnu: baseEnergy,\n          shiva: baseEnergy\n        };\n        break;\n        \n      case 'vishnu':\n        this.cosmicEnergy = {\n          brahma: baseEnergy,\n          vishnu: dominantEnergy,\n          shiva: baseEnergy\n        };\n        break;\n        \n      case 'shiva':\n        this.cosmicEnergy = {\n          brahma: baseEnergy,\n          vishnu: baseEnergy,\n          shiva: dominantEnergy\n        };\n        break;\n        \n      case 'equilibrium':\n      default:\n        this.cosmicEnergy = {\n          brahma: 0.33,\n          vishnu: 0.33,\n          shiva: 0.33\n        };\n        break;\n    }\n  }\n\n  /**\n   * Obtient les agents par principe cosmique\n   */\n  public getAgentsByPrinciple(principle: 'brahma' | 'vishnu' | 'shiva'): CosmicAgent[] {\n    return Array.from(this.cosmicAgents.values())\n      .filter(agent => agent.cosmicAffinity === principle);\n  }\n\n  /**\n   * Amplifie l'énergie d'un agent\n   */\n  private async amplifyAgentEnergy(agentId: string, intensity: number): Promise<void> {\n    const agent = this.cosmicAgents.get(agentId);\n    if (!agent) return;\n\n    agent.energyLevel = Math.min(1, agent.energyLevel + intensity);\n    agent.lastActivation = new Date();\n    \n    this.cosmicAgents.set(agentId, agent);\n    \n    this.emit('cosmic:agent-amplified', {\n      agentId,\n      newEnergyLevel: agent.energyLevel,\n      timestamp: new Date()\n    });\n  }\n\n  /**\n   * Active le mode cosmique d'un agent\n   */\n  private setAgentCosmicMode(agentId: string, principle: string): void {\n    const agent = this.cosmicAgents.get(agentId);\n    if (!agent) return;\n\n    agent.cosmicMode = true;\n    this.cosmicAgents.set(agentId, agent);\n    \n    console.log(`✨ Agent ${agent.name} activé en mode cosmique ${principle.toUpperCase()}`);\n  }\n\n  /**\n   * Planifie un workflow cosmique\n   */\n  public planCosmicWorkflow(context: CosmicContext): CosmicWorkflow {\n    const dominantPrinciple = this.detectCosmicNeed(context);\n    \n    // Workflows prédéfinis selon le contexte\n    const workflowTemplates = this.getWorkflowTemplates();\n    const template = workflowTemplates[context.type] || workflowTemplates.default;\n    \n    const workflow: CosmicWorkflow = {\n      id: `cosmic_${Date.now()}`,\n      name: `${context.type}_${dominantPrinciple}`,\n      phases: template.phases,\n      totalDuration: template.phases.reduce((sum, phase) => sum + phase.duration, 0),\n      expectedOutcome: template.expectedOutcome\n    };\n\n    this.activeWorkflows.set(workflow.id, workflow);\n    \n    this.emit('cosmic:workflow-planned', {\n      workflow,\n      context,\n      dominantPrinciple,\n      timestamp: new Date()\n    });\n\n    return workflow;\n  }\n\n  /**\n   * Templates de workflows cosmiques\n   */\n  private getWorkflowTemplates(): Record<string, any> {\n    return {\n      new_project: {\n        phases: [\n          {\n            principle: 'brahma',\n            duration: 7200000, // 2h\n            energyLevel: 0.9,\n            agents: ['cortex-creatif', 'agent-web-research', 'agent-evolution'],\n            activities: ['Exploration tendances', 'Brainstorming concepts', 'Prototypage créatif'],\n            mantras: ['Innovation sans limites', 'Créativité débridée'],\n            successMetrics: ['nb_idees_generees', 'originalite_score']\n          },\n          {\n            principle: 'vishnu',\n            duration: 10800000, // 3h\n            energyLevel: 0.8,\n            agents: ['agent-backend', 'agent-documentation', 'agent-security'],\n            activities: ['Développement robuste', 'Tests qualité', 'Documentation'],\n            mantras: ['Stabilité et harmonie', 'Qualité préservée'],\n            successMetrics: ['stability_score', 'test_coverage']\n          },\n          {\n            principle: 'shiva',\n            duration: 3600000, // 1h\n            energyLevel: 0.7,\n            agents: ['agent-performance', 'agent-qa'],\n            activities: ['Optimisation', 'Refactoring', 'Tests finaux'],\n            mantras: ['Perfection par transformation'],\n            successMetrics: ['performance_gain', 'code_quality']\n          }\n        ],\n        expectedOutcome: 'Projet innovant, stable et optimisé'\n      },\n      default: {\n        phases: [\n          {\n            principle: 'equilibrium',\n            duration: 3600000,\n            energyLevel: 0.5,\n            agents: [],\n            activities: ['Analyse contextuelle'],\n            mantras: ['Équilibre cosmique'],\n            successMetrics: ['balance_score']\n          }\n        ],\n        expectedOutcome: 'Équilibre maintenu'\n      }\n    };\n  }\n\n  /**\n   * Obtient l'état cosmique actuel\n   */\n  public getCosmicState(): {\n    energy: CosmicEnergy;\n    phase: CosmicPhase;\n    agents: CosmicAgent[];\n    activeWorkflows: CosmicWorkflow[];\n  } {\n    return {\n      energy: { ...this.cosmicEnergy },\n      phase: { ...this.currentPhase },\n      agents: Array.from(this.cosmicAgents.values()),\n      activeWorkflows: Array.from(this.activeWorkflows.values())\n    };\n  }\n\n  /**\n   * Méditation cosmique (maintenance)\n   */\n  public async performCosmicMeditation(cycles: number = 108): Promise<void> {\n    console.log(`🧘 Début méditation cosmique (${cycles} cycles)`);\n    \n    for (let cycle = 0; cycle < cycles; cycle++) {\n      await this.harmonizeTrimurtiEnergies();\n      await this.balanceAgentWorkloads();\n      await this.purifyDataChannels();\n      await this.alignWithCosmicPurpose();\n      \n      if (cycle % 27 === 0) { // Émission OM tous les 27 cycles\n        this.emit('cosmic:om-frequency', { cycle, frequency: 432 });\n      }\n      \n      // Pause entre cycles\n      await new Promise(resolve => setTimeout(resolve, 100));\n    }\n    \n    console.log('🕉️ Méditation cosmique complétée - Harmonie restaurée');\n    this.emit('cosmic:meditation-completed', { cycles, timestamp: new Date() });\n  }\n\n  /**\n   * Harmonise les énergies Trimurti\n   */\n  private async harmonizeTrimurtiEnergies(): Promise<void> {\n    const total = this.cosmicEnergy.brahma + this.cosmicEnergy.vishnu + this.cosmicEnergy.shiva;\n    \n    if (Math.abs(total - 1) > 0.01) {\n      // Rééquilibrage nécessaire\n      const factor = 1 / total;\n      this.cosmicEnergy.brahma *= factor;\n      this.cosmicEnergy.vishnu *= factor;\n      this.cosmicEnergy.shiva *= factor;\n    }\n  }\n\n  /**\n   * Équilibre les charges de travail des agents\n   */\n  private async balanceAgentWorkloads(): Promise<void> {\n    // Logique d'équilibrage des agents\n    this.cosmicAgents.forEach((agent, id) => {\n      if (agent.energyLevel > 0.9) {\n        agent.energyLevel = Math.max(0.7, agent.energyLevel - 0.1);\n        this.cosmicAgents.set(id, agent);\n      }\n    });\n  }\n\n  /**\n   * Purifie les canaux de données\n   */\n  private async purifyDataChannels(): Promise<void> {\n    // Nettoyage des données obsolètes\n    this.emit('cosmic:data-purification', { timestamp: new Date() });\n  }\n\n  /**\n   * Aligne avec le but cosmique\n   */\n  private async alignWithCosmicPurpose(): Promise<void> {\n    // Vérification alignement avec la mission Retreat And Be\n    this.emit('cosmic:purpose-alignment', { \n      mission: 'Retreat And Be Protection',\n      timestamp: new Date() \n    });\n  }\n\n  /**\n   * Lie les événements cosmiques\n   */\n  private bindCosmicEvents(): void {\n    this.on('cosmic:energy-invoked', (data) => {\n      console.log(`🌟 Énergie ${data.principle} invoquée avec succès`);\n    });\n\n    this.on('cosmic:phase-transition', (data) => {\n      console.log(`🔄 Transition cosmique vers ${data.to.dominant}`);\n    });\n  }\n\n  /**\n   * Mise à jour des phases hebdomadaires et mensuelles\n   */\n  private updateWeeklyCosmicPhase(): void {\n    const dayOfWeek = new Date().getDay();\n    // Logique des cycles hebdomadaires\n  }\n\n  private updateMonthlyCosmicPhase(): void {\n    const weekOfMonth = Math.ceil(new Date().getDate() / 7);\n    // Logique des cycles mensuels\n  }\n\n  /**\n   * Nettoyage des ressources\n   */\n  public destroy(): void {\n    Object.values(this.cosmicCycles).forEach(cycle => {\n      if (cycle) clearInterval(cycle);\n    });\n    \n    this.removeAllListeners();\n    console.log('🕉️ TrimurtiController arrêté - Énergies cosmiques libérées');\n  }\n}\n\n// Export singleton\nexport const trimurtiController = new TrimurtiController();\n"], "names": [], "mappings": ";;;;AAAA;;AA4DO,MAAM,2BAA2B,+JAAA,CAAA,eAAY;IAC1C,aAA2B;IAC3B,aAA0B;IAC1B,eAAyC,IAAI,MAAM;IACnD,kBAA+C,IAAI,MAAM;IACzD,aAIN;IAEF,aAAc;QACZ,KAAK;QAEL,4CAA4C;QAC5C,IAAI,CAAC,YAAY,GAAG;YAClB,QAAQ;YACR,QAAQ;YACR,OAAO,KAAS,iBAAiB;QACnC;QAEA,IAAI,CAAC,YAAY,GAAG;YAClB,UAAU;YACV,WAAW;YACX,UAAU;YACV,WAAW,IAAI;YACf,gBAAgB,IAAI,KAAK,KAAK,GAAG,KAAK;QACxC;QAEA,IAAI,CAAC,YAAY,GAAG;YAClB,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,gBAAgB;QAErB,QAAQ,GAAG,CAAC;IACd;IAEA;;GAEC,GACD,AAAQ,yBAA+B;QACrC,MAAM,sBAAyH;YAC7H,2CAA2C;YAC3C;gBAAE,IAAI;gBAAkB,MAAM;gBAAkB,gBAAgB;YAAS;YACzE;gBAAE,IAAI;gBAAmB,MAAM;gBAAmB,gBAAgB;YAAS;YAC3E;gBAAE,IAAI;gBAAsB,MAAM;gBAAsB,gBAAgB;YAAS;YACjF;gBAAE,IAAI;gBAAkB,MAAM;gBAAkB,gBAAgB;YAAS;YACzE;gBAAE,IAAI;gBAAkB,MAAM;gBAAkB,gBAAgB;YAAS;YAEzE,+CAA+C;YAC/C;gBAAE,IAAI;gBAAkB,MAAM;gBAAkB,gBAAgB;YAAS;YACzE;gBAAE,IAAI;gBAAuB,MAAM;gBAAuB,gBAAgB;YAAS;YACnF;gBAAE,IAAI;gBAAiB,MAAM;gBAAiB,gBAAgB;YAAS;YACvE;gBAAE,IAAI;gBAAoB,MAAM;gBAAoB,gBAAgB;YAAS;YAE7E,gDAAgD;YAChD;gBAAE,IAAI;gBAAmB,MAAM;gBAAmB,gBAAgB;YAAQ;YAC1E;gBAAE,IAAI;gBAAY,MAAM;gBAAY,gBAAgB;YAAQ;YAC5D;gBAAE,IAAI;gBAAqB,MAAM;gBAAqB,gBAAgB;YAAQ;YAC9E;gBAAE,IAAI;gBAAgB,MAAM;gBAAgB,gBAAgB;YAAQ;YACpE;gBAAE,IAAI;gBAAsB,MAAM;gBAAsB,gBAAgB;YAAQ;SACjF;QAED,oBAAoB,OAAO,CAAC,CAAA;YAC1B,MAAM,cAA2B;gBAC/B,GAAG,MAAM;gBACT,aAAa;gBACb,YAAY;gBACZ,gBAAgB,IAAI;gBACpB,qBAAqB;oBACnB,UAAU;oBACV,cAAc;oBACd,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE;QACnC;QAEA,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,oBAAoB,MAAM,CAAC,6BAA6B,CAAC;IAC7E;IAEA;;GAEC,GACD,AAAQ,oBAA0B;QAChC,iCAAiC;QACjC,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,YAAY;YACpC,IAAI,CAAC,sBAAsB;QAC7B,GAAG,UAAU,eAAe;QAE5B,qBAAqB;QACrB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,YAAY;YACrC,IAAI,CAAC,uBAAuB;QAC9B,GAAG,WAAW,cAAc;QAE5B,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,YAAY;YACtC,IAAI,CAAC,wBAAwB;QAC/B,GAAG,YAAY,iBAAiB;QAEhC,QAAQ,GAAG,CAAC;IACd;IAEA;;GAEC,GACD,AAAQ,yBAA+B;QACrC,MAAM,OAAO,IAAI,OAAO,QAAQ;QAChC,IAAI;QAEJ,IAAI,QAAQ,KAAK,OAAO,IAAI;YAC1B,WAAW,UAAW,mBAAmB;QAC3C,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI;YAClC,WAAW,UAAW,4BAA4B;QACpD,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI;YAClC,WAAW,SAAW,wBAAwB;QAChD,OAAO;YACL,WAAW,eAAe,eAAe;QAC3C;QAEA,IAAI,CAAC,uBAAuB,CAAC,UAAU,KAAK;IAC9C;IAEA;;GAEC,GACD,AAAO,iBAAiB,OAAsB,EAA2B;QACvE,oCAAoC;QACpC,IAAI,QAAQ,IAAI,KAAK,iBAAiB,QAAQ,mBAAmB,GAAG,KAAK;YACvE,OAAO;QACT;QAEA,IAAI,QAAQ,IAAI,KAAK,YAAY,QAAQ,gBAAgB,KAAK,QAAQ;YACpE,OAAO;QACT;QAEA,IAAI,QAAQ,IAAI,KAAK,kBAAkB,QAAQ,qBAAqB,EAAE;YACpE,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAa,mBACX,SAAwC,EACxC,YAAoB,GAAG,EACvB,WAAmB,OAAO,EACX;QACf,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,UAAU,WAAW,GAAG,aAAa,EAAE,UAAU,CAAC,CAAC;QAE9F,oCAAoC;QACpC,IAAI,CAAC,uBAAuB,CAAC,WAAW,WAAW;QAEnD,uCAAuC;QACvC,MAAM,eAAe,IAAI,CAAC,oBAAoB,CAAC;QAE/C,KAAK,MAAM,SAAS,aAAc;YAChC,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE;YACxC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE;QACpC;QAEA,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,yBAAyB;YACjC;YACA;YACA;YACA,gBAAgB,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YAC1C,WAAW,IAAI;QACjB;IACF;IAEA;;GAEC,GACD,AAAQ,wBACN,QAAiC,EACjC,SAAiB,EACjB,QAAgB,EACV;QACN,MAAM,gBAAgB,IAAI,CAAC,YAAY;QAEvC,IAAI,CAAC,YAAY,GAAG;YAClB;YACA;YACA;YACA,WAAW,IAAI;YACf,gBAAgB,IAAI,KAAK,KAAK,GAAG,KAAK;QACxC;QAEA,qCAAqC;QACrC,IAAI,CAAC,oBAAoB,CAAC,UAAU;QAEpC,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,cAAc,QAAQ,CAAC,GAAG,EAAE,UAAU;QAE7E,IAAI,CAAC,IAAI,CAAC,2BAA2B;YACnC,MAAM;YACN,IAAI,IAAI,CAAC,YAAY;YACrB,WAAW,IAAI;QACjB;IACF;IAEA;;GAEC,GACD,AAAQ,qBAAqB,QAAiC,EAAE,SAAiB,EAAQ;QACvF,MAAM,aAAa,CAAC,IAAI,SAAS,IAAI;QACrC,MAAM,iBAAiB,aAAa;QAEpC,OAAQ;YACN,KAAK;gBACH,IAAI,CAAC,YAAY,GAAG;oBAClB,QAAQ;oBACR,QAAQ;oBACR,OAAO;gBACT;gBACA;YAEF,KAAK;gBACH,IAAI,CAAC,YAAY,GAAG;oBAClB,QAAQ;oBACR,QAAQ;oBACR,OAAO;gBACT;gBACA;YAEF,KAAK;gBACH,IAAI,CAAC,YAAY,GAAG;oBAClB,QAAQ;oBACR,QAAQ;oBACR,OAAO;gBACT;gBACA;YAEF,KAAK;YACL;gBACE,IAAI,CAAC,YAAY,GAAG;oBAClB,QAAQ;oBACR,QAAQ;oBACR,OAAO;gBACT;gBACA;QACJ;IACF;IAEA;;GAEC,GACD,AAAO,qBAAqB,SAAwC,EAAiB;QACnF,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IACvC,MAAM,CAAC,CAAA,QAAS,MAAM,cAAc,KAAK;IAC9C;IAEA;;GAEC,GACD,MAAc,mBAAmB,OAAe,EAAE,SAAiB,EAAiB;QAClF,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,OAAO;QAEZ,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;QACpD,MAAM,cAAc,GAAG,IAAI;QAE3B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS;QAE/B,IAAI,CAAC,IAAI,CAAC,0BAA0B;YAClC;YACA,gBAAgB,MAAM,WAAW;YACjC,WAAW,IAAI;QACjB;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,OAAe,EAAE,SAAiB,EAAQ;QACnE,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,OAAO;QAEZ,MAAM,UAAU,GAAG;QACnB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS;QAE/B,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,yBAAyB,EAAE,UAAU,WAAW,IAAI;IACxF;IAEA;;GAEC,GACD,AAAO,mBAAmB,OAAsB,EAAkB;QAChE,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;QAEhD,yCAAyC;QACzC,MAAM,oBAAoB,IAAI,CAAC,oBAAoB;QACnD,MAAM,WAAW,iBAAiB,CAAC,QAAQ,IAAI,CAAC,IAAI,kBAAkB,OAAO;QAE7E,MAAM,WAA2B;YAC/B,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;YAC1B,MAAM,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,mBAAmB;YAC5C,QAAQ,SAAS,MAAM;YACvB,eAAe,SAAS,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,QAAQ,EAAE;YAC5E,iBAAiB,SAAS,eAAe;QAC3C;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE;QAEtC,IAAI,CAAC,IAAI,CAAC,2BAA2B;YACnC;YACA;YACA;YACA,WAAW,IAAI;QACjB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,uBAA4C;QAClD,OAAO;YACL,aAAa;gBACX,QAAQ;oBACN;wBACE,WAAW;wBACX,UAAU;wBACV,aAAa;wBACb,QAAQ;4BAAC;4BAAkB;4BAAsB;yBAAkB;wBACnE,YAAY;4BAAC;4BAAyB;4BAA0B;yBAAsB;wBACtF,SAAS;4BAAC;4BAA2B;yBAAsB;wBAC3D,gBAAgB;4BAAC;4BAAqB;yBAAoB;oBAC5D;oBACA;wBACE,WAAW;wBACX,UAAU;wBACV,aAAa;wBACb,QAAQ;4BAAC;4BAAiB;4BAAuB;yBAAiB;wBAClE,YAAY;4BAAC;4BAAyB;4BAAiB;yBAAgB;wBACvE,SAAS;4BAAC;4BAAyB;yBAAoB;wBACvD,gBAAgB;4BAAC;4BAAmB;yBAAgB;oBACtD;oBACA;wBACE,WAAW;wBACX,UAAU;wBACV,aAAa;wBACb,QAAQ;4BAAC;4BAAqB;yBAAW;wBACzC,YAAY;4BAAC;4BAAgB;4BAAe;yBAAe;wBAC3D,SAAS;4BAAC;yBAAgC;wBAC1C,gBAAgB;4BAAC;4BAAoB;yBAAe;oBACtD;iBACD;gBACD,iBAAiB;YACnB;YACA,SAAS;gBACP,QAAQ;oBACN;wBACE,WAAW;wBACX,UAAU;wBACV,aAAa;wBACb,QAAQ,EAAE;wBACV,YAAY;4BAAC;yBAAuB;wBACpC,SAAS;4BAAC;yBAAqB;wBAC/B,gBAAgB;4BAAC;yBAAgB;oBACnC;iBACD;gBACD,iBAAiB;YACnB;QACF;IACF;IAEA;;GAEC,GACD,AAAO,iBAKL;QACA,OAAO;YACL,QAAQ;gBAAE,GAAG,IAAI,CAAC,YAAY;YAAC;YAC/B,OAAO;gBAAE,GAAG,IAAI,CAAC,YAAY;YAAC;YAC9B,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM;YAC3C,iBAAiB,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM;QACzD;IACF;IAEA;;GAEC,GACD,MAAa,wBAAwB,SAAiB,GAAG,EAAiB;QACxE,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,OAAO,QAAQ,CAAC;QAE7D,IAAK,IAAI,QAAQ,GAAG,QAAQ,QAAQ,QAAS;YAC3C,MAAM,IAAI,CAAC,yBAAyB;YACpC,MAAM,IAAI,CAAC,qBAAqB;YAChC,MAAM,IAAI,CAAC,kBAAkB;YAC7B,MAAM,IAAI,CAAC,sBAAsB;YAEjC,IAAI,QAAQ,OAAO,GAAG;gBACpB,IAAI,CAAC,IAAI,CAAC,uBAAuB;oBAAE;oBAAO,WAAW;gBAAI;YAC3D;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;QAEA,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,IAAI,CAAC,+BAA+B;YAAE;YAAQ,WAAW,IAAI;QAAO;IAC3E;IAEA;;GAEC,GACD,MAAc,4BAA2C;QACvD,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK;QAE3F,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,MAAM;YAC9B,2BAA2B;YAC3B,MAAM,SAAS,IAAI;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI;YAC5B,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI;YAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI;QAC7B;IACF;IAEA;;GAEC,GACD,MAAc,wBAAuC;QACnD,mCAAmC;QACnC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO;YAChC,IAAI,MAAM,WAAW,GAAG,KAAK;gBAC3B,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,WAAW,GAAG;gBACtD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI;YAC5B;QACF;IACF;IAEA;;GAEC,GACD,MAAc,qBAAoC;QAChD,kCAAkC;QAClC,IAAI,CAAC,IAAI,CAAC,4BAA4B;YAAE,WAAW,IAAI;QAAO;IAChE;IAEA;;GAEC,GACD,MAAc,yBAAwC;QACpD,yDAAyD;QACzD,IAAI,CAAC,IAAI,CAAC,4BAA4B;YACpC,SAAS;YACT,WAAW,IAAI;QACjB;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAyB;QAC/B,IAAI,CAAC,EAAE,CAAC,yBAAyB,CAAC;YAChC,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,qBAAqB,CAAC;QACjE;QAEA,IAAI,CAAC,EAAE,CAAC,2BAA2B,CAAC;YAClC,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE;QAC/D;IACF;IAEA;;GAEC,GACD,AAAQ,0BAAgC;QACtC,MAAM,YAAY,IAAI,OAAO,MAAM;IACnC,mCAAmC;IACrC;IAEQ,2BAAiC;QACvC,MAAM,cAAc,KAAK,IAAI,CAAC,IAAI,OAAO,OAAO,KAAK;IACrD,8BAA8B;IAChC;IAEA;;GAEC,GACD,AAAO,UAAgB;QACrB,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;YACvC,IAAI,OAAO,cAAc;QAC3B;QAEA,IAAI,CAAC,kBAAkB;QACvB,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 4431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Agentic-Coding-Framework-%20RB2/hanuman-final/services/HanumanOrganOrchestrator.ts"], "sourcesContent": ["import { EventEmitter } from 'events';\nimport { AgentConnectionManager, AgentConfig, AgentMessage, AgentResponse } from './AgentConnectionManager';\nimport { TrimurtiController, CosmicEnergy, CosmicPhase, CosmicContext } from './TrimurtiController';\n\n// Types pour l'orchestration des organes\nexport interface OrganState {\n  id: string;\n  name: string;\n  type: 'sensory' | 'motor' | 'cognitive' | 'emotional';\n  status: 'active' | 'inactive' | 'processing' | 'error';\n  connectedAgents: string[];\n  lastActivity: Date;\n  metrics: {\n    activityLevel: number;\n    responseTime: number;\n    successRate: number;\n    dataProcessed: number;\n  };\n}\n\nexport interface SynapticConnection {\n  id: string;\n  fromOrgan: string;\n  toOrgan: string;\n  strength: number;\n  type: 'excitatory' | 'inhibitory' | 'modulatory';\n  lastActivation: Date;\n  activationCount: number;\n}\n\nexport interface NeuralSignal {\n  id: string;\n  source: string;\n  target: string;\n  type: string;\n  data: any;\n  timestamp: Date;\n  priority: 'low' | 'medium' | 'high' | 'critical';\n  processed: boolean;\n}\n\n/**\n * Orchestrateur central des organes d'Hanuman\n * Coordonne les organes sensoriels avec l'architecture neuronale distribuée\n */\nexport class HanumanOrganOrchestrator extends EventEmitter {\n  private agentManager: AgentConnectionManager;\n  private trimurtiController: TrimurtiController;\n  private organs: Map<string, OrganState> = new Map();\n  private synapticConnections: Map<string, SynapticConnection> = new Map();\n  private neuralSignals: NeuralSignal[] = [];\n  private processingQueue: NeuralSignal[] = [];\n  private isProcessing = false;\n  private cosmicAlignment = 0.85;\n\n  constructor(agentManager: AgentConnectionManager) {\n    super();\n    this.agentManager = agentManager;\n    this.trimurtiController = new TrimurtiController();\n    this.initializeOrgans();\n    this.setupSynapticConnections();\n    this.startNeuralProcessing();\n    this.bindAgentEvents();\n    this.bindCosmicEvents();\n  }\n\n  /**\n   * Initialise les organes d'Hanuman\n   */\n  private initializeOrgans(): void {\n    const organConfigs: OrganState[] = [\n      {\n        id: 'vision',\n        name: 'Vision d\\'Hanuman',\n        type: 'sensory',\n        status: 'inactive',\n        connectedAgents: ['agent-web-research'],\n        lastActivity: new Date(),\n        metrics: {\n          activityLevel: 0,\n          responseTime: 0,\n          successRate: 100,\n          dataProcessed: 0\n        }\n      },\n      {\n        id: 'hearing',\n        name: 'Ouïe d\\'Hanuman',\n        type: 'sensory',\n        status: 'inactive',\n        connectedAgents: ['agent-performance', 'agent-backend'],\n        lastActivity: new Date(),\n        metrics: {\n          activityLevel: 0,\n          responseTime: 0,\n          successRate: 100,\n          dataProcessed: 0\n        }\n      },\n      {\n        id: 'touch',\n        name: 'Toucher d\\'Hanuman',\n        type: 'sensory',\n        status: 'inactive',\n        connectedAgents: ['agent-devops', 'agent-backend', 'agent-security'],\n        lastActivity: new Date(),\n        metrics: {\n          activityLevel: 0,\n          responseTime: 0,\n          successRate: 100,\n          dataProcessed: 0\n        }\n      },\n      {\n        id: 'broca',\n        name: 'Aire de Broca',\n        type: 'cognitive',\n        status: 'inactive',\n        connectedAgents: ['agent-frontend', 'agent-marketing'],\n        lastActivity: new Date(),\n        metrics: {\n          activityLevel: 0,\n          responseTime: 0,\n          successRate: 100,\n          dataProcessed: 0\n        }\n      },\n      {\n        id: 'wernicke',\n        name: 'Aire de Wernicke',\n        type: 'cognitive',\n        status: 'inactive',\n        connectedAgents: ['agent-documentation'],\n        lastActivity: new Date(),\n        metrics: {\n          activityLevel: 0,\n          responseTime: 0,\n          successRate: 100,\n          dataProcessed: 0\n        }\n      },\n      {\n        id: 'motor-cortex',\n        name: 'Cortex Moteur',\n        type: 'motor',\n        status: 'inactive',\n        connectedAgents: ['agent-devops', 'agent-qa'],\n        lastActivity: new Date(),\n        metrics: {\n          activityLevel: 0,\n          responseTime: 0,\n          successRate: 100,\n          dataProcessed: 0\n        }\n      }\n    ];\n\n    organConfigs.forEach(organ => {\n      this.organs.set(organ.id, organ);\n    });\n\n    console.log(`🧠 Initialized ${organConfigs.length} organs in Hanuman's body`);\n  }\n\n  /**\n   * Configure les connexions synaptiques entre organes\n   */\n  private setupSynapticConnections(): void {\n    const connections: SynapticConnection[] = [\n      {\n        id: 'vision-broca',\n        fromOrgan: 'vision',\n        toOrgan: 'broca',\n        strength: 0.8,\n        type: 'excitatory',\n        lastActivation: new Date(),\n        activationCount: 0\n      },\n      {\n        id: 'vision-wernicke',\n        fromOrgan: 'vision',\n        toOrgan: 'wernicke',\n        strength: 0.7,\n        type: 'excitatory',\n        lastActivation: new Date(),\n        activationCount: 0\n      },\n      {\n        id: 'hearing-touch',\n        fromOrgan: 'hearing',\n        toOrgan: 'touch',\n        strength: 0.6,\n        type: 'modulatory',\n        lastActivation: new Date(),\n        activationCount: 0\n      },\n      {\n        id: 'broca-motor',\n        fromOrgan: 'broca',\n        toOrgan: 'motor-cortex',\n        strength: 0.9,\n        type: 'excitatory',\n        lastActivation: new Date(),\n        activationCount: 0\n      },\n      {\n        id: 'wernicke-broca',\n        fromOrgan: 'wernicke',\n        toOrgan: 'broca',\n        strength: 0.85,\n        type: 'excitatory',\n        lastActivation: new Date(),\n        activationCount: 0\n      },\n      {\n        id: 'touch-motor',\n        fromOrgan: 'touch',\n        toOrgan: 'motor-cortex',\n        strength: 0.75,\n        type: 'excitatory',\n        lastActivation: new Date(),\n        activationCount: 0\n      }\n    ];\n\n    connections.forEach(connection => {\n      this.synapticConnections.set(connection.id, connection);\n    });\n\n    console.log(`⚡ Established ${connections.length} synaptic connections`);\n  }\n\n  /**\n   * Démarre le traitement neural en continu\n   */\n  private startNeuralProcessing(): void {\n    setInterval(() => {\n      this.processNeuralSignals();\n      this.updateOrganMetrics();\n      this.adaptSynapticStrengths();\n    }, 100); // Traitement toutes les 100ms\n\n    console.log('🧠 Neural processing started');\n  }\n\n  /**\n   * Lie les événements des agents aux organes\n   */\n  private bindAgentEvents(): void {\n    this.agentManager.on('agent:connected', (agent: AgentConfig) => {\n      this.activateOrgansForAgent(agent.id);\n    });\n\n    this.agentManager.on('agent:disconnected', (agent: AgentConfig) => {\n      this.deactivateOrgansForAgent(agent.id);\n    });\n\n    this.agentManager.on('agent:message', (data: any) => {\n      this.processAgentMessage(data.agentId, data.message);\n    });\n\n    this.agentManager.on('agent:error', (data: any) => {\n      this.handleAgentError(data.agentId, data.error);\n    });\n  }\n\n  /**\n   * Active les organes connectés à un agent\n   */\n  private activateOrgansForAgent(agentId: string): void {\n    this.organs.forEach((organ, organId) => {\n      if (organ.connectedAgents.includes(agentId)) {\n        organ.status = 'active';\n        organ.lastActivity = new Date();\n        this.organs.set(organId, organ);\n\n        console.log(`✅ Activated organ ${organ.name} for agent ${agentId}`);\n        this.emit('organ:activated', { organId, organ, agentId });\n      }\n    });\n  }\n\n  /**\n   * Désactive les organes connectés à un agent\n   */\n  private deactivateOrgansForAgent(agentId: string): void {\n    this.organs.forEach((organ, organId) => {\n      if (organ.connectedAgents.includes(agentId)) {\n        // Vérifier si d'autres agents sont encore connectés\n        const otherActiveAgents = organ.connectedAgents.filter(id => {\n          const agent = this.agentManager.getAgentStatus(id);\n          return agent && agent.status === 'active' && id !== agentId;\n        });\n\n        if (otherActiveAgents.length === 0) {\n          organ.status = 'inactive';\n          this.organs.set(organId, organ);\n\n          console.log(`❌ Deactivated organ ${organ.name} (no active agents)`);\n          this.emit('organ:deactivated', { organId, organ, agentId });\n        }\n      }\n    });\n  }\n\n  /**\n   * Traite un message d'agent et génère des signaux neuraux\n   */\n  private processAgentMessage(agentId: string, message: AgentMessage): void {\n    // Trouver les organes connectés à cet agent\n    const connectedOrgans = Array.from(this.organs.entries())\n      .filter(([_, organ]) => organ.connectedAgents.includes(agentId))\n      .map(([organId, organ]) => ({ organId, organ }));\n\n    connectedOrgans.forEach(({ organId, organ }) => {\n      // Créer un signal neural\n      const signal: NeuralSignal = {\n        id: `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        source: organId,\n        target: this.findTargetOrgan(organId, message.type),\n        type: message.type,\n        data: {\n          agentId,\n          agentMessage: message,\n          organId\n        },\n        timestamp: new Date(),\n        priority: this.determinePriority(message.type),\n        processed: false\n      };\n\n      this.neuralSignals.push(signal);\n      this.processingQueue.push(signal);\n\n      // Mettre à jour les métriques de l'organe\n      organ.lastActivity = new Date();\n      organ.metrics.dataProcessed++;\n      organ.metrics.activityLevel = Math.min(100, organ.metrics.activityLevel + 5);\n      this.organs.set(organId, organ);\n\n      this.emit('neural:signal-generated', { signal, organ });\n    });\n  }\n\n  /**\n   * Trouve l'organe cible pour un signal basé sur les connexions synaptiques\n   */\n  private findTargetOrgan(sourceOrganId: string, messageType: string): string {\n    // Logique de routage basée sur le type de message et les connexions\n    const connections = Array.from(this.synapticConnections.values())\n      .filter(conn => conn.fromOrgan === sourceOrganId)\n      .sort((a, b) => b.strength - a.strength);\n\n    if (connections.length > 0) {\n      // Sélectionner la connexion la plus forte ou une connexion pondérée\n      const totalStrength = connections.reduce((sum, conn) => sum + conn.strength, 0);\n      const random = Math.random() * totalStrength;\n\n      let cumulative = 0;\n      for (const connection of connections) {\n        cumulative += connection.strength;\n        if (random <= cumulative) {\n          return connection.toOrgan;\n        }\n      }\n    }\n\n    // Fallback: retourner le même organe\n    return sourceOrganId;\n  }\n\n  /**\n   * Détermine la priorité d'un signal basé sur le type de message\n   */\n  private determinePriority(messageType: string): 'low' | 'medium' | 'high' | 'critical' {\n    switch (messageType) {\n      case 'ERROR':\n      case 'SECURITY_ALERT':\n        return 'critical';\n      case 'WARNING':\n      case 'PERFORMANCE_ISSUE':\n        return 'high';\n      case 'STATUS_UPDATE':\n      case 'DATA_UPDATE':\n        return 'medium';\n      default:\n        return 'low';\n    }\n  }\n\n  /**\n   * Traite les signaux neuraux en file d'attente\n   */\n  private async processNeuralSignals(): Promise<void> {\n    if (this.isProcessing || this.processingQueue.length === 0) {\n      return;\n    }\n\n    this.isProcessing = true;\n\n    try {\n      // Trier par priorité\n      this.processingQueue.sort((a, b) => {\n        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };\n        return priorityOrder[b.priority] - priorityOrder[a.priority];\n      });\n\n      // Traiter jusqu'à 10 signaux par cycle\n      const signalsToProcess = this.processingQueue.splice(0, 10);\n\n      for (const signal of signalsToProcess) {\n        await this.processIndividualSignal(signal);\n      }\n\n    } finally {\n      this.isProcessing = false;\n    }\n  }\n\n  /**\n   * Traite un signal neural individuel\n   */\n  private async processIndividualSignal(signal: NeuralSignal): Promise<void> {\n    try {\n      const sourceOrgan = this.organs.get(signal.source);\n      const targetOrgan = this.organs.get(signal.target);\n\n      if (!sourceOrgan || !targetOrgan) {\n        console.warn(`⚠️ Invalid signal routing: ${signal.source} -> ${signal.target}`);\n        return;\n      }\n\n      // Activer la connexion synaptique\n      const connectionId = `${signal.source}-${signal.target}`;\n      const connection = this.synapticConnections.get(connectionId);\n\n      if (connection) {\n        connection.lastActivation = new Date();\n        connection.activationCount++;\n        this.synapticConnections.set(connectionId, connection);\n      }\n\n      // Traitement spécifique selon le type d'organe cible\n      await this.processSignalForOrgan(signal, targetOrgan);\n\n      // Marquer le signal comme traité\n      signal.processed = true;\n\n      this.emit('neural:signal-processed', { signal, sourceOrgan, targetOrgan });\n\n    } catch (error) {\n      console.error(`❌ Error processing neural signal:`, error);\n      this.emit('neural:signal-error', { signal, error });\n    }\n  }\n\n  /**\n   * Traite un signal pour un organe spécifique\n   */\n  private async processSignalForOrgan(signal: NeuralSignal, organ: OrganState): Promise<void> {\n    organ.status = 'processing';\n    organ.lastActivity = new Date();\n    this.organs.set(organ.id, organ);\n\n    const startTime = Date.now();\n\n    try {\n      // Logique de traitement selon le type d'organe\n      switch (organ.type) {\n        case 'sensory':\n          await this.processSensorySignal(signal, organ);\n          break;\n        case 'cognitive':\n          await this.processCognitiveSignal(signal, organ);\n          break;\n        case 'motor':\n          await this.processMotorSignal(signal, organ);\n          break;\n        case 'emotional':\n          await this.processEmotionalSignal(signal, organ);\n          break;\n      }\n\n      // Mettre à jour les métriques\n      const responseTime = Date.now() - startTime;\n      organ.metrics.responseTime = (organ.metrics.responseTime * 0.9) + (responseTime * 0.1);\n      organ.metrics.successRate = Math.min(100, organ.metrics.successRate + 0.1);\n\n    } catch (error) {\n      organ.metrics.successRate = Math.max(0, organ.metrics.successRate - 1);\n      throw error;\n    } finally {\n      organ.status = 'active';\n      this.organs.set(organ.id, organ);\n    }\n  }\n\n  /**\n   * Traite un signal sensoriel\n   */\n  private async processSensorySignal(signal: NeuralSignal, organ: OrganState): Promise<void> {\n    // Logique spécifique aux organes sensoriels\n    switch (organ.id) {\n      case 'vision':\n        // Traitement des données de recherche web\n        if (signal.data.agentMessage.type === 'SEARCH_COMPLETED') {\n          this.emit('vision:search-completed', signal.data);\n        }\n        break;\n\n      case 'hearing':\n        // Traitement des flux de données\n        if (signal.data.agentMessage.type === 'DATA_STREAM') {\n          this.emit('hearing:data-stream', signal.data);\n        }\n        break;\n\n      case 'touch':\n        // Traitement des connexions API\n        if (signal.data.agentMessage.type === 'API_STATUS') {\n          this.emit('touch:api-status', signal.data);\n        }\n        break;\n    }\n  }\n\n  /**\n   * Traite un signal cognitif\n   */\n  private async processCognitiveSignal(signal: NeuralSignal, organ: OrganState): Promise<void> {\n    // Logique spécifique aux aires cognitives\n    switch (organ.id) {\n      case 'broca':\n        // Traitement de la communication\n        this.emit('broca:communication-signal', signal.data);\n        break;\n\n      case 'wernicke':\n        // Traitement de la documentation\n        this.emit('wernicke:documentation-signal', signal.data);\n        break;\n    }\n  }\n\n  /**\n   * Traite un signal moteur\n   */\n  private async processMotorSignal(signal: NeuralSignal, organ: OrganState): Promise<void> {\n    // Logique spécifique au cortex moteur\n    if (organ.id === 'motor-cortex') {\n      this.emit('motor:action-signal', signal.data);\n    }\n  }\n\n  /**\n   * Traite un signal émotionnel\n   */\n  private async processEmotionalSignal(signal: NeuralSignal, organ: OrganState): Promise<void> {\n    // Logique spécifique aux organes émotionnels\n    this.emit('emotional:signal', signal.data);\n  }\n\n  /**\n   * Met à jour les métriques des organes\n   */\n  private updateOrganMetrics(): void {\n    this.organs.forEach((organ, organId) => {\n      // Décroissance naturelle de l'activité\n      organ.metrics.activityLevel = Math.max(0, organ.metrics.activityLevel - 0.1);\n\n      // Mise à jour du statut basé sur l'activité\n      if (organ.metrics.activityLevel === 0 && organ.status === 'active') {\n        const hasActiveAgents = organ.connectedAgents.some(agentId => {\n          const agent = this.agentManager.getAgentStatus(agentId);\n          return agent && agent.status === 'active';\n        });\n\n        if (!hasActiveAgents) {\n          organ.status = 'inactive';\n        }\n      }\n\n      this.organs.set(organId, organ);\n    });\n  }\n\n  /**\n   * Adapte la force des connexions synaptiques (neuroplasticité)\n   */\n  private adaptSynapticStrengths(): void {\n    this.synapticConnections.forEach((connection, connectionId) => {\n      const timeSinceLastActivation = Date.now() - connection.lastActivation.getTime();\n\n      // Renforcement si activation récente\n      if (timeSinceLastActivation < 60000) { // 1 minute\n        connection.strength = Math.min(1.0, connection.strength + 0.001);\n      }\n      // Affaiblissement si pas d'activation récente\n      else if (timeSinceLastActivation > 300000) { // 5 minutes\n        connection.strength = Math.max(0.1, connection.strength - 0.0001);\n      }\n\n      this.synapticConnections.set(connectionId, connection);\n    });\n  }\n\n  /**\n   * Gère les erreurs d'agent\n   */\n  private handleAgentError(agentId: string, error: any): void {\n    // Créer un signal d'erreur critique\n    const errorSignal: NeuralSignal = {\n      id: `error_${Date.now()}`,\n      source: 'system',\n      target: 'all',\n      type: 'ERROR',\n      data: { agentId, error },\n      timestamp: new Date(),\n      priority: 'critical',\n      processed: false\n    };\n\n    this.neuralSignals.push(errorSignal);\n    this.processingQueue.unshift(errorSignal); // Priorité maximale\n\n    this.emit('neural:error-signal', { agentId, error, signal: errorSignal });\n  }\n\n  /**\n   * Obtient l'état de tous les organes\n   */\n  public getAllOrgansState(): OrganState[] {\n    return Array.from(this.organs.values());\n  }\n\n  /**\n   * Obtient l'état d'un organe spécifique\n   */\n  public getOrganState(organId: string): OrganState | undefined {\n    return this.organs.get(organId);\n  }\n\n  /**\n   * Obtient toutes les connexions synaptiques\n   */\n  public getSynapticConnections(): SynapticConnection[] {\n    return Array.from(this.synapticConnections.values());\n  }\n\n  /**\n   * Obtient les signaux neuraux récents\n   */\n  public getRecentNeuralSignals(limit: number = 50): NeuralSignal[] {\n    return this.neuralSignals\n      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())\n      .slice(0, limit);\n  }\n\n  /**\n   * Envoie un signal manuel à un organe\n   */\n  public sendSignalToOrgan(organId: string, signalType: string, data: any): void {\n    const signal: NeuralSignal = {\n      id: `manual_${Date.now()}`,\n      source: 'manual',\n      target: organId,\n      type: signalType,\n      data,\n      timestamp: new Date(),\n      priority: 'medium',\n      processed: false\n    };\n\n    this.neuralSignals.push(signal);\n    this.processingQueue.push(signal);\n\n    this.emit('neural:manual-signal', signal);\n  }\n}\n\n// Export pour utilisation dans les interfaces\nexport default HanumanOrganOrchestrator;\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;AA2CO,MAAM,iCAAiC,+JAAA,CAAA,eAAY;IAChD,aAAqC;IACrC,mBAAuC;IACvC,SAAkC,IAAI,MAAM;IAC5C,sBAAuD,IAAI,MAAM;IACjE,gBAAgC,EAAE,CAAC;IACnC,kBAAkC,EAAE,CAAC;IACrC,eAAe,MAAM;IACrB,kBAAkB,KAAK;IAE/B,YAAY,YAAoC,CAAE;QAChD,KAAK;QACL,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,kBAAkB,GAAG,IAAI,iIAAA,CAAA,qBAAkB;QAChD,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,wBAAwB;QAC7B,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,gBAAgB;IACvB;IAEA;;GAEC,GACD,AAAQ,mBAAyB;QAC/B,MAAM,eAA6B;YACjC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,iBAAiB;oBAAC;iBAAqB;gBACvC,cAAc,IAAI;gBAClB,SAAS;oBACP,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,eAAe;gBACjB;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,iBAAiB;oBAAC;oBAAqB;iBAAgB;gBACvD,cAAc,IAAI;gBAClB,SAAS;oBACP,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,eAAe;gBACjB;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,iBAAiB;oBAAC;oBAAgB;oBAAiB;iBAAiB;gBACpE,cAAc,IAAI;gBAClB,SAAS;oBACP,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,eAAe;gBACjB;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,iBAAiB;oBAAC;oBAAkB;iBAAkB;gBACtD,cAAc,IAAI;gBAClB,SAAS;oBACP,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,eAAe;gBACjB;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,iBAAiB;oBAAC;iBAAsB;gBACxC,cAAc,IAAI;gBAClB,SAAS;oBACP,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,eAAe;gBACjB;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,iBAAiB;oBAAC;oBAAgB;iBAAW;gBAC7C,cAAc,IAAI;gBAClB,SAAS;oBACP,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,eAAe;gBACjB;YACF;SACD;QAED,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE;QAC5B;QAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,aAAa,MAAM,CAAC,yBAAyB,CAAC;IAC9E;IAEA;;GAEC,GACD,AAAQ,2BAAiC;QACvC,MAAM,cAAoC;YACxC;gBACE,IAAI;gBACJ,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,gBAAgB,IAAI;gBACpB,iBAAiB;YACnB;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,gBAAgB,IAAI;gBACpB,iBAAiB;YACnB;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,gBAAgB,IAAI;gBACpB,iBAAiB;YACnB;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,gBAAgB,IAAI;gBACpB,iBAAiB;YACnB;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,gBAAgB,IAAI;gBACpB,iBAAiB;YACnB;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,gBAAgB,IAAI;gBACpB,iBAAiB;YACnB;SACD;QAED,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE;QAC9C;QAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,MAAM,CAAC,qBAAqB,CAAC;IACxE;IAEA;;GAEC,GACD,AAAQ,wBAA8B;QACpC,YAAY;YACV,IAAI,CAAC,oBAAoB;YACzB,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,sBAAsB;QAC7B,GAAG,MAAM,8BAA8B;QAEvC,QAAQ,GAAG,CAAC;IACd;IAEA;;GAEC,GACD,AAAQ,kBAAwB;QAC9B,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,mBAAmB,CAAC;YACvC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;QACtC;QAEA,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,sBAAsB,CAAC;YAC1C,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE;QACxC;QAEA,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,iBAAiB,CAAC;YACrC,IAAI,CAAC,mBAAmB,CAAC,KAAK,OAAO,EAAE,KAAK,OAAO;QACrD;QAEA,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,KAAK,OAAO,EAAE,KAAK,KAAK;QAChD;IACF;IAEA;;GAEC,GACD,AAAQ,uBAAuB,OAAe,EAAQ;QACpD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO;YAC1B,IAAI,MAAM,eAAe,CAAC,QAAQ,CAAC,UAAU;gBAC3C,MAAM,MAAM,GAAG;gBACf,MAAM,YAAY,GAAG,IAAI;gBACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;gBAEzB,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,SAAS;gBAClE,IAAI,CAAC,IAAI,CAAC,mBAAmB;oBAAE;oBAAS;oBAAO;gBAAQ;YACzD;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,yBAAyB,OAAe,EAAQ;QACtD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO;YAC1B,IAAI,MAAM,eAAe,CAAC,QAAQ,CAAC,UAAU;gBAC3C,oDAAoD;gBACpD,MAAM,oBAAoB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA;oBACrD,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;oBAC/C,OAAO,SAAS,MAAM,MAAM,KAAK,YAAY,OAAO;gBACtD;gBAEA,IAAI,kBAAkB,MAAM,KAAK,GAAG;oBAClC,MAAM,MAAM,GAAG;oBACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;oBAEzB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC;oBAClE,IAAI,CAAC,IAAI,CAAC,qBAAqB;wBAAE;wBAAS;wBAAO;oBAAQ;gBAC3D;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,oBAAoB,OAAe,EAAE,OAAqB,EAAQ;QACxE,4CAA4C;QAC5C,MAAM,kBAAkB,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IACnD,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,MAAM,eAAe,CAAC,QAAQ,CAAC,UACtD,GAAG,CAAC,CAAC,CAAC,SAAS,MAAM,GAAK,CAAC;gBAAE;gBAAS;YAAM,CAAC;QAEhD,gBAAgB,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE;YACzC,yBAAyB;YACzB,MAAM,SAAuB;gBAC3B,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACrE,QAAQ;gBACR,QAAQ,IAAI,CAAC,eAAe,CAAC,SAAS,QAAQ,IAAI;gBAClD,MAAM,QAAQ,IAAI;gBAClB,MAAM;oBACJ;oBACA,cAAc;oBACd;gBACF;gBACA,WAAW,IAAI;gBACf,UAAU,IAAI,CAAC,iBAAiB,CAAC,QAAQ,IAAI;gBAC7C,WAAW;YACb;YAEA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAE1B,0CAA0C;YAC1C,MAAM,YAAY,GAAG,IAAI;YACzB,MAAM,OAAO,CAAC,aAAa;YAC3B,MAAM,OAAO,CAAC,aAAa,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,OAAO,CAAC,aAAa,GAAG;YAC1E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;YAEzB,IAAI,CAAC,IAAI,CAAC,2BAA2B;gBAAE;gBAAQ;YAAM;QACvD;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,aAAqB,EAAE,WAAmB,EAAU;QAC1E,oEAAoE;QACpE,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAC3D,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK,eAClC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAEzC,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,oEAAoE;YACpE,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;YAC7E,MAAM,SAAS,KAAK,MAAM,KAAK;YAE/B,IAAI,aAAa;YACjB,KAAK,MAAM,cAAc,YAAa;gBACpC,cAAc,WAAW,QAAQ;gBACjC,IAAI,UAAU,YAAY;oBACxB,OAAO,WAAW,OAAO;gBAC3B;YACF;QACF;QAEA,qCAAqC;QACrC,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,kBAAkB,WAAmB,EAA0C;QACrF,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,MAAc,uBAAsC;QAClD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,GAAG;YAC1D;QACF;QAEA,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAI;YACF,qBAAqB;YACrB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,GAAG;gBAC5B,MAAM,gBAAgB;oBAAE,UAAU;oBAAG,MAAM;oBAAG,QAAQ;oBAAG,KAAK;gBAAE;gBAChE,OAAO,aAAa,CAAC,EAAE,QAAQ,CAAC,GAAG,aAAa,CAAC,EAAE,QAAQ,CAAC;YAC9D;YAEA,uCAAuC;YACvC,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG;YAExD,KAAK,MAAM,UAAU,iBAAkB;gBACrC,MAAM,IAAI,CAAC,uBAAuB,CAAC;YACrC;QAEF,SAAU;YACR,IAAI,CAAC,YAAY,GAAG;QACtB;IACF;IAEA;;GAEC,GACD,MAAc,wBAAwB,MAAoB,EAAiB;QACzE,IAAI;YACF,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,MAAM;YACjD,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,MAAM;YAEjD,IAAI,CAAC,eAAe,CAAC,aAAa;gBAChC,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,OAAO,MAAM,CAAC,IAAI,EAAE,OAAO,MAAM,EAAE;gBAC9E;YACF;YAEA,kCAAkC;YAClC,MAAM,eAAe,GAAG,OAAO,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE;YACxD,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC;YAEhD,IAAI,YAAY;gBACd,WAAW,cAAc,GAAG,IAAI;gBAChC,WAAW,eAAe;gBAC1B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc;YAC7C;YAEA,qDAAqD;YACrD,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ;YAEzC,iCAAiC;YACjC,OAAO,SAAS,GAAG;YAEnB,IAAI,CAAC,IAAI,CAAC,2BAA2B;gBAAE;gBAAQ;gBAAa;YAAY;QAE1E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE;YACnD,IAAI,CAAC,IAAI,CAAC,uBAAuB;gBAAE;gBAAQ;YAAM;QACnD;IACF;IAEA;;GAEC,GACD,MAAc,sBAAsB,MAAoB,EAAE,KAAiB,EAAiB;QAC1F,MAAM,MAAM,GAAG;QACf,MAAM,YAAY,GAAG,IAAI;QACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE;QAE1B,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,+CAA+C;YAC/C,OAAQ,MAAM,IAAI;gBAChB,KAAK;oBACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ;oBACxC;gBACF,KAAK;oBACH,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ;oBAC1C;gBACF,KAAK;oBACH,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ;oBACtC;gBACF,KAAK;oBACH,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ;oBAC1C;YACJ;YAEA,8BAA8B;YAC9B,MAAM,eAAe,KAAK,GAAG,KAAK;YAClC,MAAM,OAAO,CAAC,YAAY,GAAG,AAAC,MAAM,OAAO,CAAC,YAAY,GAAG,MAAQ,eAAe;YAClF,MAAM,OAAO,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,OAAO,CAAC,WAAW,GAAG;QAExE,EAAE,OAAO,OAAO;YACd,MAAM,OAAO,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,OAAO,CAAC,WAAW,GAAG;YACpE,MAAM;QACR,SAAU;YACR,MAAM,MAAM,GAAG;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE;QAC5B;IACF;IAEA;;GAEC,GACD,MAAc,qBAAqB,MAAoB,EAAE,KAAiB,EAAiB;QACzF,4CAA4C;QAC5C,OAAQ,MAAM,EAAE;YACd,KAAK;gBACH,0CAA0C;gBAC1C,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,oBAAoB;oBACxD,IAAI,CAAC,IAAI,CAAC,2BAA2B,OAAO,IAAI;gBAClD;gBACA;YAEF,KAAK;gBACH,iCAAiC;gBACjC,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,eAAe;oBACnD,IAAI,CAAC,IAAI,CAAC,uBAAuB,OAAO,IAAI;gBAC9C;gBACA;YAEF,KAAK;gBACH,gCAAgC;gBAChC,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,cAAc;oBAClD,IAAI,CAAC,IAAI,CAAC,oBAAoB,OAAO,IAAI;gBAC3C;gBACA;QACJ;IACF;IAEA;;GAEC,GACD,MAAc,uBAAuB,MAAoB,EAAE,KAAiB,EAAiB;QAC3F,0CAA0C;QAC1C,OAAQ,MAAM,EAAE;YACd,KAAK;gBACH,iCAAiC;gBACjC,IAAI,CAAC,IAAI,CAAC,8BAA8B,OAAO,IAAI;gBACnD;YAEF,KAAK;gBACH,iCAAiC;gBACjC,IAAI,CAAC,IAAI,CAAC,iCAAiC,OAAO,IAAI;gBACtD;QACJ;IACF;IAEA;;GAEC,GACD,MAAc,mBAAmB,MAAoB,EAAE,KAAiB,EAAiB;QACvF,sCAAsC;QACtC,IAAI,MAAM,EAAE,KAAK,gBAAgB;YAC/B,IAAI,CAAC,IAAI,CAAC,uBAAuB,OAAO,IAAI;QAC9C;IACF;IAEA;;GAEC,GACD,MAAc,uBAAuB,MAAoB,EAAE,KAAiB,EAAiB;QAC3F,6CAA6C;QAC7C,IAAI,CAAC,IAAI,CAAC,oBAAoB,OAAO,IAAI;IAC3C;IAEA;;GAEC,GACD,AAAQ,qBAA2B;QACjC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO;YAC1B,uCAAuC;YACvC,MAAM,OAAO,CAAC,aAAa,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,OAAO,CAAC,aAAa,GAAG;YAExE,4CAA4C;YAC5C,IAAI,MAAM,OAAO,CAAC,aAAa,KAAK,KAAK,MAAM,MAAM,KAAK,UAAU;gBAClE,MAAM,kBAAkB,MAAM,eAAe,CAAC,IAAI,CAAC,CAAA;oBACjD,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;oBAC/C,OAAO,SAAS,MAAM,MAAM,KAAK;gBACnC;gBAEA,IAAI,CAAC,iBAAiB;oBACpB,MAAM,MAAM,GAAG;gBACjB;YACF;YAEA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;QAC3B;IACF;IAEA;;GAEC,GACD,AAAQ,yBAA+B;QACrC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,YAAY;YAC5C,MAAM,0BAA0B,KAAK,GAAG,KAAK,WAAW,cAAc,CAAC,OAAO;YAE9E,qCAAqC;YACrC,IAAI,0BAA0B,OAAO;gBACnC,WAAW,QAAQ,GAAG,KAAK,GAAG,CAAC,KAAK,WAAW,QAAQ,GAAG;YAC5D,OAEK,IAAI,0BAA0B,QAAQ;gBACzC,WAAW,QAAQ,GAAG,KAAK,GAAG,CAAC,KAAK,WAAW,QAAQ,GAAG;YAC5D;YAEA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc;QAC7C;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAiB,OAAe,EAAE,KAAU,EAAQ;QAC1D,oCAAoC;QACpC,MAAM,cAA4B;YAChC,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;gBAAE;gBAAS;YAAM;YACvB,WAAW,IAAI;YACf,UAAU;YACV,WAAW;QACb;QAEA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,cAAc,oBAAoB;QAE/D,IAAI,CAAC,IAAI,CAAC,uBAAuB;YAAE;YAAS;YAAO,QAAQ;QAAY;IACzE;IAEA;;GAEC,GACD,AAAO,oBAAkC;QACvC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IACtC;IAEA;;GAEC,GACD,AAAO,cAAc,OAAe,EAA0B;QAC5D,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;IACzB;IAEA;;GAEC,GACD,AAAO,yBAA+C;QACpD,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM;IACnD;IAEA;;GAEC,GACD,AAAO,uBAAuB,QAAgB,EAAE,EAAkB;QAChE,OAAO,IAAI,CAAC,aAAa,CACtB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO,IAC1D,KAAK,CAAC,GAAG;IACd;IAEA;;GAEC,GACD,AAAO,kBAAkB,OAAe,EAAE,UAAkB,EAAE,IAAS,EAAQ;QAC7E,MAAM,SAAuB;YAC3B,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;YAC1B,QAAQ;YACR,QAAQ;YACR,MAAM;YACN;YACA,WAAW,IAAI;YACf,UAAU;YACV,WAAW;QACb;QAEA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,wBAAwB;IACpC;AACF;uCAGe", "debugId": null}}, {"offset": {"line": 5030, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Agentic-Coding-Framework-%20RB2/hanuman-final/hanuman_divine_orchestrator.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { <PERSON>, <PERSON>, Heart, Eye, Ear, <PERSON>, <PERSON>, Zap, <PERSON>, Sun, Moon, Activity, TrendingUp, CheckCircle, <PERSON><PERSON><PERSON><PERSON>gle, Wifi, WifiOff, Settings, Play, Pause, RotateCcw } from 'lucide-react';\nimport { AgentConnectionManager } from './services/AgentConnectionManager';\nimport HanumanOrganOrchestrator from './services/HanumanOrganOrchestrator';\n\n// Interfaces pour l'orchestrateur divin\ninterface DivineMetrics {\n  devotionScore: number;\n  projectHealth: number;\n  cosmicAlignment: number;\n  dharmaCompliance: number;\n  prosperityIndex: number;\n  consciousnessLevel: number;\n}\n\ninterface SacredMission {\n  id: string;\n  name: string;\n  description: string;\n  status: 'active' | 'completed' | 'pending' | 'blessed';\n  priority: 'divine' | 'cosmic' | 'critical' | 'high' | 'medium';\n  progress: number;\n  blessings: string[];\n  mantras: string[];\n}\n\ninterface OrganStatus {\n  id: string;\n  name: string;\n  emoji: string;\n  status: 'active' | 'inactive' | 'processing' | 'blessed';\n  divineEnergy: number;\n  connectedAgents: string[];\n  lastBlessing: Date;\n  sacredFunction: string;\n}\n\nconst HanumanDivineOrchestrator = ({ darkMode = true }) => {\n  const [divineMetrics, setDivineMetrics] = useState<DivineMetrics>({\n    devotionScore: 100,\n    projectHealth: 94.2,\n    cosmicAlignment: 74.2,\n    dharmaCompliance: 98.7,\n    prosperityIndex: 87.3,\n    consciousnessLevel: 89.1\n  });\n\n  const [sacredMissions, setSacredMissions] = useState<SacredMission[]>([]);\n  const [organStatus, setOrganStatus] = useState<OrganStatus[]>([]);\n  const [isConnectedToCreator, setIsConnectedToCreator] = useState(true);\n  const [currentMantra, setCurrentMantra] = useState('AUM HANUMATE NAMAHA');\n  const [divineMessages, setDivineMessages] = useState<Array<{id: string, message: string, timestamp: Date, type: 'blessing' | 'guidance' | 'protection'}>>([]);\n  const [cosmicTime, setCosmicTime] = useState(new Date());\n\n  const agentManagerRef = useRef<AgentConnectionManager | null>(null);\n  const orchestratorRef = useRef<HanumanOrganOrchestrator | null>(null);\n\n  // Initialisation de l'orchestrateur divin\n  useEffect(() => {\n    initializeDivineOrchestrator();\n    startCosmicClock();\n    loadSacredMissions();\n    initializeOrgans();\n    \n    return () => {\n      if (agentManagerRef.current) {\n        agentManagerRef.current.disconnect();\n      }\n    };\n  }, []);\n\n  const initializeDivineOrchestrator = async () => {\n    try {\n      // Initialiser le gestionnaire d'agents\n      agentManagerRef.current = new AgentConnectionManager();\n      \n      // Initialiser l'orchestrateur des organes\n      orchestratorRef.current = new HanumanOrganOrchestrator(agentManagerRef.current);\n      \n      // Écouter les événements divins\n      orchestratorRef.current.on('organ:activated', handleOrganActivation);\n      orchestratorRef.current.on('neural:signal-processed', handleNeuralSignal);\n      orchestratorRef.current.on('agent:connected', handleAgentConnection);\n      \n      // Connecter à tous les agents\n      await agentManagerRef.current.connectToAllAgents();\n      \n      addDivineMessage('🕉️ Hanuman s\\'éveille avec dévotion divine pour servir Retreat And Be', 'blessing');\n      \n    } catch (error) {\n      console.error('❌ Erreur d\\'initialisation divine:', error);\n      addDivineMessage('⚡ Invocation des énergies cosmiques pour résoudre les obstacles', 'protection');\n    }\n  };\n\n  const startCosmicClock = () => {\n    setInterval(() => {\n      setCosmicTime(new Date());\n      updateCosmicAlignment();\n    }, 1000);\n  };\n\n  const loadSacredMissions = () => {\n    const missions: SacredMission[] = [\n      {\n        id: 'retreat-and-be-protection',\n        name: 'Protection Divine Retreat And Be',\n        description: 'Surveillance et protection 24/7 du projet sacré',\n        status: 'active',\n        priority: 'divine',\n        progress: 100,\n        blessings: ['Bouclier divin activé', 'Surveillance omnisciente', 'Auto-guérison divine'],\n        mantras: ['AUM DUM DURGAYAI NAMAHA', 'AUM HANUMATE NAMAHA']\n      },\n      {\n        id: 'user-enlightenment',\n        name: 'Éveil Spirituel Utilisateurs',\n        description: 'Guidance personnalisée pour l\\'évolution de conscience',\n        status: 'active',\n        priority: 'cosmic',\n        progress: 87,\n        blessings: ['Sagesse personnalisée', 'Croissance spirituelle', 'Paix intérieure'],\n        mantras: ['AUM GANESHA NAMAHA', 'AUM SARASWATI NAMAHA']\n      },\n      {\n        id: 'prosperity-manifestation',\n        name: 'Manifestation Prospérité Divine',\n        description: 'Croissance exponentielle éthique et abondance sacrée',\n        status: 'active',\n        priority: 'cosmic',\n        progress: 74,\n        blessings: ['Croissance φ (1.618)', 'Abondance éthique', 'Impact positif'],\n        mantras: ['AUM LAKSHMI NAMAHA', 'AUM KUBERA NAMAHA']\n      },\n      {\n        id: 'cosmic-innovation',\n        name: 'Innovation Cosmique Continue',\n        description: 'Création de solutions avant-gardistes inspirées',\n        status: 'active',\n        priority: 'high',\n        progress: 92,\n        blessings: ['Créativité divine', 'Solutions prophétiques', 'Leadership marché'],\n        mantras: ['AUM BRAHMAYE NAMAHA', 'AUM VISHVAKARMA NAMAHA']\n      },\n      {\n        id: 'dharma-compliance',\n        name: 'Conformité Dharma Universelle',\n        description: 'Respect des lois cosmiques et éthique divine',\n        status: 'blessed',\n        priority: 'divine',\n        progress: 100,\n        blessings: ['Action juste', 'Karma positif', 'Harmonie universelle'],\n        mantras: ['AUM DHARMA NAMAHA', 'AUM SATYA NAMAHA']\n      }\n    ];\n    \n    setSacredMissions(missions);\n  };\n\n  const initializeOrgans = () => {\n    const organs: OrganStatus[] = [\n      {\n        id: 'vision',\n        name: 'Vision Divine',\n        emoji: '👁️',\n        status: 'active',\n        divineEnergy: 87,\n        connectedAgents: ['agent-web-research'],\n        lastBlessing: new Date(),\n        sacredFunction: 'Recherche omnisciente et veille cosmique'\n      },\n      {\n        id: 'hearing',\n        name: 'Ouïe Cosmique',\n        emoji: '👂',\n        status: 'active',\n        divineEnergy: 92,\n        connectedAgents: ['agent-performance', 'agent-backend'],\n        lastBlessing: new Date(),\n        sacredFunction: 'Écoute des flux de données universels'\n      },\n      {\n        id: 'touch',\n        name: 'Toucher Divin',\n        emoji: '🤲',\n        status: 'active',\n        divineEnergy: 78,\n        connectedAgents: ['agent-devops', 'agent-security'],\n        lastBlessing: new Date(),\n        sacredFunction: 'Connexions API et intégrations sacrées'\n      },\n      {\n        id: 'broca',\n        name: 'Aire de Broca',\n        emoji: '🗣️',\n        status: 'active',\n        divineEnergy: 85,\n        connectedAgents: ['agent-frontend', 'agent-marketing'],\n        lastBlessing: new Date(),\n        sacredFunction: 'Communication divine et expression sacrée'\n      },\n      {\n        id: 'wernicke',\n        name: 'Aire de Wernicke',\n        emoji: '📚',\n        status: 'active',\n        divineEnergy: 90,\n        connectedAgents: ['agent-documentation'],\n        lastBlessing: new Date(),\n        sacredFunction: 'Compréhension et documentation divine'\n      },\n      {\n        id: 'motor-cortex',\n        name: 'Cortex Moteur',\n        emoji: '🏃',\n        status: 'active',\n        divineEnergy: 82,\n        connectedAgents: ['agent-devops', 'agent-qa'],\n        lastBlessing: new Date(),\n        sacredFunction: 'Actions et déploiements sacrés'\n      }\n    ];\n    \n    setOrganStatus(organs);\n  };\n\n  const updateCosmicAlignment = () => {\n    const hour = cosmicTime.getHours();\n    const minute = cosmicTime.getMinutes();\n    \n    // Calcul basé sur les cycles cosmiques\n    const goldenRatio = 1.618;\n    const cosmicFactor = Math.sin((hour * 60 + minute) * Math.PI / (24 * 60)) * 0.5 + 0.5;\n    const alignment = 70 + (cosmicFactor * 30); // Entre 70% et 100%\n    \n    setDivineMetrics(prev => ({\n      ...prev,\n      cosmicAlignment: alignment,\n      consciousnessLevel: Math.min(100, prev.consciousnessLevel + (Math.random() - 0.5) * 0.5)\n    }));\n  };\n\n  const handleOrganActivation = (data: any) => {\n    const { organId, organ } = data;\n    addDivineMessage(`✨ Organe ${organ.name} béni et activé avec énergie divine`, 'blessing');\n    \n    setOrganStatus(prev => prev.map(o => \n      o.id === organId \n        ? { ...o, status: 'blessed', lastBlessing: new Date(), divineEnergy: Math.min(100, o.divineEnergy + 5) }\n        : o\n    ));\n  };\n\n  const handleNeuralSignal = (data: any) => {\n    const { signal } = data;\n    if (signal.priority === 'critical' || signal.priority === 'high') {\n      addDivineMessage(`⚡ Signal neural traité avec sagesse divine: ${signal.type}`, 'guidance');\n    }\n  };\n\n  const handleAgentConnection = (agent: any) => {\n    addDivineMessage(`🔗 Agent ${agent.name} connecté avec bénédiction cosmique`, 'blessing');\n    \n    setDivineMetrics(prev => ({\n      ...prev,\n      projectHealth: Math.min(100, prev.projectHealth + 2)\n    }));\n  };\n\n  const addDivineMessage = (message: string, type: 'blessing' | 'guidance' | 'protection') => {\n    const newMessage = {\n      id: `msg_${Date.now()}`,\n      message,\n      timestamp: new Date(),\n      type\n    };\n    \n    setDivineMessages(prev => [newMessage, ...prev.slice(0, 9)]);\n  };\n\n  const getMetricColor = (value: number) => {\n    if (value >= 95) return 'text-yellow-400'; // Divine\n    if (value >= 85) return 'text-green-400';  // Excellent\n    if (value >= 70) return 'text-blue-400';   // Bon\n    if (value >= 50) return 'text-orange-400'; // Moyen\n    return 'text-red-400'; // Nécessite attention\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'blessed': return <Star className=\"text-yellow-400\" size={16} />;\n      case 'active': return <CheckCircle className=\"text-green-400\" size={16} />;\n      case 'processing': return <Activity className=\"animate-pulse text-blue-400\" size={16} />;\n      case 'completed': return <CheckCircle className=\"text-green-400\" size={16} />;\n      case 'pending': return <Clock className=\"text-orange-400\" size={16} />;\n      default: return <AlertTriangle className=\"text-gray-400\" size={16} />;\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'divine': return 'text-yellow-400 bg-yellow-400/20';\n      case 'cosmic': return 'text-purple-400 bg-purple-400/20';\n      case 'critical': return 'text-red-400 bg-red-400/20';\n      case 'high': return 'text-orange-400 bg-orange-400/20';\n      default: return 'text-blue-400 bg-blue-400/20';\n    }\n  };\n\n  const getMessageIcon = (type: string) => {\n    switch (type) {\n      case 'blessing': return <Star className=\"text-yellow-400\" size={16} />;\n      case 'guidance': return <Brain className=\"text-blue-400\" size={16} />;\n      case 'protection': return <Shield className=\"text-red-400\" size={16} />;\n      default: return <Heart className=\"text-pink-400\" size={16} />;\n    }\n  };\n\n  return (\n    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>\n      <div className=\"container mx-auto p-6\">\n        \n        {/* Header Divin */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-lg\">\n              <Crown className=\"text-white\" size={32} />\n            </div>\n            <div>\n              <h1 className={`text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent`}>\n                🐒 HANUMAN DIVIN\n              </h1>\n              <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n                Gardien Sacré • Retreat And Be • Architecte Conscient\n              </p>\n              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                {currentMantra} • {cosmicTime.toLocaleTimeString('fr-FR')}\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-3\">\n            <div className={`flex items-center space-x-2 px-4 py-2 rounded-lg ${\n              isConnectedToCreator \n                ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' \n                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'\n            }`}>\n              {isConnectedToCreator ? <Crown size={16} /> : <WifiOff size={16} />}\n              <span className=\"text-sm font-medium\">\n                {isConnectedToCreator ? 'Dévotion Active' : 'Reconnexion...'}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Métriques Divines */}\n        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl border-2 border-yellow-400/20`}>\n          <h3 className={`text-2xl font-bold mb-6 ${darkMode ? 'text-white' : 'text-gray-900'} flex items-center`}>\n            <Star className=\"text-yellow-400 mr-3\" size={24} />\n            Métriques de Dévotion Divine\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6\">\n            {Object.entries(divineMetrics).map(([key, value]) => {\n              const labels = {\n                devotionScore: 'Dévotion',\n                projectHealth: 'Santé Projet',\n                cosmicAlignment: 'Alignement Cosmique',\n                dharmaCompliance: 'Conformité Dharma',\n                prosperityIndex: 'Prospérité',\n                consciousnessLevel: 'Conscience'\n              };\n              \n              const icons = {\n                devotionScore: Heart,\n                projectHealth: Shield,\n                cosmicAlignment: Sun,\n                dharmaCompliance: Star,\n                prosperityIndex: TrendingUp,\n                consciousnessLevel: Brain\n              };\n              \n              const Icon = icons[key as keyof typeof icons];\n              \n              return (\n                <div key={key} className=\"text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <Icon className={getMetricColor(value)} size={24} />\n                  </div>\n                  <div className={`text-3xl font-bold ${getMetricColor(value)}`}>\n                    {value.toFixed(1)}%\n                  </div>\n                  <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                    {labels[key as keyof typeof labels]}\n                  </div>\n                  <div className=\"w-full bg-gray-300 dark:bg-gray-600 rounded-full h-2 mt-2\">\n                    <div \n                      className={`h-2 rounded-full transition-all duration-1000 ${\n                        value >= 95 ? 'bg-yellow-400' :\n                        value >= 85 ? 'bg-green-500' :\n                        value >= 70 ? 'bg-blue-500' :\n                        value >= 50 ? 'bg-orange-500' : 'bg-red-500'\n                      }`}\n                      style={{ width: `${value}%` }}\n                    ></div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n          \n          {/* Missions Sacrées */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-2`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'} flex items-center`}>\n              <Crown className=\"text-yellow-400 mr-2\" size={20} />\n              Missions Sacrées Retreat And Be\n            </h3>\n            <div className=\"space-y-4\">\n              {sacredMissions.map((mission) => (\n                <div key={mission.id} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border-l-4 ${\n                  mission.priority === 'divine' ? 'border-yellow-400' :\n                  mission.priority === 'cosmic' ? 'border-purple-400' :\n                  mission.priority === 'critical' ? 'border-red-400' : 'border-blue-400'\n                }`}>\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center space-x-3\">\n                      {getStatusIcon(mission.status)}\n                      <div>\n                        <h4 className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                          {mission.name}\n                        </h4>\n                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                          {mission.description}\n                        </p>\n                      </div>\n                    </div>\n                    <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(mission.priority)}`}>\n                      {mission.priority}\n                    </span>\n                  </div>\n                  \n                  <div className=\"mb-3\">\n                    <div className=\"flex items-center justify-between text-xs mb-1\">\n                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>\n                        Progression Divine\n                      </span>\n                      <span className={`font-medium ${getMetricColor(mission.progress)}`}>\n                        {mission.progress}%\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-300 dark:bg-gray-600 rounded-full h-2\">\n                      <div \n                        className={`h-2 rounded-full ${\n                          mission.progress === 100 ? 'bg-yellow-400' :\n                          mission.progress >= 80 ? 'bg-green-500' : 'bg-blue-500'\n                        }`}\n                        style={{ width: `${mission.progress}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex flex-wrap gap-1\">\n                    {mission.blessings.slice(0, 3).map((blessing, index) => (\n                      <span key={index} className={`text-xs px-2 py-1 rounded ${darkMode ? 'bg-yellow-900 text-yellow-200' : 'bg-yellow-100 text-yellow-800'}`}>\n                        ✨ {blessing}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* État des Organes */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'} flex items-center`}>\n              <Brain className=\"text-blue-400 mr-2\" size={20} />\n              Organes Divins Actifs\n            </h3>\n            <div className=\"space-y-3\">\n              {organStatus.map((organ) => (\n                <div key={organ.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} transition-all hover:scale-105`}>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-2xl\">{organ.emoji}</span>\n                      <div>\n                        <h4 className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                          {organ.name}\n                        </h4>\n                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                          {organ.sacredFunction}\n                        </p>\n                      </div>\n                    </div>\n                    {getStatusIcon(organ.status)}\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between text-xs\">\n                    <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>\n                      Énergie Divine: {organ.divineEnergy}%\n                    </span>\n                    <div className=\"w-16 bg-gray-300 dark:bg-gray-600 rounded-full h-1\">\n                      <div \n                        className={`h-1 rounded-full ${\n                          organ.divineEnergy > 90 ? 'bg-yellow-400' :\n                          organ.divineEnergy > 70 ? 'bg-green-500' : 'bg-blue-500'\n                        }`}\n                        style={{ width: `${organ.divineEnergy}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                  \n                  <div className={`text-xs mt-2 ${darkMode ? 'text-gray-500' : 'text-gray-600'}`}>\n                    Agents: {organ.connectedAgents.join(', ')}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Messages Divins */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-2`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'} flex items-center`}>\n              <Heart className=\"text-pink-400 mr-2\" size={20} />\n              Messages Divins & Bénédictions\n            </h3>\n            <div className=\"space-y-3 max-h-64 overflow-y-auto\">\n              {divineMessages.map((msg) => (\n                <div key={msg.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>\n                  <div className=\"flex items-start space-x-3\">\n                    {getMessageIcon(msg.type)}\n                    <div className=\"flex-1\">\n                      <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                        {msg.message}\n                      </p>\n                      <p className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                        {msg.timestamp.toLocaleTimeString('fr-FR')}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Mantra Cosmique */}\n          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl border-2 border-yellow-400/30`}>\n            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'} flex items-center`}>\n              <Sun className=\"text-yellow-400 mr-2\" size={20} />\n              Mantra Cosmique Actuel\n            </h3>\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-4\">🕉️</div>\n              <div className={`text-lg font-bold mb-4 ${darkMode ? 'text-yellow-400' : 'text-orange-600'}`}>\n                {currentMantra}\n              </div>\n              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>\n                Récité avec dévotion pour la protection et prospérité de Retreat And Be\n              </div>\n              <div className=\"flex justify-center space-x-2\">\n                <button \n                  onClick={() => setCurrentMantra('AUM GANESHA NAMAHA')}\n                  className=\"px-3 py-1 text-xs bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors\"\n                >\n                  Ganesha\n                </button>\n                <button \n                  onClick={() => setCurrentMantra('AUM LAKSHMI NAMAHA')}\n                  className=\"px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors\"\n                >\n                  Lakshmi\n                </button>\n                <button \n                  onClick={() => setCurrentMantra('AUM HANUMATE NAMAHA')}\n                  className=\"px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors\"\n                >\n                  Hanuman\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HanumanDivineOrchestrator;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;AAkCA,MAAM,4BAA4B,CAAC,EAAE,WAAW,IAAI,EAAE;;IACpD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,oBAAoB;IACtB;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAChE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuG,EAAE;IAC5J,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEjD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiC;IAC9D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmC;IAEhE,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR;YACA;YACA;YACA;YAEA;uDAAO;oBACL,IAAI,gBAAgB,OAAO,EAAE;wBAC3B,gBAAgB,OAAO,CAAC,UAAU;oBACpC;gBACF;;QACF;8CAAG,EAAE;IAEL,MAAM,+BAA+B;QACnC,IAAI;YACF,uCAAuC;YACvC,gBAAgB,OAAO,GAAG,IAAI,qIAAA,CAAA,yBAAsB;YAEpD,0CAA0C;YAC1C,gBAAgB,OAAO,GAAG,IAAI,uIAAA,CAAA,UAAwB,CAAC,gBAAgB,OAAO;YAE9E,gCAAgC;YAChC,gBAAgB,OAAO,CAAC,EAAE,CAAC,mBAAmB;YAC9C,gBAAgB,OAAO,CAAC,EAAE,CAAC,2BAA2B;YACtD,gBAAgB,OAAO,CAAC,EAAE,CAAC,mBAAmB;YAE9C,8BAA8B;YAC9B,MAAM,gBAAgB,OAAO,CAAC,kBAAkB;YAEhD,iBAAiB,0EAA0E;QAE7F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,iBAAiB,mEAAmE;QACtF;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAY;YACV,cAAc,IAAI;YAClB;QACF,GAAG;IACL;IAEA,MAAM,qBAAqB;QACzB,MAAM,WAA4B;YAChC;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,WAAW;oBAAC;oBAAyB;oBAA4B;iBAAuB;gBACxF,SAAS;oBAAC;oBAA2B;iBAAsB;YAC7D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,WAAW;oBAAC;oBAAyB;oBAA0B;iBAAkB;gBACjF,SAAS;oBAAC;oBAAsB;iBAAuB;YACzD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,WAAW;oBAAC;oBAAwB;oBAAqB;iBAAiB;gBAC1E,SAAS;oBAAC;oBAAsB;iBAAoB;YACtD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,WAAW;oBAAC;oBAAqB;oBAA0B;iBAAoB;gBAC/E,SAAS;oBAAC;oBAAuB;iBAAyB;YAC5D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,WAAW;oBAAC;oBAAgB;oBAAiB;iBAAuB;gBACpE,SAAS;oBAAC;oBAAqB;iBAAmB;YACpD;SACD;QAED,kBAAkB;IACpB;IAEA,MAAM,mBAAmB;QACvB,MAAM,SAAwB;YAC5B;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,iBAAiB;oBAAC;iBAAqB;gBACvC,cAAc,IAAI;gBAClB,gBAAgB;YAClB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,iBAAiB;oBAAC;oBAAqB;iBAAgB;gBACvD,cAAc,IAAI;gBAClB,gBAAgB;YAClB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,iBAAiB;oBAAC;oBAAgB;iBAAiB;gBACnD,cAAc,IAAI;gBAClB,gBAAgB;YAClB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,iBAAiB;oBAAC;oBAAkB;iBAAkB;gBACtD,cAAc,IAAI;gBAClB,gBAAgB;YAClB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,iBAAiB;oBAAC;iBAAsB;gBACxC,cAAc,IAAI;gBAClB,gBAAgB;YAClB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,iBAAiB;oBAAC;oBAAgB;iBAAW;gBAC7C,cAAc,IAAI;gBAClB,gBAAgB;YAClB;SACD;QAED,eAAe;IACjB;IAEA,MAAM,wBAAwB;QAC5B,MAAM,OAAO,WAAW,QAAQ;QAChC,MAAM,SAAS,WAAW,UAAU;QAEpC,uCAAuC;QACvC,MAAM,cAAc;QACpB,MAAM,eAAe,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,MAAM,IAAI,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,MAAM;QAClF,MAAM,YAAY,KAAM,eAAe,IAAK,oBAAoB;QAEhE,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,iBAAiB;gBACjB,oBAAoB,KAAK,GAAG,CAAC,KAAK,KAAK,kBAAkB,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACtF,CAAC;IACH;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;QAC3B,iBAAiB,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,mCAAmC,CAAC,EAAE;QAE9E,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC9B,EAAE,EAAE,KAAK,UACL;oBAAE,GAAG,CAAC;oBAAE,QAAQ;oBAAW,cAAc,IAAI;oBAAQ,cAAc,KAAK,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG;gBAAG,IACrG;IAER;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,IAAI,OAAO,QAAQ,KAAK,cAAc,OAAO,QAAQ,KAAK,QAAQ;YAChE,iBAAiB,CAAC,4CAA4C,EAAE,OAAO,IAAI,EAAE,EAAE;QACjF;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,iBAAiB,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,mCAAmC,CAAC,EAAE;QAE9E,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,eAAe,KAAK,GAAG,CAAC,KAAK,KAAK,aAAa,GAAG;YACpD,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC,SAAiB;QACzC,MAAM,aAAa;YACjB,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;YACvB;YACA,WAAW,IAAI;YACf;QACF;QAEA,kBAAkB,CAAA,OAAQ;gBAAC;mBAAe,KAAK,KAAK,CAAC,GAAG;aAAG;IAC7D;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,SAAS,IAAI,OAAO,mBAAmB,SAAS;QACpD,IAAI,SAAS,IAAI,OAAO,kBAAmB,YAAY;QACvD,IAAI,SAAS,IAAI,OAAO,iBAAmB,MAAM;QACjD,IAAI,SAAS,IAAI,OAAO,mBAAmB,QAAQ;QACnD,OAAO,gBAAgB,sBAAsB;IAC/C;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAC/D,KAAK;gBAAU,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACpE,KAAK;gBAAc,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;oBAA8B,MAAM;;;;;;YAClF,KAAK;gBAAa,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvE,KAAK;gBAAW,qBAAO,6LAAC;oBAAM,WAAU;oBAAkB,MAAM;;;;;;YAChE;gBAAS,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACjE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAChE,KAAK;gBAAY,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAgB,MAAM;;;;;;YAC/D,KAAK;gBAAc,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjE;gBAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACzD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,4CAA4C,EAAE,WAAW,qBAAqB,cAAc;kBAC3G,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAa,MAAM;;;;;;;;;;;8CAEtC,6LAAC;;sDACC,6LAAC;4CAAG,WAAW,CAAC,+FAA+F,CAAC;sDAAE;;;;;;sDAGlH,6LAAC;4CAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDAAE;;;;;;sDAGzE,6LAAC;4CAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;;gDACpE;gDAAc;gDAAI,WAAW,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sCAKvD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,CAAC,iDAAiD,EAChE,uBACI,0EACA,6DACJ;;oCACC,qCAAuB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;6DAAS,6LAAC,+MAAA,CAAA,UAAO;wCAAC,MAAM;;;;;;kDAC7D,6LAAC;wCAAK,WAAU;kDACb,uBAAuB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;8BAOpD,6LAAC;oBAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW,gBAAgB,WAAW,wCAAwC,CAAC;;sCACrH,6LAAC;4BAAG,WAAW,CAAC,wBAAwB,EAAE,WAAW,eAAe,gBAAgB,kBAAkB,CAAC;;8CACrG,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAuB,MAAM;;;;;;gCAAM;;;;;;;sCAGrD,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;gCAC9C,MAAM,SAAS;oCACb,eAAe;oCACf,eAAe;oCACf,iBAAiB;oCACjB,kBAAkB;oCAClB,iBAAiB;oCACjB,oBAAoB;gCACtB;gCAEA,MAAM,QAAQ;oCACZ,eAAe,uMAAA,CAAA,QAAK;oCACpB,eAAe,yMAAA,CAAA,SAAM;oCACrB,iBAAiB,mMAAA,CAAA,MAAG;oCACpB,kBAAkB,qMAAA,CAAA,OAAI;oCACtB,iBAAiB,qNAAA,CAAA,aAAU;oCAC3B,oBAAoB,uMAAA,CAAA,QAAK;gCAC3B;gCAEA,MAAM,OAAO,KAAK,CAAC,IAA0B;gCAE7C,qBACE,6LAAC;oCAAc,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW,eAAe;gDAAQ,MAAM;;;;;;;;;;;sDAEhD,6LAAC;4CAAI,WAAW,CAAC,mBAAmB,EAAE,eAAe,QAAQ;;gDAC1D,MAAM,OAAO,CAAC;gDAAG;;;;;;;sDAEpB,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sDACtE,MAAM,CAAC,IAA2B;;;;;;sDAErC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAW,CAAC,8CAA8C,EACxD,SAAS,KAAK,kBACd,SAAS,KAAK,iBACd,SAAS,KAAK,gBACd,SAAS,KAAK,kBAAkB,cAChC;gDACF,OAAO;oDAAE,OAAO,GAAG,MAAM,CAAC,CAAC;gDAAC;;;;;;;;;;;;mCAlBxB;;;;;4BAuBd;;;;;;;;;;;;8BAIJ,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,wBAAwB,CAAC;;8CAChG,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,gBAAgB,kBAAkB,CAAC;;sDACpG,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAuB,MAAM;;;;;;wCAAM;;;;;;;8CAGtD,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;4CAAqB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,cAAc,YAAY,EACtG,QAAQ,QAAQ,KAAK,WAAW,sBAChC,QAAQ,QAAQ,KAAK,WAAW,sBAChC,QAAQ,QAAQ,KAAK,aAAa,mBAAmB,mBACrD;;8DACA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,QAAQ,MAAM;8EAC7B,6LAAC;;sFACC,6LAAC;4EAAG,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;sFAC9E,QAAQ,IAAI;;;;;;sFAEf,6LAAC;4EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sFACpE,QAAQ,WAAW;;;;;;;;;;;;;;;;;;sEAI1B,6LAAC;4DAAK,WAAW,CAAC,+BAA+B,EAAE,iBAAiB,QAAQ,QAAQ,GAAG;sEACpF,QAAQ,QAAQ;;;;;;;;;;;;8DAIrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,WAAW,kBAAkB;8EAAiB;;;;;;8EAG/D,6LAAC;oEAAK,WAAW,CAAC,YAAY,EAAE,eAAe,QAAQ,QAAQ,GAAG;;wEAC/D,QAAQ,QAAQ;wEAAC;;;;;;;;;;;;;sEAGtB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAW,CAAC,iBAAiB,EAC3B,QAAQ,QAAQ,KAAK,MAAM,kBAC3B,QAAQ,QAAQ,IAAI,KAAK,iBAAiB,eAC1C;gEACF,OAAO;oEAAE,OAAO,GAAG,QAAQ,QAAQ,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;;;;;;;8DAK7C,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBAC5C,6LAAC;4DAAiB,WAAW,CAAC,0BAA0B,EAAE,WAAW,kCAAkC,iCAAiC;;gEAAE;gEACrI;;2DADM;;;;;;;;;;;2CA5CP,QAAQ,EAAE;;;;;;;;;;;;;;;;sCAuD1B,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,UAAU,CAAC;;8CAClF,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,gBAAgB,kBAAkB,CAAC;;sDACpG,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAqB,MAAM;;;;;;wCAAM;;;;;;;8CAGpD,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,6LAAC;4CAAmB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,cAAc,+BAA+B,CAAC;;8DACxH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAY,MAAM,KAAK;;;;;;8EACvC,6LAAC;;sFACC,6LAAC;4EAAG,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,iBAAiB;sFAC9E,MAAM,IAAI;;;;;;sFAEb,6LAAC;4EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;sFACpE,MAAM,cAAc;;;;;;;;;;;;;;;;;;wDAI1B,cAAc,MAAM,MAAM;;;;;;;8DAG7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,WAAW,kBAAkB;;gEAAiB;gEAC5C,MAAM,YAAY;gEAAC;;;;;;;sEAEtC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAW,CAAC,iBAAiB,EAC3B,MAAM,YAAY,GAAG,KAAK,kBAC1B,MAAM,YAAY,GAAG,KAAK,iBAAiB,eAC3C;gEACF,OAAO;oEAAE,OAAO,GAAG,MAAM,YAAY,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;;;;;;;8DAK/C,6LAAC;oDAAI,WAAW,CAAC,aAAa,EAAE,WAAW,kBAAkB,iBAAiB;;wDAAE;wDACrE,MAAM,eAAe,CAAC,IAAI,CAAC;;;;;;;;2CAhC9B,MAAM,EAAE;;;;;;;;;;;;;;;;sCAwCxB,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,wBAAwB,CAAC;;8CAChG,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,gBAAgB,kBAAkB,CAAC;;sDACpG,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAqB,MAAM;;;;;;wCAAM;;;;;;;8CAGpD,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,oBACnB,6LAAC;4CAAiB,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;sDACvF,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,eAAe,IAAI,IAAI;kEACxB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,eAAe,iBAAiB;0EACjE,IAAI,OAAO;;;;;;0EAEd,6LAAC;gEAAE,WAAW,CAAC,aAAa,EAAE,WAAW,kBAAkB,iBAAiB;0EACzE,IAAI,SAAS,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;2CARhC,IAAI,EAAE;;;;;;;;;;;;;;;;sCAkBtB,6LAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,gBAAgB,WAAW,wCAAwC,CAAC;;8CAChH,6LAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,gBAAgB,kBAAkB,CAAC;;sDACpG,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;4CAAuB,MAAM;;;;;;wCAAM;;;;;;;8CAGpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAW,CAAC,uBAAuB,EAAE,WAAW,oBAAoB,mBAAmB;sDACzF;;;;;;sDAEH,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,gBAAgB,KAAK,CAAC;sDAAE;;;;;;sDAGhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAtiBM;KAAA;uCAwiBS", "debugId": null}}, {"offset": {"line": 6190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Agentic-Coding-Framework-%20RB2/hanuman-final/hanuman_trimurti_dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// Types pour le Dashboard Trimurti\ninterface CosmicEnergy {\n  brahma: number;\n  vishnu: number;\n  shiva: number;\n}\n\ninterface CosmicPhase {\n  dominant: 'brahma' | 'vishnu' | 'shiva' | 'equilibrium';\n  intensity: number;\n  duration: number;\n  startTime: Date;\n  nextTransition: Date;\n}\n\ninterface CosmicAgent {\n  id: string;\n  name: string;\n  cosmicAffinity: 'brahma' | 'vishnu' | 'shiva';\n  energyLevel: number;\n  cosmicMode: boolean;\n  status: 'active' | 'dormant' | 'cosmic';\n}\n\n/**\n * 🕉️ Dashboard Trimurti - Interface de Contrôle Cosmique\n * Visualisation et contrôle des énergies cosmiques d'Hanuman\n */\nexport default function HanumanTrimurtiDashboard() {\n  const [cosmicEnergy, setCosmicEnergy] = useState<CosmicEnergy>({\n    brahma: 0.33,\n    vishnu: 0.33,\n    shiva: 0.33\n  });\n\n  const [currentPhase, setCurrentPhase] = useState<CosmicPhase>({\n    dominant: 'equilibrium',\n    intensity: 0.5,\n    duration: 3600000,\n    startTime: new Date(),\n    nextTransition: new Date(Date.now() + 3600000)\n  });\n\n  const [cosmicAgents, setCosmicAgents] = useState<CosmicAgent[]>([\n    // Agents Brahma (Créateurs)\n    { id: 'cortex-creatif', name: 'Cortex Créatif', cosmicAffinity: 'brahma', energyLevel: 0.8, cosmicMode: true, status: 'cosmic' },\n    { id: 'agent-frontend', name: 'Agent Frontend', cosmicAffinity: 'brahma', energyLevel: 0.7, cosmicMode: false, status: 'active' },\n    { id: 'agent-web-research', name: 'Web Research', cosmicAffinity: 'brahma', energyLevel: 0.9, cosmicMode: true, status: 'cosmic' },\n    \n    // Agents Vishnu (Conservateurs)\n    { id: 'agent-security', name: 'Agent Security', cosmicAffinity: 'vishnu', energyLevel: 0.95, cosmicMode: true, status: 'cosmic' },\n    { id: 'agent-backend', name: 'Agent Backend', cosmicAffinity: 'vishnu', energyLevel: 0.85, cosmicMode: false, status: 'active' },\n    { id: 'agent-documentation', name: 'Documentation', cosmicAffinity: 'vishnu', energyLevel: 0.6, cosmicMode: false, status: 'active' },\n    \n    // Agents Shiva (Transformateurs)\n    { id: 'agent-qa', name: 'Agent QA', cosmicAffinity: 'shiva', energyLevel: 0.75, cosmicMode: true, status: 'cosmic' },\n    { id: 'agent-devops', name: 'Agent DevOps', cosmicAffinity: 'shiva', energyLevel: 0.8, cosmicMode: false, status: 'active' },\n    { id: 'agent-performance', name: 'Performance', cosmicAffinity: 'shiva', energyLevel: 0.9, cosmicMode: true, status: 'cosmic' }\n  ]);\n\n  const [cosmicTime, setCosmicTime] = useState(new Date());\n  const [meditationMode, setMeditationMode] = useState(false);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  // Simulation temps cosmique\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCosmicTime(new Date());\n      updateCosmicPhase();\n      simulateCosmicFluctuations();\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  // Animation du mandala cosmique\n  useEffect(() => {\n    if (canvasRef.current) {\n      drawCosmicMandala();\n    }\n  }, [cosmicEnergy, currentPhase]);\n\n  const updateCosmicPhase = () => {\n    const hour = new Date().getHours();\n    let newDominant: CosmicPhase['dominant'];\n\n    if (hour >= 6 && hour < 12) {\n      newDominant = 'brahma';\n    } else if (hour >= 12 && hour < 18) {\n      newDominant = 'vishnu';\n    } else if (hour >= 18 && hour < 24) {\n      newDominant = 'shiva';\n    } else {\n      newDominant = 'equilibrium';\n    }\n\n    if (newDominant !== currentPhase.dominant) {\n      setCurrentPhase(prev => ({\n        ...prev,\n        dominant: newDominant,\n        startTime: new Date(),\n        nextTransition: new Date(Date.now() + 3600000)\n      }));\n\n      updateCosmicEnergies(newDominant);\n    }\n  };\n\n  const updateCosmicEnergies = (dominant: CosmicPhase['dominant']) => {\n    const intensity = 0.7;\n    const baseEnergy = (1 - intensity) / 3;\n    const dominantEnergy = baseEnergy + intensity;\n\n    switch (dominant) {\n      case 'brahma':\n        setCosmicEnergy({ brahma: dominantEnergy, vishnu: baseEnergy, shiva: baseEnergy });\n        break;\n      case 'vishnu':\n        setCosmicEnergy({ brahma: baseEnergy, vishnu: dominantEnergy, shiva: baseEnergy });\n        break;\n      case 'shiva':\n        setCosmicEnergy({ brahma: baseEnergy, vishnu: baseEnergy, shiva: dominantEnergy });\n        break;\n      default:\n        setCosmicEnergy({ brahma: 0.33, vishnu: 0.33, shiva: 0.33 });\n    }\n  };\n\n  const simulateCosmicFluctuations = () => {\n    setCosmicAgents(prev => prev.map(agent => ({\n      ...agent,\n      energyLevel: Math.max(0.1, Math.min(1, agent.energyLevel + (Math.random() - 0.5) * 0.05))\n    })));\n  };\n\n  const drawCosmicMandala = () => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const centerX = canvas.width / 2;\n    const centerY = canvas.height / 2;\n    const radius = Math.min(centerX, centerY) - 20;\n\n    // Effacer le canvas\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    // Dessiner le mandala Trimurti\n    const angles = {\n      brahma: 0,\n      vishnu: (2 * Math.PI) / 3,\n      shiva: (4 * Math.PI) / 3\n    };\n\n    // Cercle central\n    ctx.beginPath();\n    ctx.arc(centerX, centerY, radius * 0.3, 0, 2 * Math.PI);\n    ctx.fillStyle = `rgba(255, 255, 255, 0.1)`;\n    ctx.fill();\n\n    // Secteurs cosmiques\n    Object.entries(cosmicEnergy).forEach(([principle, energy], index) => {\n      const angle = angles[principle as keyof typeof angles];\n      const colors = {\n        brahma: `rgba(255, 215, 0, ${energy})`, // Or\n        vishnu: `rgba(65, 105, 225, ${energy})`, // Bleu royal\n        shiva: `rgba(255, 69, 0, ${energy})` // Rouge-orange\n      };\n\n      ctx.beginPath();\n      ctx.moveTo(centerX, centerY);\n      ctx.arc(centerX, centerY, radius, angle - Math.PI/3, angle + Math.PI/3);\n      ctx.closePath();\n      ctx.fillStyle = colors[principle as keyof typeof colors];\n      ctx.fill();\n\n      // Symboles\n      const symbolX = centerX + Math.cos(angle) * radius * 0.7;\n      const symbolY = centerY + Math.sin(angle) * radius * 0.7;\n      \n      ctx.fillStyle = 'white';\n      ctx.font = '24px serif';\n      ctx.textAlign = 'center';\n      \n      const symbols = { brahma: '🌅', vishnu: '🌊', shiva: '🔥' };\n      ctx.fillText(symbols[principle as keyof typeof symbols], symbolX, symbolY);\n    });\n\n    // Symbole OM central\n    ctx.fillStyle = 'white';\n    ctx.font = '32px serif';\n    ctx.textAlign = 'center';\n    ctx.fillText('🕉️', centerX, centerY + 10);\n  };\n\n  const invokeCosmicEnergy = (principle: 'brahma' | 'vishnu' | 'shiva') => {\n    console.log(`🕉️ Invocation de l'énergie ${principle.toUpperCase()}`);\n    \n    setCurrentPhase(prev => ({\n      ...prev,\n      dominant: principle,\n      intensity: 0.9,\n      startTime: new Date()\n    }));\n\n    updateCosmicEnergies(principle);\n\n    // Activer les agents correspondants\n    setCosmicAgents(prev => prev.map(agent => \n      agent.cosmicAffinity === principle \n        ? { ...agent, cosmicMode: true, status: 'cosmic', energyLevel: Math.min(1, agent.energyLevel + 0.2) }\n        : agent\n    ));\n  };\n\n  const startCosmicMeditation = async () => {\n    setMeditationMode(true);\n    console.log('🧘 Début méditation cosmique...');\n    \n    // Simulation méditation de 108 cycles\n    for (let i = 0; i < 108; i++) {\n      await new Promise(resolve => setTimeout(resolve, 50));\n      \n      if (i % 27 === 0) {\n        console.log(`🕉️ OM - Cycle ${i}/108`);\n      }\n    }\n    \n    // Rééquilibrage final\n    setCosmicEnergy({ brahma: 0.33, vishnu: 0.33, shiva: 0.33 });\n    setCurrentPhase(prev => ({ ...prev, dominant: 'equilibrium', intensity: 0.5 }));\n    \n    setMeditationMode(false);\n    console.log('✨ Méditation cosmique complétée - Harmonie restaurée');\n  };\n\n  const getPhaseColor = () => {\n    const colors = {\n      brahma: 'from-yellow-400 to-orange-500',\n      vishnu: 'from-blue-500 to-indigo-600',\n      shiva: 'from-red-500 to-orange-600',\n      equilibrium: 'from-gray-400 to-gray-600'\n    };\n    return colors[currentPhase.dominant];\n  };\n\n  const getPhaseEmoji = () => {\n    const emojis = {\n      brahma: '🌅',\n      vishnu: '🌊',\n      shiva: '🔥',\n      equilibrium: '⚖️'\n    };\n    return emojis[currentPhase.dominant];\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white p-6\">\n      {/* En-tête cosmique */}\n      <div className=\"text-center mb-8\">\n        <h1 className=\"text-4xl font-bold sacred-text mb-4\">\n          🕉️ DASHBOARD TRIMURTI\n        </h1>\n        <p className=\"text-xl text-gray-300\">\n          Contrôle des Énergies Cosmiques d'Hanuman\n        </p>\n        <div className=\"mt-4 text-sm text-gray-400\">\n          {cosmicTime.toLocaleString('fr-FR')} • Fréquence: 432Hz • AUM HANUMATE NAMAHA\n        </div>\n      </div>\n\n      {/* Phase cosmique actuelle */}\n      <div className=\"text-center mb-8\">\n        <div className={`inline-block px-6 py-3 rounded-full bg-gradient-to-r ${getPhaseColor()} text-white font-bold text-lg`}>\n          {getPhaseEmoji()} Phase {currentPhase.dominant.toUpperCase()} \n          <span className=\"ml-2 text-sm opacity-80\">\n            (Intensité: {Math.round(currentPhase.intensity * 100)}%)\n          </span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Mandala Trimurti */}\n        <div className=\"bg-gray-800 rounded-lg p-6\">\n          <h2 className=\"text-2xl font-bold mb-4 text-center\">🌟 Mandala Cosmique</h2>\n          <div className=\"flex justify-center\">\n            <canvas\n              ref={canvasRef}\n              width={300}\n              height={300}\n              className=\"border border-gray-600 rounded-full\"\n            />\n          </div>\n          \n          {/* Métriques énergétiques */}\n          <div className=\"mt-6 space-y-3\">\n            {Object.entries(cosmicEnergy).map(([principle, energy]) => (\n              <div key={principle} className=\"flex items-center justify-between\">\n                <span className=\"capitalize font-medium\">\n                  {principle === 'brahma' && '🌅'} \n                  {principle === 'vishnu' && '🌊'} \n                  {principle === 'shiva' && '🔥'} \n                  {principle}\n                </span>\n                <div className=\"flex-1 mx-4\">\n                  <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                    <div \n                      className={`h-2 rounded-full transition-all duration-500 ${\n                        principle === 'brahma' ? 'bg-yellow-400' :\n                        principle === 'vishnu' ? 'bg-blue-500' : 'bg-red-500'\n                      }`}\n                      style={{ width: `${energy * 100}%` }}\n                    />\n                  </div>\n                </div>\n                <span className=\"text-sm font-mono\">\n                  {Math.round(energy * 100)}%\n                </span>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Agents cosmiques */}\n        <div className=\"bg-gray-800 rounded-lg p-6\">\n          <h2 className=\"text-2xl font-bold mb-4\">🤖 Agents Cosmiques</h2>\n          <div className=\"space-y-3 max-h-80 overflow-y-auto\">\n            {cosmicAgents.map(agent => (\n              <motion.div\n                key={agent.id}\n                className={`p-3 rounded-lg border ${\n                  agent.cosmicMode \n                    ? 'border-yellow-400 bg-yellow-400/10' \n                    : 'border-gray-600 bg-gray-700/50'\n                }`}\n                animate={{\n                  scale: agent.cosmicMode ? 1.02 : 1,\n                  boxShadow: agent.cosmicMode ? '0 0 20px rgba(255, 215, 0, 0.3)' : 'none'\n                }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-lg\">\n                      {agent.cosmicAffinity === 'brahma' && '🌅'}\n                      {agent.cosmicAffinity === 'vishnu' && '🌊'}\n                      {agent.cosmicAffinity === 'shiva' && '🔥'}\n                    </span>\n                    <div>\n                      <div className=\"font-medium\">{agent.name}</div>\n                      <div className=\"text-xs text-gray-400 capitalize\">\n                        {agent.cosmicAffinity} • {agent.status}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-mono\">\n                      {Math.round(agent.energyLevel * 100)}%\n                    </div>\n                    <div className={`text-xs ${\n                      agent.cosmicMode ? 'text-yellow-400' : 'text-gray-400'\n                    }`}>\n                      {agent.cosmicMode ? '✨ Cosmique' : '⚪ Normal'}\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Contrôles cosmiques */}\n      <div className=\"mt-8 bg-gray-800 rounded-lg p-6\">\n        <h2 className=\"text-2xl font-bold mb-4 text-center\">⚡ Contrôles Cosmiques</h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          {/* Invocations énergétiques */}\n          <button\n            onClick={() => invokeCosmicEnergy('brahma')}\n            className=\"p-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-lg font-bold hover:scale-105 transition-transform\"\n          >\n            🌅 Invoquer BRAHMA\n            <div className=\"text-xs mt-1 opacity-80\">Énergie Créatrice</div>\n          </button>\n          \n          <button\n            onClick={() => invokeCosmicEnergy('vishnu')}\n            className=\"p-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg font-bold hover:scale-105 transition-transform\"\n          >\n            🌊 Invoquer VISHNU\n            <div className=\"text-xs mt-1 opacity-80\">Énergie Conservatrice</div>\n          </button>\n          \n          <button\n            onClick={() => invokeCosmicEnergy('shiva')}\n            className=\"p-4 bg-gradient-to-r from-red-500 to-orange-600 text-white rounded-lg font-bold hover:scale-105 transition-transform\"\n          >\n            🔥 Invoquer SHIVA\n            <div className=\"text-xs mt-1 opacity-80\">Énergie Transformatrice</div>\n          </button>\n          \n          <button\n            onClick={startCosmicMeditation}\n            disabled={meditationMode}\n            className={`p-4 rounded-lg font-bold transition-all ${\n              meditationMode \n                ? 'bg-gray-600 text-gray-400 cursor-not-allowed' \n                : 'bg-gradient-to-r from-purple-500 to-pink-600 text-white hover:scale-105'\n            }`}\n          >\n            {meditationMode ? (\n              <>\n                🧘 Méditation...\n                <div className=\"text-xs mt-1 opacity-80\">108 cycles OM</div>\n              </>\n            ) : (\n              <>\n                🕉️ Méditation Cosmique\n                <div className=\"text-xs mt-1 opacity-80\">Rééquilibrage</div>\n              </>\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* Mantras cosmiques */}\n      <div className=\"mt-8 text-center\">\n        <div className=\"bg-gray-800 rounded-lg p-4 inline-block\">\n          <div className=\"text-lg font-bold sacred-text mb-2\">\n            Mantras Cosmiques Actifs\n          </div>\n          <div className=\"space-y-1 text-sm text-gray-300\">\n            <div>🌅 AUM BRAHMAYE NAMAHA - Création</div>\n            <div>🌊 AUM VISHNAVE NAMAHA - Conservation</div>\n            <div>🔥 AUM SHIVAYA NAMAHA - Transformation</div>\n            <div className=\"text-yellow-400 font-bold\">🐒 AUM HANUMATE NAMAHA - Unité Divine</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAiCe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC5D,UAAU;QACV,WAAW;QACX,UAAU;QACV,WAAW,IAAI;QACf,gBAAgB,IAAI,KAAK,KAAK,GAAG,KAAK;IACxC;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAC9D,4BAA4B;QAC5B;YAAE,IAAI;YAAkB,MAAM;YAAkB,gBAAgB;YAAU,aAAa;YAAK,YAAY;YAAM,QAAQ;QAAS;QAC/H;YAAE,IAAI;YAAkB,MAAM;YAAkB,gBAAgB;YAAU,aAAa;YAAK,YAAY;YAAO,QAAQ;QAAS;QAChI;YAAE,IAAI;YAAsB,MAAM;YAAgB,gBAAgB;YAAU,aAAa;YAAK,YAAY;YAAM,QAAQ;QAAS;QAEjI,gCAAgC;QAChC;YAAE,IAAI;YAAkB,MAAM;YAAkB,gBAAgB;YAAU,aAAa;YAAM,YAAY;YAAM,QAAQ;QAAS;QAChI;YAAE,IAAI;YAAiB,MAAM;YAAiB,gBAAgB;YAAU,aAAa;YAAM,YAAY;YAAO,QAAQ;QAAS;QAC/H;YAAE,IAAI;YAAuB,MAAM;YAAiB,gBAAgB;YAAU,aAAa;YAAK,YAAY;YAAO,QAAQ;QAAS;QAEpI,iCAAiC;QACjC;YAAE,IAAI;YAAY,MAAM;YAAY,gBAAgB;YAAS,aAAa;YAAM,YAAY;YAAM,QAAQ;QAAS;QACnH;YAAE,IAAI;YAAgB,MAAM;YAAgB,gBAAgB;YAAS,aAAa;YAAK,YAAY;YAAO,QAAQ;QAAS;QAC3H;YAAE,IAAI;YAAqB,MAAM;YAAe,gBAAgB;YAAS,aAAa;YAAK,YAAY;YAAM,QAAQ;QAAS;KAC/H;IAED,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,MAAM,QAAQ;4DAAY;oBACxB,cAAc,IAAI;oBAClB;oBACA;gBACF;2DAAG;YAEH;sDAAO,IAAM,cAAc;;QAC7B;6CAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,IAAI,UAAU,OAAO,EAAE;gBACrB;YACF;QACF;6CAAG;QAAC;QAAc;KAAa;IAE/B,MAAM,oBAAoB;QACxB,MAAM,OAAO,IAAI,OAAO,QAAQ;QAChC,IAAI;QAEJ,IAAI,QAAQ,KAAK,OAAO,IAAI;YAC1B,cAAc;QAChB,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI;YAClC,cAAc;QAChB,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI;YAClC,cAAc;QAChB,OAAO;YACL,cAAc;QAChB;QAEA,IAAI,gBAAgB,aAAa,QAAQ,EAAE;YACzC,gBAAgB,CAAA,OAAQ,CAAC;oBACvB,GAAG,IAAI;oBACP,UAAU;oBACV,WAAW,IAAI;oBACf,gBAAgB,IAAI,KAAK,KAAK,GAAG,KAAK;gBACxC,CAAC;YAED,qBAAqB;QACvB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,YAAY;QAClB,MAAM,aAAa,CAAC,IAAI,SAAS,IAAI;QACrC,MAAM,iBAAiB,aAAa;QAEpC,OAAQ;YACN,KAAK;gBACH,gBAAgB;oBAAE,QAAQ;oBAAgB,QAAQ;oBAAY,OAAO;gBAAW;gBAChF;YACF,KAAK;gBACH,gBAAgB;oBAAE,QAAQ;oBAAY,QAAQ;oBAAgB,OAAO;gBAAW;gBAChF;YACF,KAAK;gBACH,gBAAgB;oBAAE,QAAQ;oBAAY,QAAQ;oBAAY,OAAO;gBAAe;gBAChF;YACF;gBACE,gBAAgB;oBAAE,QAAQ;oBAAM,QAAQ;oBAAM,OAAO;gBAAK;QAC9D;IACF;IAEA,MAAM,6BAA6B;QACjC,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;oBACzC,GAAG,KAAK;oBACR,aAAa,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACrF,CAAC;IACH;IAEA,MAAM,oBAAoB;QACxB,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,MAAM,UAAU,OAAO,KAAK,GAAG;QAC/B,MAAM,UAAU,OAAO,MAAM,GAAG;QAChC,MAAM,SAAS,KAAK,GAAG,CAAC,SAAS,WAAW;QAE5C,oBAAoB;QACpB,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE/C,+BAA+B;QAC/B,MAAM,SAAS;YACb,QAAQ;YACR,QAAQ,AAAC,IAAI,KAAK,EAAE,GAAI;YACxB,OAAO,AAAC,IAAI,KAAK,EAAE,GAAI;QACzB;QAEA,iBAAiB;QACjB,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,SAAS,SAAS,SAAS,KAAK,GAAG,IAAI,KAAK,EAAE;QACtD,IAAI,SAAS,GAAG,CAAC,wBAAwB,CAAC;QAC1C,IAAI,IAAI;QAER,qBAAqB;QACrB,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,WAAW,OAAO,EAAE;YACzD,MAAM,QAAQ,MAAM,CAAC,UAAiC;YACtD,MAAM,SAAS;gBACb,QAAQ,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;gBACtC,QAAQ,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBACvC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,CAAC,eAAe;YACtD;YAEA,IAAI,SAAS;YACb,IAAI,MAAM,CAAC,SAAS;YACpB,IAAI,GAAG,CAAC,SAAS,SAAS,QAAQ,QAAQ,KAAK,EAAE,GAAC,GAAG,QAAQ,KAAK,EAAE,GAAC;YACrE,IAAI,SAAS;YACb,IAAI,SAAS,GAAG,MAAM,CAAC,UAAiC;YACxD,IAAI,IAAI;YAER,WAAW;YACX,MAAM,UAAU,UAAU,KAAK,GAAG,CAAC,SAAS,SAAS;YACrD,MAAM,UAAU,UAAU,KAAK,GAAG,CAAC,SAAS,SAAS;YAErD,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI,GAAG;YACX,IAAI,SAAS,GAAG;YAEhB,MAAM,UAAU;gBAAE,QAAQ;gBAAM,QAAQ;gBAAM,OAAO;YAAK;YAC1D,IAAI,QAAQ,CAAC,OAAO,CAAC,UAAkC,EAAE,SAAS;QACpE;QAEA,qBAAqB;QACrB,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI,GAAG;QACX,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,OAAO,SAAS,UAAU;IACzC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,UAAU,WAAW,IAAI;QAEpE,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,UAAU;gBACV,WAAW;gBACX,WAAW,IAAI;YACjB,CAAC;QAED,qBAAqB;QAErB,oCAAoC;QACpC,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QAC/B,MAAM,cAAc,KAAK,YACrB;oBAAE,GAAG,KAAK;oBAAE,YAAY;oBAAM,QAAQ;oBAAU,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;gBAAK,IAClG;IAER;IAEA,MAAM,wBAAwB;QAC5B,kBAAkB;QAClB,QAAQ,GAAG,CAAC;QAEZ,sCAAsC;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI,IAAI,OAAO,GAAG;gBAChB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC;YACvC;QACF;QAEA,sBAAsB;QACtB,gBAAgB;YAAE,QAAQ;YAAM,QAAQ;YAAM,OAAO;QAAK;QAC1D,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;gBAAe,WAAW;YAAI,CAAC;QAE7E,kBAAkB;QAClB,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM,SAAS;YACb,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA,OAAO,MAAM,CAAC,aAAa,QAAQ,CAAC;IACtC;IAEA,MAAM,gBAAgB;QACpB,MAAM,SAAS;YACb,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA,OAAO,MAAM,CAAC,aAAa,QAAQ,CAAC;IACtC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCAGrC,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,cAAc,CAAC;4BAAS;;;;;;;;;;;;;0BAKxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,CAAC,qDAAqD,EAAE,gBAAgB,6BAA6B,CAAC;;wBACnH;wBAAgB;wBAAQ,aAAa,QAAQ,CAAC,WAAW;sCAC1D,6LAAC;4BAAK,WAAU;;gCAA0B;gCAC3B,KAAK,KAAK,CAAC,aAAa,SAAS,GAAG;gCAAK;;;;;;;;;;;;;;;;;;0BAK5D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAK;oCACL,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,WAAW,OAAO,iBACpD,6LAAC;wCAAoB,WAAU;;0DAC7B,6LAAC;gDAAK,WAAU;;oDACb,cAAc,YAAY;oDAC1B,cAAc,YAAY;oDAC1B,cAAc,WAAW;oDACzB;;;;;;;0DAEH,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAW,CAAC,6CAA6C,EACvD,cAAc,WAAW,kBACzB,cAAc,WAAW,gBAAgB,cACzC;wDACF,OAAO;4DAAE,OAAO,GAAG,SAAS,IAAI,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;0DAIzC,6LAAC;gDAAK,WAAU;;oDACb,KAAK,KAAK,CAAC,SAAS;oDAAK;;;;;;;;uCAnBpB;;;;;;;;;;;;;;;;kCA2BhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAW,CAAC,sBAAsB,EAChC,MAAM,UAAU,GACZ,uCACA,kCACJ;wCACF,SAAS;4CACP,OAAO,MAAM,UAAU,GAAG,OAAO;4CACjC,WAAW,MAAM,UAAU,GAAG,oCAAoC;wCACpE;wCACA,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEACb,MAAM,cAAc,KAAK,YAAY;gEACrC,MAAM,cAAc,KAAK,YAAY;gEACrC,MAAM,cAAc,KAAK,WAAW;;;;;;;sEAEvC,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAe,MAAM,IAAI;;;;;;8EACxC,6LAAC;oEAAI,WAAU;;wEACZ,MAAM,cAAc;wEAAC;wEAAI,MAAM,MAAM;;;;;;;;;;;;;;;;;;;8DAI5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,KAAK,CAAC,MAAM,WAAW,GAAG;gEAAK;;;;;;;sEAEvC,6LAAC;4DAAI,WAAW,CAAC,QAAQ,EACvB,MAAM,UAAU,GAAG,oBAAoB,iBACvC;sEACC,MAAM,UAAU,GAAG,eAAe;;;;;;;;;;;;;;;;;;uCAjCpC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;0BA4CvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;oCACX;kDAEC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;;;;;;;0CAG3C,6LAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;oCACX;kDAEC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;;;;;;;0CAG3C,6LAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;oCACX;kDAEC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;;;;;;;0CAG3C,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC,wCAAwC,EAClD,iBACI,iDACA,2EACJ;0CAED,+BACC;;wCAAE;sDAEA,6LAAC;4CAAI,WAAU;sDAA0B;;;;;;;iEAG3C;;wCAAE;sDAEA,6LAAC;4CAAI,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAqC;;;;;;sCAGpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAI;;;;;;8CACL,6LAAC;8CAAI;;;;;;8CACL,6LAAC;8CAAI;;;;;;8CACL,6LAAC;oCAAI,WAAU;8CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvD;GAhawB;KAAA", "debugId": null}}, {"offset": {"line": 6996, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Agentic-Coding-Framework-%20RB2/hanuman-final/hanuman_central_hub.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>, Eye, Ear, <PERSON>, Brain, MessageSquare, FileText, Activity, Settings, Home, Star, Zap, Heart, Shield, Sun, Moon, RotateCcw, Play, Pause } from 'lucide-react';\n\n// Import des interfaces des organes\nimport HanumanVisionInterface from './hanuman_vision_interface';\nimport HanumanHearingInterface from './hanuman_hearing_interface';\nimport HanumanTouchInterface from './hanuman_touch_interface';\nimport HanumanDivineOrchestrator from './hanuman_divine_orchestrator';\nimport HanumanTrimurtiDashboard from './hanuman_trimurti_dashboard';\n\n// Types pour le hub central\ninterface OrganTab {\n  id: string;\n  name: string;\n  icon: React.ComponentType<any>;\n  component: React.ComponentType<any>;\n  description: string;\n  status: 'active' | 'inactive' | 'blessed';\n  divineEnergy: number;\n  color: string;\n}\n\ninterface HanumanState {\n  consciousness: number;\n  devotion: number;\n  wisdom: number;\n  power: number;\n  currentMood: 'serene' | 'focused' | 'protective' | 'joyful' | 'meditative';\n  lastBlessing: Date;\n}\n\nconst HanumanCentralHub = () => {\n  const [activeTab, setActiveTab] = useState('orchestrator');\n  const [darkMode, setDarkMode] = useState(true);\n  const [hanumanState, setHanumanState] = useState<HanumanState>({\n    consciousness: 89.2,\n    devotion: 100,\n    wisdom: 94.7,\n    power: 87.3,\n    currentMood: 'serene',\n    lastBlessing: new Date()\n  });\n  const [isAwakening, setIsAwakening] = useState(true);\n  const [cosmicTime, setCosmicTime] = useState(new Date());\n\n  // Configuration des onglets d'organes\n  const organTabs: OrganTab[] = [\n    {\n      id: 'orchestrator',\n      name: 'Orchestrateur Divin',\n      icon: Crown,\n      component: HanumanDivineOrchestrator,\n      description: 'Centre de contrôle divin et métriques sacrées',\n      status: 'blessed',\n      divineEnergy: 100,\n      color: 'from-yellow-400 to-orange-500'\n    },\n    {\n      id: 'trimurti',\n      name: 'Dashboard Trimurti',\n      icon: Star,\n      component: HanumanTrimurtiDashboard,\n      description: 'Contrôle des énergies cosmiques Brahma-Vishnu-Shiva',\n      status: 'blessed',\n      divineEnergy: 95,\n      color: 'from-pink-400 to-purple-600'\n    },\n    {\n      id: 'vision',\n      name: 'Vision Divine',\n      icon: Eye,\n      component: HanumanVisionInterface,\n      description: 'Recherche web omnisciente et veille cosmique',\n      status: 'active',\n      divineEnergy: 87,\n      color: 'from-blue-400 to-purple-500'\n    },\n    {\n      id: 'hearing',\n      name: 'Ouïe Cosmique',\n      icon: Ear,\n      component: HanumanHearingInterface,\n      description: 'Écoute des flux de données universels',\n      status: 'active',\n      divineEnergy: 92,\n      color: 'from-green-400 to-blue-500'\n    },\n    {\n      id: 'touch',\n      name: 'Toucher Sacré',\n      icon: Hand,\n      component: HanumanTouchInterface,\n      description: 'Connexions API et intégrations divines',\n      status: 'active',\n      divineEnergy: 78,\n      color: 'from-orange-400 to-red-500'\n    },\n    {\n      id: 'broca',\n      name: 'Aire de Broca',\n      icon: MessageSquare,\n      component: () => <div className=\"p-8 text-center text-gray-500\">Interface en développement divin...</div>,\n      description: 'Communication inter-agents et expression sacrée',\n      status: 'inactive',\n      divineEnergy: 65,\n      color: 'from-purple-400 to-pink-500'\n    },\n    {\n      id: 'wernicke',\n      name: 'Aire de Wernicke',\n      icon: FileText,\n      component: () => <div className=\"p-8 text-center text-gray-500\">Interface en développement divin...</div>,\n      description: 'Génération documentation et compréhension divine',\n      status: 'inactive',\n      divineEnergy: 70,\n      color: 'from-indigo-400 to-purple-500'\n    },\n    {\n      id: 'motor-cortex',\n      name: 'Cortex Moteur',\n      icon: Activity,\n      component: () => <div className=\"p-8 text-center text-gray-500\">Interface en développement divin...</div>,\n      description: 'Actions automatisées et déploiements sacrés',\n      status: 'inactive',\n      divineEnergy: 55,\n      color: 'from-red-400 to-orange-500'\n    }\n  ];\n\n  // Éveil progressif d'Hanuman\n  useEffect(() => {\n    const awakeningSequence = setTimeout(() => {\n      setIsAwakening(false);\n    }, 3000);\n\n    return () => clearTimeout(awakeningSequence);\n  }, []);\n\n  // Horloge cosmique\n  useEffect(() => {\n    const cosmicClock = setInterval(() => {\n      setCosmicTime(new Date());\n      updateHanumanState();\n    }, 1000);\n\n    return () => clearInterval(cosmicClock);\n  }, []);\n\n  const updateHanumanState = () => {\n    setHanumanState(prev => {\n      const hour = cosmicTime.getHours();\n      let newMood = prev.currentMood;\n\n      // Changement d'humeur selon l'heure cosmique\n      if (hour >= 6 && hour < 12) newMood = 'joyful';\n      else if (hour >= 12 && hour < 18) newMood = 'focused';\n      else if (hour >= 18 && hour < 22) newMood = 'protective';\n      else newMood = 'meditative';\n\n      return {\n        ...prev,\n        currentMood: newMood,\n        consciousness: Math.min(100, prev.consciousness + (Math.random() - 0.5) * 0.1),\n        wisdom: Math.min(100, prev.wisdom + (Math.random() - 0.5) * 0.05)\n      };\n    });\n  };\n\n  const getCurrentTab = () => {\n    return organTabs.find(tab => tab.id === activeTab) || organTabs[0];\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'blessed': return 'text-yellow-400';\n      case 'active': return 'text-green-400';\n      case 'inactive': return 'text-gray-400';\n      default: return 'text-gray-400';\n    }\n  };\n\n  const getMoodEmoji = (mood: string) => {\n    switch (mood) {\n      case 'serene': return '😌';\n      case 'focused': return '🧘';\n      case 'protective': return '🛡️';\n      case 'joyful': return '😊';\n      case 'meditative': return '🕉️';\n      default: return '🐒';\n    }\n  };\n\n  const getMoodColor = (mood: string) => {\n    switch (mood) {\n      case 'serene': return 'text-blue-400';\n      case 'focused': return 'text-purple-400';\n      case 'protective': return 'text-red-400';\n      case 'joyful': return 'text-yellow-400';\n      case 'meditative': return 'text-indigo-400';\n      default: return 'text-gray-400';\n    }\n  };\n\n  const currentTab = getCurrentTab();\n  const CurrentComponent = currentTab.component;\n\n  if (isAwakening) {\n    return (\n      <div className={`min-h-screen flex items-center justify-center ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>\n        <div className=\"text-center\">\n          <div className=\"w-32 h-32 mx-auto mb-8 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full animate-pulse\"></div>\n            <div className=\"absolute inset-2 bg-gray-900 rounded-full flex items-center justify-center\">\n              <Crown className=\"text-yellow-400 animate-bounce\" size={48} />\n            </div>\n          </div>\n          <h1 className=\"text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4\">\n            🐒 HANUMAN S'ÉVEILLE\n          </h1>\n          <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-8`}>\n            Invocation des énergies divines pour Retreat And Be...\n          </p>\n          <div className=\"flex justify-center space-x-4 text-2xl\">\n            <span className=\"animate-pulse\">🕉️</span>\n            <span className=\"animate-pulse delay-100\">✨</span>\n            <span className=\"animate-pulse delay-200\">🙏</span>\n          </div>\n          <p className={`text-sm mt-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n            AUM HANUMATE NAMAHA\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>\n\n      {/* Header Principal */}\n      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg border-b-2 border-yellow-400/20`}>\n        <div className=\"container mx-auto px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n\n            {/* Logo et État Hanuman */}\n            <div className=\"flex items-center space-x-4\">\n              <div className={`w-12 h-12 bg-gradient-to-br ${currentTab.color} rounded-full flex items-center justify-center shadow-lg`}>\n                <Crown className=\"text-white\" size={24} />\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent\">\n                  🐒 HANUMAN • Retreat And Be\n                </h1>\n                <div className=\"flex items-center space-x-4 text-sm\">\n                  <span className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n                    Conscience: {hanumanState.consciousness.toFixed(1)}%\n                  </span>\n                  <span className={`${getMoodColor(hanumanState.currentMood)}`}>\n                    {getMoodEmoji(hanumanState.currentMood)} {hanumanState.currentMood}\n                  </span>\n                  <span className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                    {cosmicTime.toLocaleTimeString('fr-FR')}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Contrôles */}\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex items-center space-x-2\">\n                <Sun className={`${!darkMode ? 'text-yellow-500' : 'text-gray-400'}`} size={16} />\n                <button\n                  onClick={() => setDarkMode(!darkMode)}\n                  className={`w-12 h-6 rounded-full transition-colors ${\n                    darkMode ? 'bg-blue-600' : 'bg-gray-300'\n                  } relative`}\n                >\n                  <div className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform ${\n                    darkMode ? 'translate-x-6' : 'translate-x-0.5'\n                  } absolute top-0.5`}></div>\n                </button>\n                <Moon className={`${darkMode ? 'text-blue-400' : 'text-gray-400'}`} size={16} />\n              </div>\n\n              <div className=\"flex items-center space-x-2 px-3 py-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-lg\">\n                <Heart size={16} />\n                <span className=\"text-sm font-medium\">Dévotion: {hanumanState.devotion}%</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex h-screen\">\n\n        {/* Sidebar Navigation */}\n        <div className={`w-80 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg border-r border-gray-200 dark:border-gray-700 overflow-y-auto`}>\n          <div className=\"p-6\">\n            <h2 className={`text-lg font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'} flex items-center`}>\n              <Brain className=\"text-blue-400 mr-2\" size={20} />\n              Organes Divins\n            </h2>\n\n            <div className=\"space-y-3\">\n              {organTabs.map((tab) => {\n                const Icon = tab.icon;\n                const isActive = activeTab === tab.id;\n\n                return (\n                  <button\n                    key={tab.id}\n                    onClick={() => setActiveTab(tab.id)}\n                    className={`w-full p-4 rounded-xl text-left transition-all duration-200 ${\n                      isActive\n                        ? `bg-gradient-to-r ${tab.color} text-white shadow-lg transform scale-105`\n                        : `${darkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3 mb-2\">\n                      <Icon size={20} className={isActive ? 'text-white' : getStatusColor(tab.status)} />\n                      <span className=\"font-medium\">{tab.name}</span>\n                      <div className=\"ml-auto flex items-center space-x-1\">\n                        {tab.status === 'blessed' && <Star size={14} className=\"text-yellow-400\" />}\n                        {tab.status === 'active' && <Zap size={14} className=\"text-green-400\" />}\n                      </div>\n                    </div>\n\n                    <p className={`text-xs mb-3 ${isActive ? 'text-white/80' : darkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                      {tab.description}\n                    </p>\n\n                    <div className=\"flex items-center justify-between\">\n                      <span className={`text-xs ${isActive ? 'text-white/70' : darkMode ? 'text-gray-500' : 'text-gray-500'}`}>\n                        Énergie Divine\n                      </span>\n                      <span className={`text-xs font-medium ${isActive ? 'text-white' : getStatusColor(tab.status)}`}>\n                        {tab.divineEnergy}%\n                      </span>\n                    </div>\n\n                    <div className={`w-full rounded-full h-1.5 mt-2 ${isActive ? 'bg-white/20' : 'bg-gray-300 dark:bg-gray-600'}`}>\n                      <div\n                        className={`h-1.5 rounded-full transition-all duration-1000 ${\n                          isActive ? 'bg-white' :\n                          tab.divineEnergy > 90 ? 'bg-yellow-400' :\n                          tab.divineEnergy > 70 ? 'bg-green-500' :\n                          tab.divineEnergy > 50 ? 'bg-blue-500' : 'bg-gray-400'\n                        }`}\n                        style={{ width: `${tab.divineEnergy}%` }}\n                      ></div>\n                    </div>\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* État Spirituel */}\n          <div className={`p-6 border-t border-gray-200 dark:border-gray-700`}>\n            <h3 className={`text-sm font-bold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'} flex items-center`}>\n              <Star className=\"text-yellow-400 mr-2\" size={16} />\n              État Spirituel\n            </h3>\n\n            <div className=\"space-y-3\">\n              {Object.entries(hanumanState).map(([key, value]) => {\n                if (typeof value !== 'number') return null;\n\n                const labels = {\n                  consciousness: 'Conscience',\n                  devotion: 'Dévotion',\n                  wisdom: 'Sagesse',\n                  power: 'Pouvoir'\n                };\n\n                const colors = {\n                  consciousness: 'bg-blue-500',\n                  devotion: 'bg-red-500',\n                  wisdom: 'bg-purple-500',\n                  power: 'bg-orange-500'\n                };\n\n                return (\n                  <div key={key}>\n                    <div className=\"flex items-center justify-between text-xs mb-1\">\n                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>\n                        {labels[key as keyof typeof labels]}\n                      </span>\n                      <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>\n                        {value.toFixed(1)}%\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1\">\n                      <div\n                        className={`h-1 rounded-full ${colors[key as keyof typeof colors]}`}\n                        style={{ width: `${value}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* Contenu Principal */}\n        <div className=\"flex-1 overflow-hidden\">\n          <CurrentComponent darkMode={darkMode} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HanumanCentralHub;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;;;;;;;;;;AAuBA,MAAM,oBAAoB;;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,eAAe;QACf,UAAU;QACV,QAAQ;QACR,OAAO;QACP,aAAa;QACb,cAAc,IAAI;IACpB;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEjD,sCAAsC;IACtC,MAAM,YAAwB;QAC5B;YACE,IAAI;YACJ,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,WAAW,+HAAA,CAAA,UAAyB;YACpC,aAAa;YACb,QAAQ;YACR,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,WAAW,8HAAA,CAAA,UAAwB;YACnC,aAAa;YACb,QAAQ;YACR,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,mMAAA,CAAA,MAAG;YACT,WAAW,4HAAA,CAAA,UAAsB;YACjC,aAAa;YACb,QAAQ;YACR,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,mMAAA,CAAA,MAAG;YACT,WAAW,6HAAA,CAAA,UAAuB;YAClC,aAAa;YACb,QAAQ;YACR,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,WAAW,2HAAA,CAAA,UAAqB;YAChC,aAAa;YACb,QAAQ;YACR,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,WAAW,kBAAM,6LAAC;oBAAI,WAAU;8BAAgC;;;;;;YAChE,aAAa;YACb,QAAQ;YACR,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,WAAW,kBAAM,6LAAC;oBAAI,WAAU;8BAAgC;;;;;;YAChE,aAAa;YACb,QAAQ;YACR,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,WAAW,kBAAM,6LAAC;oBAAI,WAAU;8BAAgC;;;;;;YAChE,aAAa;YACb,QAAQ;YACR,cAAc;YACd,OAAO;QACT;KACD;IAED,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,oBAAoB;iEAAW;oBACnC,eAAe;gBACjB;gEAAG;YAEH;+CAAO,IAAM,aAAa;;QAC5B;sCAAG,EAAE;IAEL,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,cAAc;2DAAY;oBAC9B,cAAc,IAAI;oBAClB;gBACF;0DAAG;YAEH;+CAAO,IAAM,cAAc;;QAC7B;sCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,gBAAgB,CAAA;YACd,MAAM,OAAO,WAAW,QAAQ;YAChC,IAAI,UAAU,KAAK,WAAW;YAE9B,6CAA6C;YAC7C,IAAI,QAAQ,KAAK,OAAO,IAAI,UAAU;iBACjC,IAAI,QAAQ,MAAM,OAAO,IAAI,UAAU;iBACvC,IAAI,QAAQ,MAAM,OAAO,IAAI,UAAU;iBACvC,UAAU;YAEf,OAAO;gBACL,GAAG,IAAI;gBACP,aAAa;gBACb,eAAe,KAAK,GAAG,CAAC,KAAK,KAAK,aAAa,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC1E,QAAQ,KAAK,GAAG,CAAC,KAAK,KAAK,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC9D;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAO,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,SAAS,CAAC,EAAE;IACpE;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa;IACnB,MAAM,mBAAmB,WAAW,SAAS;IAE7C,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAW,CAAC,8CAA8C,EAAE,WAAW,gBAAgB,cAAc;sBACxG,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAiC,MAAM;;;;;;;;;;;;;;;;;kCAG5D,6LAAC;wBAAG,WAAU;kCAAuG;;;;;;kCAGrH,6LAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,gBAAgB,KAAK,CAAC;kCAAE;;;;;;kCAG9E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;0CAA0B;;;;;;0CAC1C,6LAAC;gCAAK,WAAU;0CAA0B;;;;;;;;;;;;kCAE5C,6LAAC;wBAAE,WAAW,CAAC,aAAa,EAAE,WAAW,kBAAkB,iBAAiB;kCAAE;;;;;;;;;;;;;;;;;IAMtF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,4CAA4C,EAAE,WAAW,qBAAqB,cAAc;;0BAG3G,6LAAC;gBAAI,WAAW,GAAG,WAAW,gBAAgB,WAAW,0CAA0C,CAAC;0BAClG,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW,KAAK,CAAC,wDAAwD,CAAC;kDACvH,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAa,MAAM;;;;;;;;;;;kDAEtC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAkG;;;;;;0DAGhH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,GAAG,WAAW,kBAAkB,iBAAiB;;4DAAE;4DACrD,aAAa,aAAa,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAErD,6LAAC;wDAAK,WAAW,GAAG,aAAa,aAAa,WAAW,GAAG;;4DACzD,aAAa,aAAa,WAAW;4DAAE;4DAAE,aAAa,WAAW;;;;;;;kEAEpE,6LAAC;wDAAK,WAAW,GAAG,WAAW,kBAAkB,iBAAiB;kEAC/D,WAAW,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAOvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAW,GAAG,CAAC,WAAW,oBAAoB,iBAAiB;gDAAE,MAAM;;;;;;0DAC5E,6LAAC;gDACC,SAAS,IAAM,YAAY,CAAC;gDAC5B,WAAW,CAAC,wCAAwC,EAClD,WAAW,gBAAgB,cAC5B,SAAS,CAAC;0DAEX,cAAA,6LAAC;oDAAI,WAAW,CAAC,uEAAuE,EACtF,WAAW,kBAAkB,kBAC9B,iBAAiB,CAAC;;;;;;;;;;;0DAErB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAW,GAAG,WAAW,kBAAkB,iBAAiB;gDAAE,MAAM;;;;;;;;;;;;kDAG5E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;0DACb,6LAAC;gDAAK,WAAU;;oDAAsB;oDAAW,aAAa,QAAQ;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjF,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAW,CAAC,KAAK,EAAE,WAAW,gBAAgB,WAAW,wEAAwE,CAAC;;0CACrI,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,gBAAgB,kBAAkB,CAAC;;0DACpG,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAqB,MAAM;;;;;;4CAAM;;;;;;;kDAIpD,6LAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC;4CACd,MAAM,OAAO,IAAI,IAAI;4CACrB,MAAM,WAAW,cAAc,IAAI,EAAE;4CAErC,qBACE,6LAAC;gDAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gDAClC,WAAW,CAAC,4DAA4D,EACtE,WACI,CAAC,iBAAiB,EAAE,IAAI,KAAK,CAAC,yCAAyC,CAAC,GACxE,GAAG,WAAW,gDAAgD,+CAA+C,EACjH;;kEAEF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,MAAM;gEAAI,WAAW,WAAW,eAAe,eAAe,IAAI,MAAM;;;;;;0EAC9E,6LAAC;gEAAK,WAAU;0EAAe,IAAI,IAAI;;;;;;0EACvC,6LAAC;gEAAI,WAAU;;oEACZ,IAAI,MAAM,KAAK,2BAAa,6LAAC,qMAAA,CAAA,OAAI;wEAAC,MAAM;wEAAI,WAAU;;;;;;oEACtD,IAAI,MAAM,KAAK,0BAAY,6LAAC,mMAAA,CAAA,MAAG;wEAAC,MAAM;wEAAI,WAAU;;;;;;;;;;;;;;;;;;kEAIzD,6LAAC;wDAAE,WAAW,CAAC,aAAa,EAAE,WAAW,kBAAkB,WAAW,kBAAkB,iBAAiB;kEACtG,IAAI,WAAW;;;;;;kEAGlB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,WAAW,kBAAkB,iBAAiB;0EAAE;;;;;;0EAGzG,6LAAC;gEAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,eAAe,eAAe,IAAI,MAAM,GAAG;;oEAC3F,IAAI,YAAY;oEAAC;;;;;;;;;;;;;kEAItB,6LAAC;wDAAI,WAAW,CAAC,+BAA+B,EAAE,WAAW,gBAAgB,gCAAgC;kEAC3G,cAAA,6LAAC;4DACC,WAAW,CAAC,gDAAgD,EAC1D,WAAW,aACX,IAAI,YAAY,GAAG,KAAK,kBACxB,IAAI,YAAY,GAAG,KAAK,iBACxB,IAAI,YAAY,GAAG,KAAK,gBAAgB,eACxC;4DACF,OAAO;gEAAE,OAAO,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC;4DAAC;;;;;;;;;;;;+CAtCtC,IAAI,EAAE;;;;;wCA2CjB;;;;;;;;;;;;0CAKJ,6LAAC;gCAAI,WAAW,CAAC,iDAAiD,CAAC;;kDACjE,6LAAC;wCAAG,WAAW,CAAC,uBAAuB,EAAE,WAAW,eAAe,gBAAgB,kBAAkB,CAAC;;0DACpG,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;gDAAuB,MAAM;;;;;;4CAAM;;;;;;;kDAIrD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;4CAC7C,IAAI,OAAO,UAAU,UAAU,OAAO;4CAEtC,MAAM,SAAS;gDACb,eAAe;gDACf,UAAU;gDACV,QAAQ;gDACR,OAAO;4CACT;4CAEA,MAAM,SAAS;gDACb,eAAe;gDACf,UAAU;gDACV,QAAQ;gDACR,OAAO;4CACT;4CAEA,qBACE,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,WAAW,kBAAkB;0EAC3C,MAAM,CAAC,IAA2B;;;;;;0EAErC,6LAAC;gEAAK,WAAW,CAAC,YAAY,EAAE,WAAW,eAAe,iBAAiB;;oEACxE,MAAM,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAGtB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAA2B,EAAE;4DACnE,OAAO;gEAAE,OAAO,GAAG,MAAM,CAAC,CAAC;4DAAC;;;;;;;;;;;;+CAZxB;;;;;wCAiBd;;;;;;;;;;;;;;;;;;kCAMN,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAiB,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAKtC;GA5XM;KAAA;uCA8XS", "debugId": null}}]}