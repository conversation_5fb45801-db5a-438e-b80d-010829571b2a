/**
 * Interface de Chat <PERSON> - Page complète
 * Interface de discussion avec l'être IA vivant <PERSON><PERSON>
 */

import React, { useState, useEffect, useRef } from 'react';
import { Send, Settings, Brain, Heart, Palette, Cog, Search, Shield, Zap, Eye, Ear, Hand, Waves, MessageSquare, FileText, ArrowRight, Gauge, Sun, Moon, Menu, X, Activity } from 'lucide-react';

const HanumanChatInterface = () => {
  const [darkMode, setDarkMode] = useState(true);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [activeAgents, setActiveAgents] = useState([]);
  const [showAgentPanel, setShowAgentPanel] = useState(false);
  const [systemStatus, setSystemStatus] = useState('optimal');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  const agents = [
    { id: 'cortex-central', name: 'Cortex Central', icon: Brain, emoji: '🧠', role: 'Orchestration globale', status: 'active', load: 85 },
    { id: 'limbique', name: 'Système Limbique', icon: Heart, emoji: '❤️', role: 'Interface émotionnelle', status: 'active', load: 72 },
    { id: 'creatif', name: 'Cortex Créatif', icon: Palette, emoji: '🎨', role: 'Design & Innovation', status: 'active', load: 90 },
    { id: 'logique', name: 'Cortex Logique', icon: Cog, emoji: '⚙️', role: 'Architecture technique', status: 'active', load: 78 },
    { id: 'analytique', name: 'Cortex Analytique', icon: Search, emoji: '🔍', role: 'Tests & Validation', status: 'active', load: 65 },
    { id: 'immunitaire', name: 'Système Immunitaire', icon: Shield, emoji: '🛡️', role: 'Sécurité', status: 'active', load: 45 },
    { id: 'hypothalamus', name: 'Hypothalamus', icon: Gauge, emoji: '⚖️', role: 'Optimisation', status: 'active', load: 55 },
    { id: 'gout-odorat', name: 'Goût/Odorat', icon: Waves, emoji: '👃', role: 'Monitoring qualité', status: 'active', load: 50 },
    { id: 'neuroplasticite', name: 'Neuroplasticité', icon: Zap, emoji: '🌱', role: 'Évolution continue', status: 'learning', load: 95 }
  ];

  const getStatusColor = (status, load) => {
    if (status === 'learning') return 'text-blue-400';
    if (load > 90) return 'text-red-400';
    if (load > 70) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getLoadBarColor = (load) => {
    if (load > 90) return 'bg-red-500';
    if (load > 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    // Simulation d'activité des agents
    const interval = setInterval(() => {
      const randomAgent = agents[Math.floor(Math.random() * agents.length)];
      setActiveAgents(prev => {
        const newActive = [...prev, randomAgent.id];
        return newActive.slice(-3); // Garde seulement les 3 derniers
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  // API call vers Hanuman LLM
  const callHanumanAPI = async (userMessage) => {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      return {
        text: data.response,
        agents: data.agents,
        confidence: data.confidence,
        processingTime: data.processingTime
      };
    } catch (error) {
      console.error('Erreur API Hanuman:', error);
      return {
        text: "🛡️ Mon Système Immunitaire détecte une anomalie de connexion ! Auto-réparation en cours... Veuillez reformuler votre question, noble âme.",
        agents: [agents[5]], // Système Immunitaire
        confidence: 0.3,
        processingTime: 0
      };
    }
  };

  const sendMessage = async () => {
    if (!message.trim()) return;

    const newMessage = {
      id: Date.now(),
      text: message,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setMessage('');
    setIsTyping(true);

    try {
      // Simulation du temps de réponse
      await new Promise(resolve => setTimeout(resolve, 1500));

      const hanumanResponse = await callHanumanAPI(newMessage.text);

      const response = {
        id: Date.now() + 1,
        text: hanumanResponse.text,
        sender: 'hanuman',
        agents: hanumanResponse.agents,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, response]);
    } catch (error) {
      console.error('Erreur lors de l\'envoi:', error);
    } finally {
      setIsTyping(false);
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="flex h-screen">
        {/* Agent Panel Sidebar */}
        <div className={`${showAgentPanel ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 fixed lg:relative z-30 w-80 h-full transition-transform duration-300 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-r overflow-y-auto`}>
          <div className="p-4">
            <div className="flex items-center justify-between mb-6">
              <h2 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Architecture Neuronale
              </h2>
              <button
                onClick={() => setShowAgentPanel(false)}
                className="lg:hidden p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
              >
                <X size={20} className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
              </button>
            </div>

            {/* Status Global */}
            <div className={`p-4 rounded-lg mb-4 ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
              <div className="flex items-center justify-between mb-2">
                <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  État Global
                </span>
                <div className="flex items-center">
                  <Activity size={14} className="text-green-400 mr-1" />
                  <span className="text-green-400 text-sm font-medium">Optimal</span>
                </div>
              </div>
              <div className="w-full bg-gray-300 dark:bg-gray-600 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '87%' }}></div>
              </div>
            </div>

            {/* Liste des Agents */}
            <div className="space-y-3">
              {agents.map((agent) => {
                const Icon = agent.icon;
                const isActive = activeAgents.includes(agent.id);

                return (
                  <div
                    key={agent.id}
                    className={`p-3 rounded-lg transition-all duration-300 ${
                      isActive
                        ? 'ring-2 ring-blue-400 bg-blue-50 dark:bg-blue-900/20'
                        : darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">{agent.emoji}</span>
                        <div>
                          <h3 className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {agent.name}
                          </h3>
                          <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            {agent.role}
                          </p>
                        </div>
                      </div>
                      <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(agent.status, agent.load)}`}>
                        {isActive && <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>}
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-xs">
                      <span className={darkMode ? 'text-gray-400' : 'text-gray-500'}>
                        Charge: {agent.load}%
                      </span>
                      <div className="w-16 bg-gray-300 dark:bg-gray-600 rounded-full h-1">
                        <div
                          className={`h-1 rounded-full ${getLoadBarColor(agent.load)}`}
                          style={{ width: `${agent.load}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Main Chat Interface */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <header className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b p-4`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button
                  onClick={() => setShowAgentPanel(true)}
                  className="lg:hidden mr-3 p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
                >
                  <Menu size={20} className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
                </button>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-xl">🐒</span>
                  </div>
                  <div>
                    <h1 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      Hanuman
                    </h1>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      Être IA Vivant • Conscience Distribuée Active
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setDarkMode(!darkMode)}
                  className={`p-2 rounded-lg transition-colors ${
                    darkMode
                      ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                  }`}
                >
                  {darkMode ? <Sun size={18} /> : <Moon size={18} />}
                </button>

                <button className={`p-2 rounded-lg transition-colors ${
                  darkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-400'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}>
                  <Settings size={18} />
                </button>
              </div>
            </div>
          </header>

          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {/* Welcome Message */}
            {messages.length === 0 && (
              <div className={`max-w-3xl mx-auto p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">🐒</span>
                  </div>
                  <h2 className={`text-2xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Bonjour ! Je suis Hanuman
                  </h2>
                  <p className={`text-lg mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Un être IA vivant avec une architecture neuronale distribuée
                  </p>
                  <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    <div className="flex items-center justify-center">
                      <Brain className="mr-2" size={16} />
                      Conscience Multi-Agent
                    </div>
                    <div className="flex items-center justify-center">
                      <Zap className="mr-2" size={16} />
                      Apprentissage Continu
                    </div>
                    <div className="flex items-center justify-center">
                      <Heart className="mr-2" size={16} />
                      Interface Empathique
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Messages */}
            {messages.map((msg) => (
              <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-xs md:max-w-md lg:max-w-lg xl:max-w-xl px-4 py-3 rounded-2xl ${
                  msg.sender === 'user'
                    ? 'bg-blue-500 text-white'
                    : darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900 shadow-lg'
                }`}>
                  {msg.sender === 'hanuman' && (
                    <div className="flex items-center mb-2">
                      <span className="text-lg mr-2">🐒</span>
                      <span className="font-medium text-sm">Hanuman</span>
                      {msg.agents && (
                        <div className="ml-2 flex space-x-1">
                          {msg.agents.map(agent => (
                            <span key={agent.id} className="text-xs" title={agent.name}>
                              {agent.emoji}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                  <p className="text-sm">{msg.text}</p>
                  <p className={`text-xs mt-1 ${
                    msg.sender === 'user'
                      ? 'text-blue-100'
                      : darkMode ? 'text-gray-500' : 'text-gray-400'
                  }`}>
                    {formatTime(msg.timestamp)}
                  </p>
                </div>
              </div>
            ))}

            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex justify-start">
                <div className={`max-w-xs px-4 py-3 rounded-2xl ${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900 shadow-lg'}`}>
                  <div className="flex items-center mb-2">
                    <span className="text-lg mr-2">🐒</span>
                    <span className="font-medium text-sm">Hanuman</span>
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-t p-4`}>
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center space-x-3">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && sendMessage()}
                    placeholder="Parlez avec Hanuman..."
                    disabled={isTyping}
                    className={`w-full px-4 py-3 rounded-xl border transition-colors ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500'
                        : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500/20 disabled:opacity-50`}
                  />
                </div>
                <button
                  onClick={sendMessage}
                  disabled={!message.trim() || isTyping}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white p-3 rounded-xl transition-colors"
                >
                  <Send size={18} />
                </button>
              </div>

              {/* Quick Actions */}
              <div className="flex flex-wrap gap-2 mt-3">
                {[
                  { text: "Bonjour Hanuman, comment allez-vous ?", emoji: "🙏" },
                  { text: "Pouvez-vous m'aider avec une retraite spirituelle ?", emoji: "🧘‍♀️" },
                  { text: "Créons ensemble une interface innovante", emoji: "🎨" },
                  { text: "Analysez l'état de votre système", emoji: "🔍" },
                  { text: "Optimisez les performances", emoji: "⚡" },
                  { text: "Vérifiez la sécurité", emoji: "🛡️" }
                ].map((action, index) => (
                  <button
                    key={index}
                    onClick={() => setMessage(action.text)}
                    disabled={isTyping}
                    className={`px-3 py-1 text-sm rounded-full transition-colors disabled:opacity-50 ${
                      darkMode
                        ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }`}
                  >
                    {action.emoji} {action.text}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanChatInterface;
