import React, { useState, useEffect, useRef } from 'react';
import { Globe, Sun, Moon, Star, Orbit, Calendar, Clock, Zap, Setting<PERSON>, Eye, TrendingUp, Activity } from 'lucide-react';

// Types pour l'interface d'alignement planétaire
interface PlanetaryPosition {
  name: string;
  symbol: string;
  longitude: number;
  latitude: number;
  distance: number;
  magnitude: number;
  constellation: string;
  phase?: number; // Pour la Lune
  color: string;
}

interface AstrologicalAspect {
  planet1: string;
  planet2: string;
  angle: number;
  type: 'conjunction' | 'opposition' | 'trine' | 'square' | 'sextile';
  orb: number;
  influence: 'harmonious' | 'challenging' | 'neutral';
  strength: number;
}

interface CosmicInfluence {
  timestamp: Date;
  overallEnergy: number;
  dominantPlanet: string;
  favorableActivities: string[];
  challengingAreas: string[];
  recommendations: string[];
  nextSignificantEvent: {
    event: string;
    date: Date;
    impact: number;
  };
}

interface DecisionOptimization {
  id: string;
  decision: string;
  currentTiming: number;
  optimalTiming: Date;
  cosmicFactors: string[];
  confidence: number;
  recommendation: 'proceed' | 'wait' | 'reconsider';
}

const HanumanPlanetaryInterface = ({ darkMode = true }) => {
  const [planetaryPositions, setPlanetaryPositions] = useState<PlanetaryPosition[]>([]);
  const [astrologicalAspects, setAstrologicalAspects] = useState<AstrologicalAspect[]>([]);
  const [cosmicInfluence, setCosmicInfluence] = useState<CosmicInfluence | null>(null);
  const [decisionOptimizations, setDecisionOptimizations] = useState<DecisionOptimization[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedPlanet, setSelectedPlanet] = useState<string | null>(null);
  const [autoUpdate, setAutoUpdate] = useState(true);
  const wsRef = useRef<WebSocket | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Simulation des positions planétaires
  useEffect(() => {
    const mockPlanets: PlanetaryPosition[] = [
      {
        name: 'Soleil',
        symbol: '☉',
        longitude: 45.7,
        latitude: 0,
        distance: 1.0,
        magnitude: -26.7,
        constellation: 'Taureau',
        color: '#FFD700'
      },
      {
        name: 'Lune',
        symbol: '☽',
        longitude: 123.4,
        latitude: 2.1,
        distance: 0.0026,
        magnitude: -12.6,
        constellation: 'Lion',
        phase: 0.73,
        color: '#C0C0C0'
      },
      {
        name: 'Mercure',
        symbol: '☿',
        longitude: 67.2,
        latitude: 1.3,
        distance: 0.39,
        magnitude: -0.4,
        constellation: 'Gémeaux',
        color: '#87CEEB'
      },
      {
        name: 'Vénus',
        symbol: '♀',
        longitude: 89.1,
        latitude: -2.7,
        distance: 0.72,
        magnitude: -4.6,
        constellation: 'Cancer',
        color: '#FFC649'
      },
      {
        name: 'Mars',
        symbol: '♂',
        longitude: 234.8,
        latitude: 1.8,
        distance: 1.52,
        magnitude: -2.9,
        constellation: 'Scorpion',
        color: '#CD5C5C'
      },
      {
        name: 'Jupiter',
        symbol: '♃',
        longitude: 156.3,
        latitude: 0.9,
        distance: 5.2,
        magnitude: -2.7,
        constellation: 'Vierge',
        color: '#D2691E'
      },
      {
        name: 'Saturne',
        symbol: '♄',
        longitude: 298.7,
        latitude: -1.2,
        distance: 9.5,
        magnitude: 0.7,
        constellation: 'Verseau',
        color: '#FAD5A5'
      }
    ];
    setPlanetaryPositions(mockPlanets);

    const mockAspects: AstrologicalAspect[] = [
      {
        planet1: 'Soleil',
        planet2: 'Jupiter',
        angle: 120,
        type: 'trine',
        orb: 2.3,
        influence: 'harmonious',
        strength: 0.87
      },
      {
        planet1: 'Mars',
        planet2: 'Saturne',
        angle: 90,
        type: 'square',
        orb: 1.8,
        influence: 'challenging',
        strength: 0.92
      },
      {
        planet1: 'Vénus',
        planet2: 'Lune',
        angle: 60,
        type: 'sextile',
        orb: 3.1,
        influence: 'harmonious',
        strength: 0.65
      }
    ];
    setAstrologicalAspects(mockAspects);

    const mockInfluence: CosmicInfluence = {
      timestamp: new Date(),
      overallEnergy: 0.78,
      dominantPlanet: 'Jupiter',
      favorableActivities: [
        'Prise de décisions stratégiques',
        'Lancement de nouveaux projets',
        'Communication et négociation',
        'Apprentissage et formation'
      ],
      challengingAreas: [
        'Investissements risqués',
        'Confrontations directes',
        'Changements majeurs'
      ],
      recommendations: [
        'Profiter de l\'influence jupitérienne pour l\'expansion',
        'Éviter les décisions impulsives sous l\'aspect Mars-Saturne',
        'Utiliser l\'harmonie Vénus-Lune pour les relations'
      ],
      nextSignificantEvent: {
        event: 'Nouvelle Lune en Gémeaux',
        date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        impact: 0.85
      }
    };
    setCosmicInfluence(mockInfluence);

    const mockDecisions: DecisionOptimization[] = [
      {
        id: 'decision-001',
        decision: 'Déploiement nouvelle version',
        currentTiming: 0.45,
        optimalTiming: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        cosmicFactors: ['Jupiter en trigone', 'Mercure direct'],
        confidence: 0.89,
        recommendation: 'wait'
      },
      {
        id: 'decision-002',
        decision: 'Lancement campagne marketing',
        currentTiming: 0.82,
        optimalTiming: new Date(Date.now() + 6 * 60 * 60 * 1000),
        cosmicFactors: ['Vénus-Lune harmonieux', 'Soleil en Taureau'],
        confidence: 0.94,
        recommendation: 'proceed'
      }
    ];
    setDecisionOptimizations(mockDecisions);
  }, []);

  // Connexion WebSocket pour les mises à jour astronomiques
  useEffect(() => {
    if (autoUpdate) {
      // Simulation de connexion WebSocket avec APIs astronomiques
      wsRef.current = new WebSocket('ws://localhost:8080/cosmic-alignment');
      
      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'planetary_update') {
          setPlanetaryPositions(data.positions);
        } else if (data.type === 'aspects_update') {
          setAstrologicalAspects(data.aspects);
        } else if (data.type === 'influence_update') {
          setCosmicInfluence(data.influence);
        }
      };

      return () => {
        wsRef.current?.close();
      };
    }
  }, [autoUpdate]);

  // Mise à jour de l'heure
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Rendu du système solaire 3D (simplifié)
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const scale = 80;

    // Effacer le canvas
    ctx.fillStyle = darkMode ? '#1a1a1a' : '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Dessiner le Soleil au centre
    ctx.fillStyle = '#FFD700';
    ctx.beginPath();
    ctx.arc(centerX, centerY, 8, 0, 2 * Math.PI);
    ctx.fill();

    // Dessiner les planètes
    planetaryPositions.forEach((planet, index) => {
      const angle = (planet.longitude * Math.PI) / 180;
      const radius = (index + 1) * 25;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);

      // Orbite
      ctx.strokeStyle = darkMode ? '#333' : '#ddd';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.stroke();

      // Planète
      ctx.fillStyle = planet.color;
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();

      // Nom de la planète
      if (selectedPlanet === planet.name || !selectedPlanet) {
        ctx.fillStyle = darkMode ? '#fff' : '#000';
        ctx.font = '10px Arial';
        ctx.fillText(planet.symbol, x + 6, y - 6);
      }
    });
  }, [planetaryPositions, selectedPlanet, darkMode]);

  const getAspectColor = (influence: string) => {
    switch (influence) {
      case 'harmonious': return 'text-green-400';
      case 'challenging': return 'text-red-400';
      default: return 'text-yellow-400';
    }
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'proceed': return 'text-green-400 bg-green-500/20';
      case 'wait': return 'text-yellow-400 bg-yellow-500/20';
      case 'reconsider': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* En-tête */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl">
              <Globe className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                Alignement Planétaire Hanuman
              </h1>
              <p className="text-gray-400 mt-1">
                Synchronisation cosmique et optimisation des décisions
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-400">Temps Cosmique</p>
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-blue-400" />
                <p className="font-mono text-lg" suppressHydrationWarning>{currentTime.toLocaleTimeString()}</p>
              </div>
            </div>
            <button
              onClick={() => setAutoUpdate(!autoUpdate)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                autoUpdate 
                  ? 'bg-green-500 hover:bg-green-600 text-white' 
                  : 'bg-gray-600 hover:bg-gray-700 text-white'
              }`}
            >
              {autoUpdate ? <Activity className="w-4 h-4" /> : <Settings className="w-4 h-4" />}
              <span>{autoUpdate ? 'Auto' : 'Manuel'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Influence cosmique globale */}
      {cosmicInfluence && (
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700 mb-8`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <Star className="w-5 h-5 mr-2 text-yellow-400" />
            Influence Cosmique Actuelle
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div>
              <div className="flex items-center justify-between mb-3">
                <span className="text-gray-400">Énergie Globale</span>
                <span className="text-2xl font-bold text-purple-400">
                  {(cosmicInfluence.overallEnergy * 100).toFixed(0)}%
                </span>
              </div>
              <div className="w-full bg-gray-600 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full transition-all duration-1000"
                  style={{ width: `${cosmicInfluence.overallEnergy * 100}%` }}
                />
              </div>
              <p className="text-sm text-gray-400 mt-2">
                Planète dominante: <span className="text-yellow-400">{cosmicInfluence.dominantPlanet}</span>
              </p>
            </div>

            <div>
              <h4 className="font-medium text-green-400 mb-2">Activités Favorables</h4>
              <ul className="space-y-1">
                {cosmicInfluence.favorableActivities.slice(0, 3).map((activity, index) => (
                  <li key={index} className="text-sm text-gray-300 flex items-center">
                    <TrendingUp className="w-3 h-3 mr-2 text-green-400" />
                    {activity}
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-red-400 mb-2">Zones de Défi</h4>
              <ul className="space-y-1">
                {cosmicInfluence.challengingAreas.map((area, index) => (
                  <li key={index} className="text-sm text-gray-300 flex items-center">
                    <Zap className="w-3 h-3 mr-2 text-red-400" />
                    {area}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="mt-4 p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
            <h4 className="font-medium text-blue-400 mb-2">Prochain Événement Significatif</h4>
            <p className="text-sm">
              <span className="font-medium">{cosmicInfluence.nextSignificantEvent.event}</span>
              <span className="text-gray-400 ml-2">
                {formatDate(cosmicInfluence.nextSignificantEvent.date)}
              </span>
            </p>
            <div className="flex items-center mt-2">
              <span className="text-xs text-gray-400 mr-2">Impact:</span>
              <div className="w-20 bg-gray-600 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full"
                  style={{ width: `${cosmicInfluence.nextSignificantEvent.impact * 100}%` }}
                />
              </div>
              <span className="text-xs text-blue-400 ml-2">
                {(cosmicInfluence.nextSignificantEvent.impact * 100).toFixed(0)}%
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Système solaire 3D */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <Orbit className="w-5 h-5 mr-2 text-blue-400" />
            Système Solaire en Temps Réel
          </h3>
          
          <canvas
            ref={canvasRef}
            width={400}
            height={300}
            className="w-full border border-gray-600 rounded-lg cursor-pointer"
            onClick={(e) => {
              // Logique pour sélectionner une planète au clic
              const rect = e.currentTarget.getBoundingClientRect();
              const x = e.clientX - rect.left;
              const y = e.clientY - rect.top;
              // Implémentation de la sélection de planète
            }}
          />
          
          <div className="mt-4 grid grid-cols-2 gap-2">
            {planetaryPositions.slice(0, 4).map((planet) => (
              <button
                key={planet.name}
                onClick={() => setSelectedPlanet(selectedPlanet === planet.name ? null : planet.name)}
                className={`p-2 rounded-lg text-left transition-colors ${
                  selectedPlanet === planet.name
                    ? 'bg-blue-500/20 border border-blue-500'
                    : darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <span style={{ color: planet.color }} className="text-lg">{planet.symbol}</span>
                  <div>
                    <p className="font-medium text-sm">{planet.name}</p>
                    <p className="text-xs text-gray-400">{planet.constellation}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Aspects astrologiques */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <Star className="w-5 h-5 mr-2 text-purple-400" />
            Aspects Astrologiques
          </h3>
          
          <div className="space-y-3">
            {astrologicalAspects.map((aspect, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{aspect.planet1}</span>
                    <span className="text-gray-400">{aspect.type}</span>
                    <span className="font-medium">{aspect.planet2}</span>
                  </div>
                  <span className={`text-sm px-2 py-1 rounded ${getAspectColor(aspect.influence)}`}>
                    {aspect.influence}
                  </span>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-400">
                  <span>Angle: {aspect.angle}° (orbe: {aspect.orb}°)</span>
                  <div className="flex items-center space-x-2">
                    <span>Force:</span>
                    <div className="w-16 bg-gray-600 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          aspect.influence === 'harmonious' ? 'bg-green-500' :
                          aspect.influence === 'challenging' ? 'bg-red-500' : 'bg-yellow-500'
                        }`}
                        style={{ width: `${aspect.strength * 100}%` }}
                      />
                    </div>
                    <span>{(aspect.strength * 100).toFixed(0)}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Optimisation des décisions */}
      <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
        <h3 className="text-xl font-semibold mb-4 flex items-center">
          <Clock className="w-5 h-5 mr-2 text-green-400" />
          Optimisation Cosmique des Décisions
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {decisionOptimizations.map((decision) => (
            <div
              key={decision.id}
              className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
            >
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">{decision.decision}</h4>
                <span className={`text-xs px-2 py-1 rounded font-medium ${getRecommendationColor(decision.recommendation)}`}>
                  {decision.recommendation === 'proceed' ? 'Procéder' :
                   decision.recommendation === 'wait' ? 'Attendre' : 'Reconsidérer'}
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Timing actuel:</span>
                  <span className={`font-medium ${
                    decision.currentTiming > 0.7 ? 'text-green-400' :
                    decision.currentTiming > 0.4 ? 'text-yellow-400' : 'text-red-400'
                  }`}>
                    {(decision.currentTiming * 100).toFixed(0)}%
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Timing optimal:</span>
                  <span className="font-medium text-blue-400">
                    {decision.optimalTiming.toLocaleDateString('fr-FR', {
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Confiance:</span>
                  <span className="font-medium text-purple-400">
                    {(decision.confidence * 100).toFixed(0)}%
                  </span>
                </div>
              </div>
              
              <div className="mt-3">
                <p className="text-xs text-gray-400 mb-1">Facteurs cosmiques:</p>
                <div className="flex flex-wrap gap-1">
                  {decision.cosmicFactors.map((factor, index) => (
                    <span
                      key={index}
                      className="text-xs px-2 py-1 bg-purple-500/20 text-purple-400 rounded"
                    >
                      {factor}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HanumanPlanetaryInterface;
