# Agent Activation Protocol

Ce document définit le protocole d'activation des agents spécialisés dans le workflow VIMANA.

## Commandes d'activation

Pour activer un agent spécifique, utilisez la syntaxe suivante:

```
@Roo Orchestrator activate [AgentName] for [specific task or phase]
```

## Exemples d'activation par phase

### Phase 1: Idée & Concept
```
@Roo Orchestrator activate UXResearcher for user journey mapping
@Roo Orchestrator activate EthicalAI for ethical framework definition
```

### Phase 2: PRD & Spécifications
```
@Roo Orchestrator activate SecurityRecon for security boundary mapping
@Roo Orchestrator activate ThreatModeler for STRIDE analysis
@Roo Orchestrator activate ComplianceOfficer for regulatory assessment
```

### Phase 3: Architecture & Planning
```
@Roo Orchestrator activate SecurityArchitect for security control design
@Roo Orchestrator activate DevOpsArchitect for CI/CD pipeline design
@Roo Orchestrator activate TestingStrategist for test strategy development
```

### Phase 4: Implémentation
```
@Roo Orchestrator activate CryptoAuditor for cryptographic implementation review
@Roo Orchestrator activate SupplyChain<PERSON><PERSON><PERSON> for dependency analysis
@Roo Orchestrator activate DataScientist for ML model development
```

### Phase 5: Optimisation
```
@Roo Orchestrator activate PerformanceOptimizer for performance analysis
@Roo Orchestrator activate AccessibilityEngineer for WCAG compliance
@Roo Orchestrator activate LocalizationSpecialist for i18n setup
@Roo Orchestrator activate SEOSpecialist for SEO optimization
@Roo Orchestrator activate MobileOptimizer for responsive design
@Roo Orchestrator activate SustainabilityEngineer for carbon footprint reduction
```

### Phase 6: Validation
```
@Roo Orchestrator activate VulnerabilityHunter for security testing
@Roo Orchestrator activate DocumentationEngineer for documentation generation
@Roo Orchestrator activate ComplianceVerifier for compliance validation
```

## Configuration des agents

Chaque agent peut être configuré avec des paramètres spécifiques:

```
@Roo Orchestrator configure [AgentName] with [parameters]
```

Exemple:
```
@Roo Orchestrator configure PerformanceOptimizer with {"focusAreas": ["database", "frontend"], "threshold": 0.8}
```

## Intégration avec les tâches

Pour assigner une tâche spécifique à un agent:

```
@Roo Orchestrator assign task [TaskID] to [AgentName]
```

Exemple:
```
@Roo Orchestrator assign task T003 to PerformanceOptimizer
```

## Collaboration entre agents

Pour initier une collaboration entre agents:

```
@Roo Orchestrator collaborate [AgentName1] with [AgentName2] on [specific task]
```

Exemple:
```
@Roo Orchestrator collaborate SecurityArchitect with DevOpsArchitect on secure infrastructure design
```