#!/bin/bash
# 🚁 VIMANA Divine Startup Script

echo "🕉️ =============================================="
echo "🚁 Starting VIMANA Divine Framework..."
echo "🕉️ =============================================="

# Invocation divine
echo "📿 AUM GANAPATAYE NAMAHA - Removing obstacles..."
echo "🌟 AUM SARASWATYAI NAMAHA - Blessing with wisdom..."
echo "⚡ AUM HANUMATE NAMAHA - Empowering with strength..."

# Vérification divine
echo "🔍 Checking divine configuration..."
if [ ! -f "vimana.config.js" ]; then
    echo "❌ Divine configuration missing! Please run setup first."
    exit 1
fi

echo "✅ Divine configuration found"
echo "🚀 VIMANA is ready for sacred development!"
echo ""
echo "Available divine commands:"
echo "  npm run vimana:create   - Invoke Brahma Creator"
echo "  npm run vimana:test     - Invoke Vishnu Preserver" 
echo "  npm run vimana:refactor - Invoke Shiva Transformer"
echo ""
echo "🕉️ May your code be blessed with cosmic wisdom! 🕉️"
