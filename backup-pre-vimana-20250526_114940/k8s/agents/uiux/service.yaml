apiVersion: v1
kind: Service
metadata:
  name: agent-uiux
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "3005"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 3005
    targetPort: http
    protocol: TCP
  selector:
    app: agent-uiux

---
apiVersion: v1
kind: Service
metadata:
  name: agent-uiux-headless
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 3005
    targetPort: http
    protocol: TCP
  selector:
    app: agent-uiux
