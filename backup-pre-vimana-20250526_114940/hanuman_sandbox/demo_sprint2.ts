#!/usr/bin/env ts-node

/**
 * 🚀 DÉMONSTRATION SPRINT 2 - IDE HANUMAN
 * ========================================
 * 
 * Démonstration complète de l'IDE intégré avec VS Code + Roo Coder
 * Templates, Git et Simulation d'environnement
 */

import { HanumanSandbox, createHanumanSandbox } from './index';

// Mock de l'orchestrateur pour la démonstration
class MockHanumanOrchestrator {
  private listeners: Map<string, Function[]> = new Map();

  emit(event: string, data?: any): void {
    console.log(`🧠 [Orchestrateur] Événement: ${event}`);
    
    const handlers = this.listeners.get(event) || [];
    handlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`❌ Erreur dans le gestionnaire d'événement ${event}:`, error);
      }
    });
  }

  on(event: string, handler: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(handler);
  }

  off(event: string, handler: Function): void {
    const handlers = this.listeners.get(event) || [];
    const index = handlers.indexOf(handler);
    if (index > -1) {
      handlers.splice(index, 1);
    }
  }

  // Simulation d'activité de l'orchestrateur
  simulateActivity(): void {
    setInterval(() => {
      this.emit('neural:signal-generated', {
        signal: {
          id: `signal_${Date.now()}`,
          source: 'cortex-creatif',
          target: 'agent-frontend',
          type: 'development_request',
          data: { action: 'create_component' }
        }
      });
    }, 15000); // Toutes les 15 secondes
  }
}

/**
 * Fonction principale de démonstration du Sprint 2
 */
async function demonstrateIDEFeatures(): Promise<void> {
  console.log('💻 DÉMONSTRATION SPRINT 2 - IDE HANUMAN');
  console.log('========================================\n');

  try {
    // 1. Créer l'orchestrateur mock
    console.log('🧠 Création de l\'orchestrateur mock...');
    const orchestrator = new MockHanumanOrchestrator();
    orchestrator.simulateActivity();

    // 2. Créer et configurer la sandbox avec IDE
    console.log('🏗️ Création de la sandbox avec IDE...');
    const sandbox = createHanumanSandbox(orchestrator as any, {
      autoStart: true,
      enableSecurity: true,
      enableMonitoring: true,
      enableIDE: true,
      enableTesting: false,
      logLevel: 'info',
      maxContainers: 20,
      defaultSecurityLevel: 'medium',
      ideConfig: {
        vscode: {
          enabled: true,
          autoStart: true,
          defaultPort: 8080,
          extensions: [
            'ms-vscode.vscode-typescript-next',
            'esbenp.prettier-vscode',
            'ms-vscode.vscode-eslint',
            'roo-coder.roo-coder'
          ]
        },
        rooCoder: {
          enabled: true,
          model: 'gpt-4',
          customPrompts: true
        },
        templates: {
          enabled: true,
          autoLoad: true,
          customTemplates: true
        },
        git: {
          enabled: true,
          autoCommit: true,
          autoCommitInterval: 10 // 10 minutes pour la démo
        },
        simulator: {
          enabled: true,
          hotReload: true,
          monitoring: true
        }
      }
    });

    // 3. Configurer les événements de la sandbox
    setupSandboxEventHandlers(sandbox);

    // 4. Attendre que la sandbox soit prête
    console.log('⏳ Attente de l\'initialisation...');
    await new Promise(resolve => {
      sandbox.on('sandbox:started', resolve);
    });

    // 5. Afficher le statut initial
    console.log('\n📊 STATUT INITIAL AVEC IDE');
    console.log('===========================');
    displaySandboxStatus(sandbox);

    // 6. Créer des projets de développement avec l'IDE
    console.log('\n🚀 CRÉATION DE PROJETS DE DÉVELOPPEMENT');
    console.log('=======================================');
    await createDevelopmentProjects(sandbox);

    // 7. Afficher le statut après création des projets
    console.log('\n📊 STATUT APRÈS CRÉATION DES PROJETS');
    console.log('====================================');
    displaySandboxStatus(sandbox);

    // 8. Démonstration des fonctionnalités IDE
    console.log('\n💻 DÉMONSTRATION DES FONCTIONNALITÉS IDE');
    console.log('========================================');
    await demonstrateIDEComponents(sandbox);

    // 9. Simulation d'activité de développement
    console.log('\n🎭 SIMULATION D\'ACTIVITÉ DE DÉVELOPPEMENT');
    console.log('=========================================');
    await simulateDevelopmentActivity(sandbox);

    // 10. Afficher les métriques finales
    console.log('\n📈 MÉTRIQUES FINALES IDE');
    console.log('========================');
    displayFinalIDEMetrics(sandbox);

    // 11. Maintenir la sandbox en vie pour observation
    console.log('\n👀 OBSERVATION CONTINUE');
    console.log('=======================');
    console.log('La sandbox avec IDE continue de fonctionner...');
    console.log('Appuyez sur Ctrl+C pour arrêter.\n');

    // Monitoring continu
    startContinuousIDEMonitoring(sandbox);

  } catch (error) {
    console.error('❌ Erreur lors de la démonstration:', error);
    process.exit(1);
  }
}

/**
 * Configure les gestionnaires d'événements de la sandbox
 */
function setupSandboxEventHandlers(sandbox: HanumanSandbox): void {
  // Événements de base
  sandbox.on('sandbox:initialized', () => {
    console.log('✅ Sandbox avec IDE initialisée');
  });

  sandbox.on('sandbox:started', () => {
    console.log('🚀 Sandbox avec IDE démarrée');
  });

  // Événements IDE spécifiques
  sandbox.on('sandbox:project-created', (data) => {
    console.log(`🎉 Projet créé: ${data.container.name}`);
    console.log(`   💻 VS Code: ${data.vscodeInstance.url}`);
    if (data.repository) {
      console.log(`   📁 Git: ${data.repository.name}`);
    }
    if (data.simulation) {
      console.log(`   🎭 Simulation: ${data.simulation.name}`);
    }
  });

  sandbox.on('sandbox:vscode-deployed', (instance) => {
    console.log(`💻 VS Code déployé: ${instance.url}`);
    if (instance.tunnelUrl) {
      console.log(`   🌐 Tunnel: ${instance.tunnelUrl}`);
    }
  });

  sandbox.on('sandbox:ide-error', (error) => {
    console.error('❌ Erreur IDE:', error);
  });

  // Événements de sécurité
  sandbox.on('sandbox:security-incident', (incident) => {
    console.log(`🚨 Incident de sécurité [${incident.severity}]: ${incident.description}`);
  });
}

/**
 * Affiche le statut de la sandbox avec IDE
 */
function displaySandboxStatus(sandbox: HanumanSandbox): void {
  const status = sandbox.getStatus();
  const config = sandbox.getConfig();

  console.log(`🏗️ Infrastructure:`);
  console.log(`   - Initialisée: ${status.infrastructure.initialized ? '✅' : '❌'}`);
  console.log(`   - Conteneurs: ${status.infrastructure.containers}`);
  console.log(`   - Namespaces: ${status.infrastructure.namespaces}`);

  console.log(`🛡️ Sécurité:`);
  console.log(`   - Activée: ${status.security.enabled ? '✅' : '❌'}`);
  console.log(`   - Score: ${status.security.score}%`);
  console.log(`   - Incidents: ${status.security.incidents}`);

  console.log(`💻 IDE:`);
  console.log(`   - Activé: ${status.ide.enabled ? '✅' : '❌'}`);
  console.log(`   - Instances VS Code: ${status.ide.vscodeInstances}`);
  console.log(`   - Projets actifs: ${status.ide.activeProjects}`);
  console.log(`   - Dépôts Git: ${status.ide.gitRepositories}`);
  console.log(`   - Simulations: ${status.ide.runningSimulations}`);

  if (status.isRunning) {
    const uptimeHours = (status.performance.uptime / (1000 * 60 * 60)).toFixed(2);
    console.log(`⏱️ Uptime: ${uptimeHours}h`);
  }
}

/**
 * Crée des projets de développement avec l'IDE
 */
async function createDevelopmentProjects(sandbox: HanumanSandbox): Promise<void> {
  const projects = [
    {
      name: 'agent-frontend-project',
      agentId: 'agent-frontend',
      organId: 'cortex-creatif',
      templateId: 'hanuman-agent-complete'
    },
    {
      name: 'agent-backend-project',
      agentId: 'agent-backend',
      organId: 'cortex-logique',
      templateId: 'hanuman-agent-complete'
    },
    {
      name: 'agent-security-project',
      agentId: 'agent-security',
      organId: 'cortex-protection',
      templateId: 'hanuman-agent-complete'
    }
  ];

  for (const project of projects) {
    try {
      console.log(`🚀 Création du projet: ${project.name}`);
      const result = await sandbox.createDevelopmentProject(project);
      console.log(`   ✅ Projet créé avec succès`);
      console.log(`   💻 VS Code: ${result.vscodeInstance.url}`);
      
      // Attendre un peu entre les créations
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.log(`   ❌ Erreur: ${error}`);
    }
  }
}

/**
 * Démontre les composants de l'IDE
 */
async function demonstrateIDEComponents(sandbox: HanumanSandbox): Promise<void> {
  const components = sandbox.getComponents();
  
  if (components.ide) {
    const ideComponents = components.ide.getComponents();
    
    console.log('📋 Templates disponibles:');
    const templates = ideComponents.templateManager?.getTemplates() || [];
    templates.forEach(template => {
      console.log(`   - ${template.name} (${template.category})`);
    });

    console.log('\n💻 Instances VS Code:');
    const vscodeInstances = ideComponents.vscodeManager?.getInstances() || [];
    vscodeInstances.forEach(instance => {
      console.log(`   - ${instance.agentId || instance.organId}: ${instance.url}`);
    });

    console.log('\n📁 Dépôts Git:');
    const repositories = ideComponents.gitManager?.getRepositories() || [];
    repositories.forEach(repo => {
      console.log(`   - ${repo.name} (branche: ${repo.branch})`);
    });

    console.log('\n🎭 Simulations:');
    const simulations = ideComponents.environmentSimulator?.getEnvironments() || [];
    simulations.forEach(sim => {
      console.log(`   - ${sim.name} (${sim.status})`);
    });
  }
}

/**
 * Simule de l'activité de développement
 */
async function simulateDevelopmentActivity(sandbox: HanumanSandbox): Promise<void> {
  const activities = [
    'Modification de composant React',
    'Ajout de nouvelle fonctionnalité',
    'Correction de bug',
    'Mise à jour des tests',
    'Optimisation des performances',
    'Refactoring du code'
  ];

  for (let i = 0; i < activities.length; i++) {
    console.log(`   ${i + 1}. ${activities[i]}...`);
    
    // Simuler du temps de développement
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log(`   ✅ ${activities[i]} terminé`);
    
    // Simuler hot-reload
    if (i % 2 === 0) {
      console.log(`   🔥 Hot-reload déclenché`);
    }
  }

  console.log('🎉 Session de développement terminée');
}

/**
 * Affiche les métriques finales de l'IDE
 */
function displayFinalIDEMetrics(sandbox: HanumanSandbox): void {
  const status = sandbox.getStatus();
  const components = sandbox.getComponents();

  console.log('📊 Résumé des métriques IDE:');
  console.log(`   - Instances VS Code: ${status.ide.vscodeInstances}`);
  console.log(`   - Projets actifs: ${status.ide.activeProjects}`);
  console.log(`   - Dépôts Git: ${status.ide.gitRepositories}`);
  console.log(`   - Simulations: ${status.ide.runningSimulations}`);
  console.log(`   - Uptime: ${(status.performance.uptime / 1000).toFixed(1)}s`);

  if (components.ide) {
    const ideStats = components.ide.getStats();
    console.log(`   - Templates disponibles: ${ideStats.templatesAvailable}`);
    console.log(`   - Agents total: ${ideStats.totalAgents}`);
  }
}

/**
 * Démarre le monitoring continu de l'IDE
 */
function startContinuousIDEMonitoring(sandbox: HanumanSandbox): void {
  // Affichage périodique du statut IDE
  setInterval(() => {
    const status = sandbox.getStatus();
    const timestamp = new Date().toLocaleTimeString();
    
    console.log(`[${timestamp}] 💻 VS Code: ${status.ide.vscodeInstances} | ` +
                `Projets: ${status.ide.activeProjects} | ` +
                `Git: ${status.ide.gitRepositories} | ` +
                `Simulations: ${status.ide.runningSimulations}`);
  }, 30000); // Toutes les 30 secondes

  // Gestion de l'arrêt propre
  process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt de la sandbox avec IDE...');
    try {
      await sandbox.stop();
      console.log('✅ Sandbox avec IDE arrêtée proprement');
      process.exit(0);
    } catch (error) {
      console.error('❌ Erreur lors de l\'arrêt:', error);
      process.exit(1);
    }
  });
}

/**
 * Point d'entrée principal
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
💻 Script de Démonstration Sprint 2 - IDE Hanuman
==================================================

Usage: ts-node demo_sprint2.ts [options]

Options:
  --help, -h     Affiche cette aide

Fonctionnalités démontrées:
  - VS Code Server avec Roo Coder
  - Templates de projets Hanuman
  - Gestion Git intégrée
  - Simulation d'environnement avec hot-reload
  - Monitoring en temps réel

Exemples:
  ts-node demo_sprint2.ts    # Démonstration complète
`);
    return;
  }

  await demonstrateIDEFeatures();
}

// Lancer le script
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Erreur fatale:', error);
    process.exit(1);
  });
}

export { demonstrateIDEFeatures, MockHanumanOrchestrator };
