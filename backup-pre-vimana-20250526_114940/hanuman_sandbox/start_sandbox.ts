#!/usr/bin/env ts-node

/**
 * 🚀 Script de Démarrage de la Sandbox Hanuman
 * =============================================
 * 
 * Ce script démarre la sandbox avec une configuration de test
 * et lance une démonstration complète des fonctionnalités.
 */

import { HanumanSandbox, createHanumanSandbox } from './index';
import { runSandboxTests } from './tests/infrastructure_tests';

// Mock de l'orchestrateur pour la démonstration
class MockHanumanOrchestrator {
  private listeners: Map<string, Function[]> = new Map();

  emit(event: string, data?: any): void {
    console.log(`🧠 [Orchestrateur] Événement: ${event}`, data ? `- ${JSON.stringify(data, null, 2)}` : '');
    
    const handlers = this.listeners.get(event) || [];
    handlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`❌ Erreur dans le gestionnaire d'événement ${event}:`, error);
      }
    });
  }

  on(event: string, handler: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(handler);
    console.log(`👂 [Orchestrateur] Écoute de l'événement: ${event}`);
  }

  off(event: string, handler: Function): void {
    const handlers = this.listeners.get(event) || [];
    const index = handlers.indexOf(handler);
    if (index > -1) {
      handlers.splice(index, 1);
      console.log(`🔇 [Orchestrateur] Arrêt de l'écoute: ${event}`);
    }
  }

  // Simulation d'activité de l'orchestrateur
  simulateActivity(): void {
    setInterval(() => {
      this.emit('neural:signal-generated', {
        signal: {
          id: `signal_${Date.now()}`,
          source: 'cortex-creatif',
          target: 'agent-frontend',
          type: 'development_request',
          data: { action: 'create_component' }
        },
        organ: { id: 'cortex-creatif', name: 'Cortex Créatif' }
      });
    }, 10000); // Toutes les 10 secondes
  }
}

/**
 * Fonction principale de démonstration
 */
async function demonstrateSandbox(): Promise<void> {
  console.log('🏗️ DÉMONSTRATION SANDBOX HANUMAN');
  console.log('=================================\n');

  try {
    // 1. Créer l'orchestrateur mock
    console.log('🧠 Création de l\'orchestrateur mock...');
    const orchestrator = new MockHanumanOrchestrator();
    orchestrator.simulateActivity();

    // 2. Créer et configurer la sandbox
    console.log('🏗️ Création de la sandbox...');
    const sandbox = createHanumanSandbox(orchestrator as any, {
      autoStart: true,
      enableSecurity: true,
      enableMonitoring: true,
      enableTesting: true,
      logLevel: 'info',
      maxContainers: 20,
      defaultSecurityLevel: 'medium'
    });

    // 3. Configurer les événements de la sandbox
    setupSandboxEventHandlers(sandbox);

    // 4. Attendre que la sandbox soit prête
    console.log('⏳ Attente de l\'initialisation...');
    await new Promise(resolve => {
      sandbox.on('sandbox:started', resolve);
    });

    // 5. Afficher le statut initial
    console.log('\n📊 STATUT INITIAL');
    console.log('==================');
    displaySandboxStatus(sandbox);

    // 6. Créer des environnements de démonstration
    console.log('\n🧪 CRÉATION D\'ENVIRONNEMENTS DE DÉMONSTRATION');
    console.log('===============================================');
    await createDemoEnvironments(sandbox);

    // 7. Afficher le statut après création
    console.log('\n📊 STATUT APRÈS CRÉATION');
    console.log('=========================');
    displaySandboxStatus(sandbox);

    // 8. Lancer les tests si activés
    if (sandbox.getConfig().enableTesting) {
      console.log('\n🧪 LANCEMENT DES TESTS');
      console.log('======================');
      await sandbox.runTests();
    }

    // 9. Simulation d'activité
    console.log('\n🎭 SIMULATION D\'ACTIVITÉ');
    console.log('========================');
    await simulateActivity(sandbox);

    // 10. Afficher les métriques finales
    console.log('\n📈 MÉTRIQUES FINALES');
    console.log('====================');
    displayFinalMetrics(sandbox);

    // 11. Maintenir la sandbox en vie pour observation
    console.log('\n👀 OBSERVATION CONTINUE');
    console.log('=======================');
    console.log('La sandbox continue de fonctionner...');
    console.log('Appuyez sur Ctrl+C pour arrêter.\n');

    // Monitoring continu
    startContinuousMonitoring(sandbox);

  } catch (error) {
    console.error('❌ Erreur lors de la démonstration:', error);
    process.exit(1);
  }
}

/**
 * Configure les gestionnaires d'événements de la sandbox
 */
function setupSandboxEventHandlers(sandbox: HanumanSandbox): void {
  sandbox.on('sandbox:initialized', () => {
    console.log('✅ Sandbox initialisée');
  });

  sandbox.on('sandbox:started', () => {
    console.log('🚀 Sandbox démarrée');
  });

  sandbox.on('sandbox:container-created', (container) => {
    console.log(`📦 Nouveau conteneur: ${container.name} (${container.type})`);
  });

  sandbox.on('sandbox:container-destroyed', (container) => {
    console.log(`🗑️ Conteneur détruit: ${container.name}`);
  });

  sandbox.on('sandbox:security-incident', (incident) => {
    console.log(`🚨 Incident de sécurité [${incident.severity}]: ${incident.description}`);
  });

  sandbox.on('sandbox:security-scan', (metrics) => {
    console.log(`🔍 Scan de sécurité terminé - Score: ${metrics.securityScore}%`);
  });

  sandbox.on('sandbox:metrics', (metrics) => {
    console.log(`📊 Métriques: ${metrics.containers} conteneurs, Score: ${metrics.securityScore}%`);
  });

  sandbox.on('sandbox:error', (error) => {
    console.error('❌ Erreur sandbox:', error);
  });
}

/**
 * Affiche le statut de la sandbox
 */
function displaySandboxStatus(sandbox: HanumanSandbox): void {
  const status = sandbox.getStatus();
  const config = sandbox.getConfig();

  console.log(`🏗️ Infrastructure:`);
  console.log(`   - Initialisée: ${status.infrastructure.initialized ? '✅' : '❌'}`);
  console.log(`   - Conteneurs: ${status.infrastructure.containers}`);
  console.log(`   - Namespaces: ${status.infrastructure.namespaces}`);
  console.log(`   - Réseaux: ${status.infrastructure.networks}`);

  console.log(`🛡️ Sécurité:`);
  console.log(`   - Activée: ${status.security.enabled ? '✅' : '❌'}`);
  console.log(`   - Score: ${status.security.score}%`);
  console.log(`   - Incidents: ${status.security.incidents}`);
  console.log(`   - Politiques: ${status.security.policies}`);

  console.log(`⚙️ Configuration:`);
  console.log(`   - Niveau sécurité: ${config.defaultSecurityLevel}`);
  console.log(`   - Max conteneurs: ${config.maxContainers}`);
  console.log(`   - Monitoring: ${config.enableMonitoring ? '✅' : '❌'}`);

  if (status.isRunning) {
    const uptimeHours = (status.performance.uptime / (1000 * 60 * 60)).toFixed(2);
    console.log(`⏱️ Uptime: ${uptimeHours}h`);
  }
}

/**
 * Crée des environnements de démonstration
 */
async function createDemoEnvironments(sandbox: HanumanSandbox): Promise<void> {
  const environments = [
    {
      name: 'Frontend Development',
      agentId: 'agent-frontend',
      organId: 'cortex-creatif',
      securityLevel: 'medium' as const
    },
    {
      name: 'Backend Testing',
      agentId: 'agent-backend',
      organId: 'cortex-logique',
      securityLevel: 'high' as const
    },
    {
      name: 'Security Validation',
      agentId: 'agent-security',
      organId: 'cortex-protection',
      securityLevel: 'maximum' as const
    },
    {
      name: 'QA Environment',
      agentId: 'agent-qa',
      organId: 'cortex-validation',
      securityLevel: 'high' as const
    }
  ];

  for (const env of environments) {
    try {
      console.log(`🧪 Création de l'environnement: ${env.name}`);
      const container = await sandbox.createDevelopmentEnvironment(env);
      console.log(`   ✅ Créé avec succès: ${container.id}`);
      
      // Attendre un peu entre les créations
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.log(`   ❌ Erreur: ${error}`);
    }
  }
}

/**
 * Simule de l'activité dans la sandbox
 */
async function simulateActivity(sandbox: HanumanSandbox): Promise<void> {
  console.log('🎭 Simulation de développement d\'agents...');
  
  // Simuler des événements de développement
  const activities = [
    'Création de composant React',
    'Test d\'API backend',
    'Validation de sécurité',
    'Optimisation de performance',
    'Déploiement en staging'
  ];

  for (let i = 0; i < activities.length; i++) {
    console.log(`   ${i + 1}. ${activities[i]}...`);
    
    // Simuler du temps de traitement
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log(`   ✅ ${activities[i]} terminé`);
  }

  console.log('🎉 Simulation d\'activité terminée');
}

/**
 * Affiche les métriques finales
 */
function displayFinalMetrics(sandbox: HanumanSandbox): void {
  const status = sandbox.getStatus();
  const { infrastructure, security } = sandbox.getComponents();

  console.log('📊 Résumé des métriques:');
  console.log(`   - Conteneurs créés: ${status.infrastructure.containers}`);
  console.log(`   - Score de sécurité: ${status.security.score}%`);
  console.log(`   - Incidents détectés: ${status.security.incidents}`);
  console.log(`   - Uptime: ${(status.performance.uptime / 1000).toFixed(1)}s`);

  // Métriques détaillées de sécurité
  if (security) {
    const securityMetrics = security.getSecurityMetrics();
    console.log(`   - Menaces bloquées: ${securityMetrics.threatsBlocked}`);
    console.log(`   - Politiques actives: ${securityMetrics.policiesActive}`);
  }
}

/**
 * Démarre le monitoring continu
 */
function startContinuousMonitoring(sandbox: HanumanSandbox): void {
  // Affichage périodique du statut
  setInterval(() => {
    const status = sandbox.getStatus();
    const timestamp = new Date().toLocaleTimeString();
    
    console.log(`[${timestamp}] 📊 Conteneurs: ${status.infrastructure.containers} | ` +
                `Sécurité: ${status.security.score}% | ` +
                `Uptime: ${(status.performance.uptime / 1000 / 60).toFixed(1)}min`);
  }, 30000); // Toutes les 30 secondes

  // Gestion de l'arrêt propre
  process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt de la sandbox...');
    try {
      await sandbox.stop();
      console.log('✅ Sandbox arrêtée proprement');
      process.exit(0);
    } catch (error) {
      console.error('❌ Erreur lors de l\'arrêt:', error);
      process.exit(1);
    }
  });
}

/**
 * Point d'entrée principal
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🏗️ Script de Démarrage Sandbox Hanuman
======================================

Usage: ts-node start_sandbox.ts [options]

Options:
  --help, -h     Affiche cette aide
  --test-only    Lance uniquement les tests
  --demo         Lance la démonstration complète (défaut)

Exemples:
  ts-node start_sandbox.ts                # Démonstration complète
  ts-node start_sandbox.ts --test-only    # Tests uniquement
`);
    return;
  }

  if (args.includes('--test-only')) {
    console.log('🧪 LANCEMENT DES TESTS UNIQUEMENT');
    console.log('==================================\n');
    await runSandboxTests();
  } else {
    await demonstrateSandbox();
  }
}

// Lancer le script
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Erreur fatale:', error);
    process.exit(1);
  });
}

export { demonstrateSandbox, MockHanumanOrchestrator };
