import { EventEmitter } from 'events';

// Types pour la gestion des versions
export interface Version {
  major: number;
  minor: number;
  patch: number;
  prerelease?: string;
  build?: string;
}

export interface Release {
  id: string;
  version: string;
  name: string;
  description: string;
  type: 'major' | 'minor' | 'patch' | 'prerelease' | 'hotfix';
  status: 'draft' | 'pending' | 'released' | 'deprecated' | 'archived';
  createdAt: Date;
  releasedAt?: Date;
  createdBy: string;
  releasedBy?: string;
  changelog: ChangelogEntry[];
  artifacts: ReleaseArtifact[];
  dependencies: ReleaseDependency[];
  rollbackInfo?: RollbackInfo;
  metrics: ReleaseMetrics;
  tags: string[];
}

export interface ChangelogEntry {
  type: 'feature' | 'bugfix' | 'improvement' | 'breaking' | 'security' | 'deprecated';
  title: string;
  description: string;
  component: string;
  author: string;
  commitHash?: string;
  issueNumber?: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
}

export interface ReleaseArtifact {
  name: string;
  type: 'binary' | 'source' | 'documentation' | 'container' | 'package';
  path: string;
  size: number;
  checksum: string;
  downloadCount: number;
  createdAt: Date;
  metadata: Record<string, any>;
}

export interface ReleaseDependency {
  name: string;
  version: string;
  type: 'required' | 'optional' | 'development';
  source: string;
  license?: string;
}

export interface RollbackInfo {
  previousVersion: string;
  rollbackReason: string;
  rollbackDate: Date;
  rollbackBy: string;
  rollbackDuration: number;
  success: boolean;
  issues: string[];
}

export interface ReleaseMetrics {
  deploymentTime: number;
  adoptionRate: number;
  errorRate: number;
  performanceImpact: number;
  userFeedback: {
    positive: number;
    negative: number;
    neutral: number;
  };
  downloads: number;
  rollbacks: number;
}

export interface VersioningStrategy {
  type: 'semantic' | 'calendar' | 'sequential' | 'custom';
  autoIncrement: boolean;
  prereleasePattern?: string;
  buildPattern?: string;
  branchMapping: Record<string, string>;
}

export interface ComparisonResult {
  fromVersion: string;
  toVersion: string;
  changes: VersionChange[];
  compatibility: CompatibilityInfo;
  migrationGuide?: string;
  breakingChanges: BreakingChange[];
}

export interface VersionChange {
  type: 'added' | 'modified' | 'removed' | 'deprecated';
  component: string;
  description: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  migration?: string;
}

export interface CompatibilityInfo {
  backward: boolean;
  forward: boolean;
  apiCompatible: boolean;
  dataCompatible: boolean;
  configCompatible: boolean;
  issues: string[];
}

export interface BreakingChange {
  component: string;
  description: string;
  reason: string;
  migration: string;
  workaround?: string;
  deprecatedIn?: string;
  removedIn: string;
}

/**
 * Gestionnaire de Versions Hanuman
 * Gestion du versioning sémantique et des releases
 */
export class VersionManager extends EventEmitter {
  private releases: Map<string, Release> = new Map();
  private currentVersion: Version = { major: 1, minor: 0, patch: 0 };
  private strategy: VersioningStrategy;
  private isInitialized = false;

  constructor(strategy?: Partial<VersioningStrategy>) {
    super();

    this.strategy = {
      type: 'semantic',
      autoIncrement: true,
      prereleasePattern: 'alpha|beta|rc',
      buildPattern: 'build.{timestamp}',
      branchMapping: {
        'main': 'release',
        'develop': 'prerelease',
        'feature/*': 'development',
        'hotfix/*': 'hotfix'
      },
      ...strategy
    };

    this.log('info', '📦 Gestionnaire de Versions Hanuman initialisé');
  }

  /**
   * Initialise le gestionnaire de versions
   */
  async initialize(initialVersion?: string): Promise<void> {
    if (this.isInitialized) {
      throw new Error('Le gestionnaire de versions est déjà initialisé');
    }

    if (initialVersion) {
      this.currentVersion = this.parseVersion(initialVersion);
    }

    this.isInitialized = true;
    this.log('info', `🚀 Gestionnaire de versions initialisé avec la version ${this.formatVersion(this.currentVersion)}`);

    this.emit('version_manager_initialized', this.currentVersion);
  }

  /**
   * Crée une nouvelle release
   */
  async createRelease(
    type: 'major' | 'minor' | 'patch' | 'prerelease' | 'hotfix',
    options: {
      name?: string;
      description?: string;
      changelog?: ChangelogEntry[];
      createdBy: string;
      tags?: string[];
    }
  ): Promise<string> {
    const newVersion = this.calculateNextVersion(type);
    const versionString = this.formatVersion(newVersion);

    const release: Release = {
      id: this.generateId(),
      version: versionString,
      name: options.name || `Release ${versionString}`,
      description: options.description || '',
      type,
      status: 'draft',
      createdAt: new Date(),
      createdBy: options.createdBy,
      changelog: options.changelog || [],
      artifacts: [],
      dependencies: [],
      metrics: {
        deploymentTime: 0,
        adoptionRate: 0,
        errorRate: 0,
        performanceImpact: 0,
        userFeedback: { positive: 0, negative: 0, neutral: 0 },
        downloads: 0,
        rollbacks: 0
      },
      tags: options.tags || []
    };

    this.releases.set(release.id, release);
    this.log('info', `📝 Release créée: ${release.name} (${versionString})`);

    this.emit('release_created', release);
    return release.id;
  }

  /**
   * Publie une release
   */
  async publishRelease(releaseId: string, publishedBy: string): Promise<void> {
    const release = this.releases.get(releaseId);
    if (!release) {
      throw new Error(`Release non trouvée: ${releaseId}`);
    }

    if (release.status !== 'draft' && release.status !== 'pending') {
      throw new Error(`Impossible de publier une release avec le statut: ${release.status}`);
    }

    // Valider la release avant publication
    await this.validateRelease(release);

    release.status = 'released';
    release.releasedAt = new Date();
    release.releasedBy = publishedBy;

    // Mettre à jour la version courante
    this.currentVersion = this.parseVersion(release.version);

    this.log('info', `🚀 Release publiée: ${release.name} (${release.version})`);
    this.emit('release_published', release);
  }

  /**
   * Effectue un rollback vers une version précédente
   */
  async rollbackToVersion(
    targetVersion: string,
    reason: string,
    rolledBackBy: string
  ): Promise<void> {
    const targetRelease = this.findReleaseByVersion(targetVersion);
    if (!targetRelease) {
      throw new Error(`Version cible non trouvée: ${targetVersion}`);
    }

    const currentRelease = this.getCurrentRelease();
    if (!currentRelease) {
      throw new Error('Aucune release courante trouvée');
    }

    const rollbackStart = Date.now();

    try {
      // Effectuer le rollback
      await this.performRollback(currentRelease, targetRelease);

      const rollbackDuration = Date.now() - rollbackStart;

      // Enregistrer les informations de rollback
      currentRelease.rollbackInfo = {
        previousVersion: targetVersion,
        rollbackReason: reason,
        rollbackDate: new Date(),
        rollbackBy: rolledBackBy,
        rollbackDuration,
        success: true,
        issues: []
      };

      // Mettre à jour la version courante
      this.currentVersion = this.parseVersion(targetVersion);

      this.log('info', `↩️ Rollback effectué vers ${targetVersion} en ${rollbackDuration}ms`);
      this.emit('rollback_completed', { from: currentRelease.version, to: targetVersion });

    } catch (error) {
      const rollbackDuration = Date.now() - rollbackStart;

      currentRelease.rollbackInfo = {
        previousVersion: targetVersion,
        rollbackReason: reason,
        rollbackDate: new Date(),
        rollbackBy: rolledBackBy,
        rollbackDuration,
        success: false,
        issues: [error instanceof Error ? error.message : String(error)]
      };

      this.log('error', `❌ Échec du rollback vers ${targetVersion}: ${error}`);
      this.emit('rollback_failed', { from: currentRelease.version, to: targetVersion, error });
      throw error;
    }
  }

  /**
   * Compare deux versions
   */
  async compareVersions(fromVersion: string, toVersion: string): Promise<ComparisonResult> {
    const fromRelease = this.findReleaseByVersion(fromVersion);
    const toRelease = this.findReleaseByVersion(toVersion);

    // Si les releases n'existent pas, créer une comparaison basique
    if (!fromRelease || !toRelease) {
      return {
        fromVersion,
        toVersion,
        changes: [{
          type: 'modified',
          component: 'system',
          description: `Mise à jour de ${fromVersion} vers ${toVersion}`,
          impact: 'medium'
        }],
        compatibility: {
          backward: true,
          forward: true,
          apiCompatible: true,
          dataCompatible: true,
          configCompatible: true,
          issues: []
        },
        breakingChanges: []
      };
    }

    const changes: VersionChange[] = [];
    const breakingChanges: BreakingChange[] = [];

    // Analyser les changements dans le changelog
    for (const entry of toRelease.changelog) {
      changes.push({
        type: this.mapChangelogTypeToVersionChange(entry.type),
        component: entry.component,
        description: entry.description,
        impact: entry.impact
      });

      if (entry.type === 'breaking') {
        breakingChanges.push({
          component: entry.component,
          description: entry.description,
          reason: 'Breaking change introduced',
          migration: 'See migration guide',
          removedIn: toVersion
        });
      }
    }

    const compatibility: CompatibilityInfo = {
      backward: breakingChanges.length === 0,
      forward: this.isForwardCompatible(fromVersion, toVersion),
      apiCompatible: !breakingChanges.some(bc => bc.component.includes('api')),
      dataCompatible: !breakingChanges.some(bc => bc.component.includes('data')),
      configCompatible: !breakingChanges.some(bc => bc.component.includes('config')),
      issues: breakingChanges.map(bc => bc.description)
    };

    return {
      fromVersion,
      toVersion,
      changes,
      compatibility,
      breakingChanges
    };
  }

  /**
   * Obtient la version courante
   */
  getCurrentVersion(): string {
    return this.formatVersion(this.currentVersion);
  }

  /**
   * Obtient la release courante
   */
  getCurrentRelease(): Release | null {
    const currentVersionString = this.getCurrentVersion();
    return this.findReleaseByVersion(currentVersionString);
  }

  /**
   * Obtient toutes les releases
   */
  getAllReleases(): Release[] {
    return Array.from(this.releases.values()).sort((a, b) =>
      b.createdAt.getTime() - a.createdAt.getTime()
    );
  }

  /**
   * Obtient une release par ID
   */
  getRelease(releaseId: string): Release | null {
    return this.releases.get(releaseId) || null;
  }

  /**
   * Trouve une release par version
   */
  findReleaseByVersion(version: string): Release | null {
    return Array.from(this.releases.values()).find(r => r.version === version) || null;
  }

  /**
   * Ajoute un artefact à une release
   */
  async addArtifact(releaseId: string, artifact: Omit<ReleaseArtifact, 'downloadCount' | 'createdAt'>): Promise<void> {
    const release = this.releases.get(releaseId);
    if (!release) {
      throw new Error(`Release non trouvée: ${releaseId}`);
    }

    const fullArtifact: ReleaseArtifact = {
      ...artifact,
      downloadCount: 0,
      createdAt: new Date()
    };

    release.artifacts.push(fullArtifact);
    this.emit('artifact_added', { release, artifact: fullArtifact });
  }

  /**
   * Calcule la prochaine version
   */
  private calculateNextVersion(type: 'major' | 'minor' | 'patch' | 'prerelease' | 'hotfix'): Version {
    const newVersion = { ...this.currentVersion };

    switch (type) {
      case 'major':
        newVersion.major++;
        newVersion.minor = 0;
        newVersion.patch = 0;
        newVersion.prerelease = undefined;
        break;
      case 'minor':
        newVersion.minor++;
        newVersion.patch = 0;
        newVersion.prerelease = undefined;
        break;
      case 'patch':
      case 'hotfix':
        newVersion.patch++;
        newVersion.prerelease = undefined;
        break;
      case 'prerelease':
        if (newVersion.prerelease) {
          // Incrémenter le numéro de prerelease
          const match = newVersion.prerelease.match(/(\w+)\.(\d+)/);
          if (match) {
            newVersion.prerelease = `${match[1]}.${parseInt(match[2]) + 1}`;
          } else {
            newVersion.prerelease = `${newVersion.prerelease}.1`;
          }
        } else {
          newVersion.minor++;
          newVersion.patch = 0;
          newVersion.prerelease = 'alpha.1';
        }
        break;
    }

    return newVersion;
  }

  /**
   * Parse une chaîne de version
   */
  private parseVersion(versionString: string): Version {
    const regex = /^(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9.-]+))?(?:\+([a-zA-Z0-9.-]+))?$/;
    const match = versionString.match(regex);

    if (!match) {
      throw new Error(`Format de version invalide: ${versionString}`);
    }

    return {
      major: parseInt(match[1]),
      minor: parseInt(match[2]),
      patch: parseInt(match[3]),
      prerelease: match[4],
      build: match[5]
    };
  }

  /**
   * Formate une version en chaîne
   */
  private formatVersion(version: Version): string {
    let versionString = `${version.major}.${version.minor}.${version.patch}`;

    if (version.prerelease) {
      versionString += `-${version.prerelease}`;
    }

    if (version.build) {
      versionString += `+${version.build}`;
    }

    return versionString;
  }

  private async validateRelease(release: Release): Promise<void> {
    // Validation de base
    if (!release.changelog || release.changelog.length === 0) {
      throw new Error('Le changelog est requis pour publier une release');
    }

    // Vérifier que la version n'existe pas déjà
    const existingRelease = this.findReleaseByVersion(release.version);
    if (existingRelease && existingRelease.id !== release.id) {
      throw new Error(`La version ${release.version} existe déjà`);
    }
  }

  private async performRollback(currentRelease: Release, targetRelease: Release): Promise<void> {
    // Implémentation du rollback
    // TODO: Intégrer avec le système de déploiement
    this.log('info', `Rollback de ${currentRelease.version} vers ${targetRelease.version}`);
  }

  private mapChangelogTypeToVersionChange(type: string): 'added' | 'modified' | 'removed' | 'deprecated' {
    switch (type) {
      case 'feature': return 'added';
      case 'bugfix':
      case 'improvement': return 'modified';
      case 'deprecated': return 'deprecated';
      default: return 'modified';
    }
  }

  private isForwardCompatible(fromVersion: string, toVersion: string): boolean {
    const from = this.parseVersion(fromVersion);
    const to = this.parseVersion(toVersion);

    // Forward compatible si version majeure identique
    return from.major === to.major;
  }

  private generateId(): string {
    return `release_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string): void {
    console.log(`[${new Date().toISOString()}] [${level.toUpperCase()}] ${message}`);
  }
}

export default VersionManager;
