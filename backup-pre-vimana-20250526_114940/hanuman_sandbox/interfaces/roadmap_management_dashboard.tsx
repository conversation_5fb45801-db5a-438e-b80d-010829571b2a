import React, { useState, useEffect, useCallback } from 'react';
import { RoadmapGenerator, RoadmapProject, ProjectAnalysisInput } from '../roadmap/roadmap_generator';
import { RoadmapValidatorAgent, RoadmapValidation } from '../roadmap/roadmap_validator_agent';
import { RoadmapTracker, RoadmapProgress } from '../roadmap/roadmap_tracker';

/**
 * Dashboard de Gestion des Roadmaps
 * Interface principale pour la création, validation et suivi des roadmaps
 */

interface RoadmapManagementDashboardProps {
  roadmapGenerator: RoadmapGenerator;
  roadmapValidator: RoadmapValidatorAgent;
  roadmapTracker: RoadmapTracker;
}

interface DashboardState {
  activeTab: 'create' | 'validate' | 'track' | 'overview';
  projects: RoadmapProject[];
  validations: RoadmapValidation[];
  progressData: RoadmapProgress[];
  selectedProject: RoadmapProject | null;
  selectedValidation: RoadmapValidation | null;
  isLoading: boolean;
  createProjectForm: ProjectAnalysisInput;
  deploymentBlocked: Map<string, string[]>;
}

export const RoadmapManagementDashboard: React.FC<RoadmapManagementDashboardProps> = ({
  roadmapGenerator,
  roadmapValidator,
  roadmapTracker
}) => {
  const [state, setState] = useState<DashboardState>({
    activeTab: 'overview',
    projects: [],
    validations: [],
    progressData: [],
    selectedProject: null,
    selectedValidation: null,
    isLoading: false,
    createProjectForm: {
      name: '',
      description: '',
      type: 'feature',
      scope: [],
      technicalStack: [],
      integrations: [],
      requirements: [],
      constraints: [],
      team: []
    },
    deploymentBlocked: new Map()
  });

  /**
   * Charge les données du dashboard
   */
  const loadDashboardData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const projects = roadmapGenerator.getProjects();
      const validations = roadmapValidator.getValidations();
      const progressData = roadmapTracker.getAllProgress();

      // Vérifier les blocages de déploiement
      const deploymentBlocked = new Map<string, string[]>();
      for (const project of projects) {
        const canDeployResult = roadmapValidator.canDeploy(project.id);
        if (!canDeployResult.canDeploy) {
          deploymentBlocked.set(project.id, canDeployResult.reasons);
        }
      }

      setState(prev => ({
        ...prev,
        projects,
        validations,
        progressData,
        deploymentBlocked,
        isLoading: false
      }));

    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [roadmapGenerator, roadmapValidator, roadmapTracker]);

  /**
   * Crée une nouvelle roadmap
   */
  const createRoadmap = async () => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const roadmap = await roadmapGenerator.generateRoadmap(state.createProjectForm);
      
      // Démarrer automatiquement la validation
      const validation = await roadmapValidator.validateRoadmap(roadmap, 'pre_development');
      
      // Démarrer le suivi si la validation est approuvée
      if (validation.status === 'approved') {
        await roadmapTracker.startTracking(roadmap);
      }

      // Réinitialiser le formulaire
      setState(prev => ({
        ...prev,
        createProjectForm: {
          name: '',
          description: '',
          type: 'feature',
          scope: [],
          technicalStack: [],
          integrations: [],
          requirements: [],
          constraints: [],
          team: []
        },
        selectedProject: roadmap,
        activeTab: 'validate',
        isLoading: false
      }));

      await loadDashboardData();

    } catch (error) {
      console.error('Erreur lors de la création de la roadmap:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Valide une roadmap
   */
  const validateRoadmap = async (projectId: string, validationType: any = 'pre_development') => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const project = state.projects.find(p => p.id === projectId);
      if (!project) {
        throw new Error('Projet non trouvé');
      }

      const validation = await roadmapValidator.validateRoadmap(project, validationType);
      
      setState(prev => ({
        ...prev,
        selectedValidation: validation,
        isLoading: false
      }));

      await loadDashboardData();

    } catch (error) {
      console.error('Erreur lors de la validation:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Approuve une validation
   */
  const approveValidation = async (validationId: string, stakeholder: string, comments?: string) => {
    try {
      await roadmapValidator.approveValidation(validationId, stakeholder, comments);
      await loadDashboardData();
    } catch (error) {
      console.error('Erreur lors de l\'approbation:', error);
    }
  };

  /**
   * Démarre le suivi d'un projet
   */
  const startProjectTracking = async (projectId: string) => {
    try {
      const project = state.projects.find(p => p.id === projectId);
      if (!project) {
        throw new Error('Projet non trouvé');
      }

      await roadmapTracker.startTracking(project);
      await loadDashboardData();
    } catch (error) {
      console.error('Erreur lors du démarrage du suivi:', error);
    }
  };

  /**
   * Vérifie si un déploiement peut être effectué
   */
  const checkDeploymentEligibility = (projectId: string): { canDeploy: boolean; reasons: string[] } => {
    const reasons = state.deploymentBlocked.get(projectId) || [];
    return {
      canDeploy: reasons.length === 0,
      reasons
    };
  };

  // Effet pour le chargement initial
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // Calculer les statistiques du dashboard
  const dashboardStats = {
    totalProjects: state.projects.length,
    activeProjects: state.progressData.filter(p => p.overallProgress < 100).length,
    completedProjects: state.progressData.filter(p => p.overallProgress === 100).length,
    blockedProjects: state.deploymentBlocked.size,
    pendingValidations: state.validations.filter(v => v.status === 'pending').length,
    approvedValidations: state.validations.filter(v => v.status === 'approved').length,
    averageProgress: state.progressData.length > 0 
      ? Math.round(state.progressData.reduce((sum, p) => sum + p.overallProgress, 0) / state.progressData.length)
      : 0
  };

  return (
    <div className="roadmap-management-dashboard">
      {/* En-tête du dashboard */}
      <div className="dashboard-header">
        <div className="header-content">
          <h1>🗺️ Gestion des Roadmaps</h1>
          <p>Création, validation et suivi systématique des évolutions</p>
        </div>
        
        <div className="header-actions">
          <button
            className="action-btn primary"
            onClick={() => setState(prev => ({ ...prev, activeTab: 'create' }))}
          >
            ➕ Nouvelle Roadmap
          </button>
          <button
            className="action-btn secondary"
            onClick={loadDashboardData}
            disabled={state.isLoading}
          >
            🔄 Actualiser
          </button>
        </div>
      </div>

      {/* Navigation par onglets */}
      <div className="dashboard-tabs">
        {[
          { id: 'overview', label: '📊 Vue d\'ensemble', count: dashboardStats.totalProjects },
          { id: 'create', label: '➕ Créer', count: null },
          { id: 'validate', label: '✅ Valider', count: dashboardStats.pendingValidations },
          { id: 'track', label: '📈 Suivre', count: dashboardStats.activeProjects }
        ].map(tab => (
          <button
            key={tab.id}
            className={`tab-btn ${state.activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setState(prev => ({ ...prev, activeTab: tab.id as any }))}
          >
            {tab.label}
            {tab.count !== null && tab.count > 0 && (
              <span className="tab-count">{tab.count}</span>
            )}
          </button>
        ))}
      </div>

      {/* Contenu des onglets */}
      <div className="dashboard-content">
        {state.activeTab === 'overview' && (
          <div className="overview-tab">
            {/* Statistiques principales */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon">🗺️</div>
                <div className="stat-content">
                  <div className="stat-value">{dashboardStats.totalProjects}</div>
                  <div className="stat-label">Projets Total</div>
                </div>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon">🚀</div>
                <div className="stat-content">
                  <div className="stat-value">{dashboardStats.activeProjects}</div>
                  <div className="stat-label">Projets Actifs</div>
                </div>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon">✅</div>
                <div className="stat-content">
                  <div className="stat-value">{dashboardStats.completedProjects}</div>
                  <div className="stat-label">Projets Terminés</div>
                </div>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon">🚫</div>
                <div className="stat-content">
                  <div className="stat-value">{dashboardStats.blockedProjects}</div>
                  <div className="stat-label">Projets Bloqués</div>
                </div>
              </div>
            </div>

            {/* Liste des projets */}
            <div className="projects-overview">
              <h3>📋 Projets Récents</h3>
              <div className="projects-list">
                {state.projects.slice(0, 10).map(project => {
                  const progress = state.progressData.find(p => p.roadmapId === project.id);
                  const deploymentStatus = checkDeploymentEligibility(project.id);
                  
                  return (
                    <div key={project.id} className={`project-item ${project.status}`}>
                      <div className="project-header">
                        <div className="project-name">{project.name}</div>
                        <div className="project-status">{project.status}</div>
                      </div>
                      <div className="project-details">
                        <div>Type: {project.type}</div>
                        <div>Priorité: {project.priority}</div>
                        <div>Sprints: {project.sprints.length}</div>
                        {progress && (
                          <div>Progrès: {Math.round(progress.overallProgress)}%</div>
                        )}
                      </div>
                      <div className="project-actions">
                        <button
                          className="project-btn"
                          onClick={() => validateRoadmap(project.id)}
                          disabled={state.isLoading}
                        >
                          ✅ Valider
                        </button>
                        {!progress && (
                          <button
                            className="project-btn"
                            onClick={() => startProjectTracking(project.id)}
                            disabled={state.isLoading}
                          >
                            📈 Suivre
                          </button>
                        )}
                        <div className={`deployment-status ${deploymentStatus.canDeploy ? 'ready' : 'blocked'}`}>
                          {deploymentStatus.canDeploy ? '🚀 Prêt' : '🚫 Bloqué'}
                        </div>
                      </div>
                      {!deploymentStatus.canDeploy && (
                        <div className="deployment-blockers">
                          <strong>Blockers:</strong>
                          <ul>
                            {deploymentStatus.reasons.map((reason, index) => (
                              <li key={index}>{reason}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {state.activeTab === 'create' && (
          <div className="create-tab">
            <h3>➕ Créer une Nouvelle Roadmap</h3>
            <div className="create-form">
              <div className="form-section">
                <h4>Informations Générales</h4>
                <div className="form-group">
                  <label>Nom du Projet</label>
                  <input
                    type="text"
                    value={state.createProjectForm.name}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      createProjectForm: { ...prev.createProjectForm, name: e.target.value }
                    }))}
                    placeholder="Nom du projet"
                  />
                </div>
                <div className="form-group">
                  <label>Description</label>
                  <textarea
                    value={state.createProjectForm.description}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      createProjectForm: { ...prev.createProjectForm, description: e.target.value }
                    }))}
                    placeholder="Description détaillée du projet"
                    rows={4}
                  />
                </div>
                <div className="form-group">
                  <label>Type de Projet</label>
                  <select
                    value={state.createProjectForm.type}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      createProjectForm: { ...prev.createProjectForm, type: e.target.value }
                    }))}
                  >
                    <option value="feature">Nouvelle Fonctionnalité</option>
                    <option value="enhancement">Amélioration</option>
                    <option value="bugfix">Correction de Bug</option>
                    <option value="refactor">Refactoring</option>
                    <option value="security">Sécurité</option>
                    <option value="performance">Performance</option>
                  </select>
                </div>
              </div>

              <div className="form-section">
                <h4>Exigences Techniques</h4>
                <div className="form-group">
                  <label>Exigences</label>
                  <textarea
                    value={state.createProjectForm.requirements.join('\n')}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      createProjectForm: { 
                        ...prev.createProjectForm, 
                        requirements: e.target.value.split('\n').filter(r => r.trim()) 
                      }
                    }))}
                    placeholder="Une exigence par ligne"
                    rows={4}
                  />
                </div>
                <div className="form-group">
                  <label>Stack Technique</label>
                  <textarea
                    value={state.createProjectForm.technicalStack.join('\n')}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      createProjectForm: { 
                        ...prev.createProjectForm, 
                        technicalStack: e.target.value.split('\n').filter(t => t.trim()) 
                      }
                    }))}
                    placeholder="Une technologie par ligne"
                    rows={3}
                  />
                </div>
                <div className="form-group">
                  <label>Intégrations</label>
                  <textarea
                    value={state.createProjectForm.integrations.join('\n')}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      createProjectForm: { 
                        ...prev.createProjectForm, 
                        integrations: e.target.value.split('\n').filter(i => i.trim()) 
                      }
                    }))}
                    placeholder="Une intégration par ligne"
                    rows={3}
                  />
                </div>
              </div>

              <div className="form-section">
                <h4>Équipe et Contraintes</h4>
                <div className="form-group">
                  <label>Équipe</label>
                  <textarea
                    value={state.createProjectForm.team.join('\n')}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      createProjectForm: { 
                        ...prev.createProjectForm, 
                        team: e.target.value.split('\n').filter(t => t.trim()) 
                      }
                    }))}
                    placeholder="Un membre par ligne"
                    rows={3}
                  />
                </div>
                <div className="form-group">
                  <label>Contraintes</label>
                  <textarea
                    value={state.createProjectForm.constraints.join('\n')}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      createProjectForm: { 
                        ...prev.createProjectForm, 
                        constraints: e.target.value.split('\n').filter(c => c.trim()) 
                      }
                    }))}
                    placeholder="Une contrainte par ligne"
                    rows={3}
                  />
                </div>
              </div>

              <div className="form-actions">
                <button
                  className="action-btn primary"
                  onClick={createRoadmap}
                  disabled={state.isLoading || !state.createProjectForm.name || !state.createProjectForm.description}
                >
                  🗺️ Générer la Roadmap
                </button>
              </div>
            </div>
          </div>
        )}

        {state.activeTab === 'validate' && (
          <div className="validate-tab">
            <h3>✅ Validation des Roadmaps</h3>
            <div className="validations-list">
              {state.validations.map(validation => {
                const project = state.projects.find(p => p.id === validation.roadmapId);
                const failedResults = validation.results.filter(r => r.status === 'failed');
                const warningResults = validation.results.filter(r => r.status === 'warning');
                
                return (
                  <div key={validation.id} className={`validation-item ${validation.status}`}>
                    <div className="validation-header">
                      <div className="validation-name">
                        {project?.name || 'Projet Inconnu'} - {validation.validationType}
                      </div>
                      <div className="validation-status">{validation.status}</div>
                    </div>
                    <div className="validation-details">
                      <div>Règles: {validation.validationRules.length}</div>
                      <div>Erreurs: {failedResults.length}</div>
                      <div>Avertissements: {warningResults.length}</div>
                      <div>Blockers: {validation.blockers.filter(b => !b.resolved).length}</div>
                    </div>
                    {validation.blockers.filter(b => !b.resolved).length > 0 && (
                      <div className="validation-blockers">
                        <strong>Blockers actifs:</strong>
                        <ul>
                          {validation.blockers.filter(b => !b.resolved).map(blocker => (
                            <li key={blocker.id}>{blocker.description}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {validation.stakeholderApprovals.filter(a => a.required && a.status === 'pending').length > 0 && (
                      <div className="pending-approvals">
                        <strong>Approbations en attente:</strong>
                        <ul>
                          {validation.stakeholderApprovals.filter(a => a.required && a.status === 'pending').map(approval => (
                            <li key={approval.stakeholder}>
                              {approval.stakeholder} ({approval.role})
                              <button
                                className="approve-btn"
                                onClick={() => approveValidation(validation.id, approval.stakeholder)}
                              >
                                ✅ Approuver
                              </button>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {state.activeTab === 'track' && (
          <div className="track-tab">
            <h3>📈 Suivi des Projets</h3>
            <div className="progress-list">
              {state.progressData.map(progress => {
                const project = state.projects.find(p => p.id === progress.roadmapId);
                const currentSprint = progress.sprintProgress.find(sp => sp.status === 'in_progress');
                
                return (
                  <div key={progress.roadmapId} className="progress-item">
                    <div className="progress-header">
                      <div className="progress-name">{project?.name || 'Projet Inconnu'}</div>
                      <div className="progress-percentage">{Math.round(progress.overallProgress)}%</div>
                    </div>
                    <div className="progress-bar">
                      <div 
                        className="progress-fill" 
                        style={{ width: `${progress.overallProgress}%` }}
                      />
                    </div>
                    <div className="progress-details">
                      <div>Sprint Actuel: {progress.currentSprint}/{progress.sprintProgress.length}</div>
                      {currentSprint && (
                        <div>Progrès Sprint: {Math.round(currentSprint.progress)}%</div>
                      )}
                      <div>Alertes: {progress.alerts.filter(a => !a.resolved).length}</div>
                      <div>Dernière MAJ: {progress.lastUpdated.toLocaleString()}</div>
                    </div>
                    {progress.alerts.filter(a => !a.resolved && a.severity === 'critical').length > 0 && (
                      <div className="critical-alerts">
                        <strong>🚨 Alertes Critiques:</strong>
                        <ul>
                          {progress.alerts.filter(a => !a.resolved && a.severity === 'critical').map(alert => (
                            <li key={alert.id}>{alert.title}: {alert.description}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Indicateur de chargement */}
      {state.isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner">
            <div className="spinner"></div>
            <div>Traitement en cours...</div>
          </div>
        </div>
      )}
    </div>
  );
};
