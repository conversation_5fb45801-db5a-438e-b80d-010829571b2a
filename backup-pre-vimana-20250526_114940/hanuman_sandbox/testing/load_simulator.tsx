import React, { useState, useEffect, useCallback } from 'react';
import { PerformanceTesting, PerformanceTest, PerformanceTestResult } from './performance_testing';
import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';

/**
 * Simulateur de Charge pour la Sandbox Hanuman
 * Interface pour configurer et exécuter des tests de charge et de stress
 */

export interface LoadPattern {
  id: string;
  name: string;
  description: string;
  type: 'constant' | 'ramp' | 'spike' | 'wave' | 'step';
  parameters: {
    baseLoad: number;
    peakLoad?: number;
    duration: number;
    rampTime?: number;
    spikeDuration?: number;
    waveFrequency?: number;
    stepSize?: number;
    stepDuration?: number;
  };
}

export interface UserBehavior {
  id: string;
  name: string;
  description: string;
  actions: {
    name: string;
    weight: number; // percentage
    thinkTime: { min: number; max: number }; // ms
    parameters: any;
  }[];
  sessionDuration: { min: number; max: number }; // minutes
}

export interface LoadScenario {
  id: string;
  name: string;
  description: string;
  pattern: LoadPattern;
  userBehaviors: UserBehavior[];
  target: {
    endpoint?: string;
    component?: string;
    service?: string;
  };
  environment: string;
  dataSet?: {
    name: string;
    size: number;
    type: 'synthetic' | 'production' | 'custom';
  };
}

interface LoadSimulatorProps {
  performanceTesting: PerformanceTesting;
  infrastructure: SandboxInfrastructure;
  onTestStarted?: (test: PerformanceTest) => void;
  onTestCompleted?: (result: PerformanceTestResult) => void;
  onError?: (error: Error) => void;
}

export const LoadSimulator: React.FC<LoadSimulatorProps> = ({
  performanceTesting,
  infrastructure,
  onTestStarted,
  onTestCompleted,
  onError
}) => {
  const [activeTab, setActiveTab] = useState<'scenarios' | 'patterns' | 'behaviors' | 'execution'>('scenarios');
  const [scenarios, setScenarios] = useState<LoadScenario[]>([]);
  const [patterns, setPatterns] = useState<LoadPattern[]>([]);
  const [behaviors, setBehaviors] = useState<UserBehavior[]>([]);
  const [selectedScenario, setSelectedScenario] = useState<LoadScenario | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResults, setExecutionResults] = useState<PerformanceTestResult[]>([]);
  const [realTimeMetrics, setRealTimeMetrics] = useState<any>(null);

  // Patterns de charge prédéfinis
  const defaultPatterns: LoadPattern[] = [
    {
      id: 'constant-load',
      name: 'Charge Constante',
      description: 'Charge stable pendant toute la durée du test',
      type: 'constant',
      parameters: {
        baseLoad: 50,
        duration: 300 // 5 minutes
      }
    },
    {
      id: 'ramp-up',
      name: 'Montée Progressive',
      description: 'Augmentation progressive de la charge',
      type: 'ramp',
      parameters: {
        baseLoad: 10,
        peakLoad: 100,
        duration: 600,
        rampTime: 300
      }
    },
    {
      id: 'spike-test',
      name: 'Test de Pic',
      description: 'Pic soudain de charge pour tester la résilience',
      type: 'spike',
      parameters: {
        baseLoad: 20,
        peakLoad: 200,
        duration: 300,
        spikeDuration: 60
      }
    },
    {
      id: 'wave-pattern',
      name: 'Charge en Vagues',
      description: 'Charge oscillante simulant des pics périodiques',
      type: 'wave',
      parameters: {
        baseLoad: 30,
        peakLoad: 80,
        duration: 900,
        waveFrequency: 120 // 2 minutes par cycle
      }
    },
    {
      id: 'step-load',
      name: 'Charge par Paliers',
      description: 'Augmentation de charge par étapes',
      type: 'step',
      parameters: {
        baseLoad: 10,
        peakLoad: 100,
        duration: 600,
        stepSize: 20,
        stepDuration: 120
      }
    }
  ];

  // Comportements utilisateur prédéfinis
  const defaultBehaviors: UserBehavior[] = [
    {
      id: 'web-browsing',
      name: 'Navigation Web',
      description: 'Comportement typique de navigation web',
      actions: [
        { name: 'page_load', weight: 40, thinkTime: { min: 2000, max: 5000 }, parameters: {} },
        { name: 'click_link', weight: 30, thinkTime: { min: 1000, max: 3000 }, parameters: {} },
        { name: 'form_submit', weight: 20, thinkTime: { min: 5000, max: 10000 }, parameters: {} },
        { name: 'search', weight: 10, thinkTime: { min: 3000, max: 7000 }, parameters: {} }
      ],
      sessionDuration: { min: 5, max: 30 }
    },
    {
      id: 'api-consumer',
      name: 'Consommateur API',
      description: 'Utilisation intensive d\'API',
      actions: [
        { name: 'get_request', weight: 50, thinkTime: { min: 500, max: 2000 }, parameters: {} },
        { name: 'post_request', weight: 25, thinkTime: { min: 1000, max: 3000 }, parameters: {} },
        { name: 'put_request', weight: 15, thinkTime: { min: 1000, max: 3000 }, parameters: {} },
        { name: 'delete_request', weight: 10, thinkTime: { min: 2000, max: 5000 }, parameters: {} }
      ],
      sessionDuration: { min: 2, max: 15 }
    },
    {
      id: 'heavy-computation',
      name: 'Calcul Intensif',
      description: 'Opérations nécessitant beaucoup de ressources',
      actions: [
        { name: 'complex_query', weight: 40, thinkTime: { min: 5000, max: 15000 }, parameters: {} },
        { name: 'data_processing', weight: 35, thinkTime: { min: 10000, max: 30000 }, parameters: {} },
        { name: 'report_generation', weight: 25, thinkTime: { min: 15000, max: 45000 }, parameters: {} }
      ],
      sessionDuration: { min: 10, max: 60 }
    }
  ];

  useEffect(() => {
    setPatterns(defaultPatterns);
    setBehaviors(defaultBehaviors);
    
    // Créer des scénarios par défaut
    const defaultScenarios: LoadScenario[] = [
      {
        id: 'web-load-test',
        name: 'Test de Charge Web',
        description: 'Test de charge pour application web',
        pattern: defaultPatterns[0],
        userBehaviors: [defaultBehaviors[0]],
        target: { endpoint: '/api/web' },
        environment: 'test-performance'
      },
      {
        id: 'api-stress-test',
        name: 'Test de Stress API',
        description: 'Test de stress pour API REST',
        pattern: defaultPatterns[2],
        userBehaviors: [defaultBehaviors[1]],
        target: { endpoint: '/api/v1' },
        environment: 'test-performance'
      }
    ];
    
    setScenarios(defaultScenarios);
  }, []);

  /**
   * Exécute un scénario de charge
   */
  const executeScenario = useCallback(async (scenario: LoadScenario) => {
    if (!scenario) return;

    setIsExecuting(true);
    
    try {
      // Convertir le scénario en test de performance
      const performanceTest: PerformanceTest = {
        id: `load_test_${Date.now()}`,
        name: scenario.name,
        description: scenario.description,
        type: 'load',
        configuration: {
          duration: scenario.pattern.parameters.duration,
          rampUpTime: scenario.pattern.parameters.rampTime || 0,
          rampDownTime: scenario.pattern.parameters.rampTime || 0,
          virtualUsers: scenario.pattern.parameters.peakLoad || scenario.pattern.parameters.baseLoad,
          requestsPerSecond: scenario.pattern.parameters.baseLoad
        },
        target: scenario.target,
        thresholds: {
          maxResponseTime: 2000,
          minThroughput: scenario.pattern.parameters.baseLoad * 0.8,
          maxErrorRate: 5,
          maxCpuUsage: 80,
          maxMemoryUsage: 80
        },
        scenarios: []
      };

      onTestStarted?.(performanceTest);

      // Démarrer le monitoring en temps réel
      const monitoringInterval = setInterval(() => {
        setRealTimeMetrics({
          timestamp: new Date(),
          activeUsers: Math.floor(Math.random() * performanceTest.configuration.virtualUsers),
          responseTime: Math.random() * 1000 + 200,
          throughput: Math.random() * 100 + 50,
          errorRate: Math.random() * 5,
          cpuUsage: Math.random() * 80 + 10,
          memoryUsage: Math.random() * 70 + 20
        });
      }, 1000);

      // Exécuter le test
      const result = await performanceTesting.executePerformanceTest(performanceTest);
      
      clearInterval(monitoringInterval);
      setRealTimeMetrics(null);
      
      setExecutionResults(prev => [result, ...prev]);
      onTestCompleted?.(result);
      
      console.log(`✅ Scénario de charge terminé: ${scenario.name}`);
      
    } catch (error) {
      console.error('Erreur lors de l\'exécution du scénario:', error);
      onError?.(error as Error);
    } finally {
      setIsExecuting(false);
    }
  }, [performanceTesting, onTestStarted, onTestCompleted, onError]);

  /**
   * Crée un nouveau pattern de charge
   */
  const createPattern = useCallback((pattern: Omit<LoadPattern, 'id'>) => {
    const newPattern: LoadPattern = {
      ...pattern,
      id: `pattern_${Date.now()}`
    };
    setPatterns(prev => [...prev, newPattern]);
  }, []);

  /**
   * Crée un nouveau comportement utilisateur
   */
  const createBehavior = useCallback((behavior: Omit<UserBehavior, 'id'>) => {
    const newBehavior: UserBehavior = {
      ...behavior,
      id: `behavior_${Date.now()}`
    };
    setBehaviors(prev => [...prev, newBehavior]);
  }, []);

  /**
   * Crée un nouveau scénario
   */
  const createScenario = useCallback((scenario: Omit<LoadScenario, 'id'>) => {
    const newScenario: LoadScenario = {
      ...scenario,
      id: `scenario_${Date.now()}`
    };
    setScenarios(prev => [...prev, newScenario]);
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* En-tête */}
      <div className="border-b border-gray-200 px-6 py-4">
        <h2 className="text-xl font-semibold text-gray-900">⚡ Simulateur de Charge</h2>
        <p className="text-sm text-gray-600 mt-1">
          Configuration et exécution de tests de charge et de stress
        </p>
      </div>

      {/* Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'scenarios', label: '🎯 Scénarios', icon: '🎯' },
            { id: 'patterns', label: '📈 Patterns', icon: '📈' },
            { id: 'behaviors', label: '👤 Comportements', icon: '👤' },
            { id: 'execution', label: '🚀 Exécution', icon: '🚀' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon} {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Contenu */}
      <div className="p-6">
        {activeTab === 'scenarios' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Scénarios de Charge</h3>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                + Nouveau scénario
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {scenarios.map((scenario) => (
                <div key={scenario.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <h4 className="font-medium text-gray-900 mb-2">{scenario.name}</h4>
                  <p className="text-sm text-gray-600 mb-3">{scenario.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-500">Pattern:</span>
                      <span className="font-medium">{scenario.pattern.name}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-500">Durée:</span>
                      <span className="font-medium">{scenario.pattern.parameters.duration}s</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-500">Charge max:</span>
                      <span className="font-medium">
                        {scenario.pattern.parameters.peakLoad || scenario.pattern.parameters.baseLoad} utilisateurs
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedScenario(scenario)}
                      className="flex-1 px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200"
                    >
                      Sélectionner
                    </button>
                    <button
                      onClick={() => executeScenario(scenario)}
                      disabled={isExecuting}
                      className="flex-1 px-3 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200 disabled:opacity-50"
                    >
                      Exécuter
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'patterns' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Patterns de Charge</h3>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                + Nouveau pattern
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {patterns.map((pattern) => (
                <div key={pattern.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900">{pattern.name}</h4>
                      <p className="text-sm text-gray-600">{pattern.description}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      pattern.type === 'constant' ? 'bg-blue-100 text-blue-800' :
                      pattern.type === 'ramp' ? 'bg-green-100 text-green-800' :
                      pattern.type === 'spike' ? 'bg-red-100 text-red-800' :
                      pattern.type === 'wave' ? 'bg-purple-100 text-purple-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {pattern.type}
                    </span>
                  </div>
                  
                  <div className="space-y-1 text-xs text-gray-600">
                    <div className="flex justify-between">
                      <span>Charge de base:</span>
                      <span>{pattern.parameters.baseLoad}</span>
                    </div>
                    {pattern.parameters.peakLoad && (
                      <div className="flex justify-between">
                        <span>Charge max:</span>
                        <span>{pattern.parameters.peakLoad}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span>Durée:</span>
                      <span>{pattern.parameters.duration}s</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'behaviors' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Comportements Utilisateur</h3>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                + Nouveau comportement
              </button>
            </div>
            
            <div className="space-y-4">
              {behaviors.map((behavior) => (
                <div key={behavior.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900">{behavior.name}</h4>
                      <p className="text-sm text-gray-600">{behavior.description}</p>
                    </div>
                    <span className="text-xs text-gray-500">
                      Session: {behavior.sessionDuration.min}-{behavior.sessionDuration.max}min
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {behavior.actions.map((action, index) => (
                      <div key={index} className="bg-gray-50 rounded p-2">
                        <div className="text-xs font-medium text-gray-900">{action.name}</div>
                        <div className="text-xs text-gray-600">{action.weight}%</div>
                        <div className="text-xs text-gray-500">
                          {action.thinkTime.min}-{action.thinkTime.max}ms
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'execution' && (
          <div className="space-y-6">
            {/* Monitoring en temps réel */}
            {isExecuting && realTimeMetrics && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-blue-900 mb-4">📊 Monitoring en Temps Réel</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{realTimeMetrics.activeUsers}</div>
                    <div className="text-xs text-blue-800">Utilisateurs actifs</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{Math.round(realTimeMetrics.responseTime)}ms</div>
                    <div className="text-xs text-green-800">Temps de réponse</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{Math.round(realTimeMetrics.throughput)}</div>
                    <div className="text-xs text-purple-800">Req/sec</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{realTimeMetrics.errorRate.toFixed(1)}%</div>
                    <div className="text-xs text-red-800">Taux d'erreur</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{Math.round(realTimeMetrics.cpuUsage)}%</div>
                    <div className="text-xs text-yellow-800">CPU</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-indigo-600">{Math.round(realTimeMetrics.memoryUsage)}%</div>
                    <div className="text-xs text-indigo-800">Mémoire</div>
                  </div>
                </div>
              </div>
            )}

            {/* Contrôles d'exécution */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">🎮 Contrôles d'Exécution</h3>
              
              {selectedScenario ? (
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Scénario sélectionné: {selectedScenario.name}</h4>
                    <p className="text-sm text-gray-600 mb-3">{selectedScenario.description}</p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Pattern:</span>
                        <div className="font-medium">{selectedScenario.pattern.name}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Durée:</span>
                        <div className="font-medium">{selectedScenario.pattern.parameters.duration}s</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Utilisateurs:</span>
                        <div className="font-medium">
                          {selectedScenario.pattern.parameters.peakLoad || selectedScenario.pattern.parameters.baseLoad}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500">Environnement:</span>
                        <div className="font-medium">{selectedScenario.environment}</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-4">
                    <button
                      onClick={() => executeScenario(selectedScenario)}
                      disabled={isExecuting}
                      className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {isExecuting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span>Exécution en cours...</span>
                        </>
                      ) : (
                        <>
                          <span>🚀</span>
                          <span>Démarrer le test</span>
                        </>
                      )}
                    </button>
                    
                    {isExecuting && (
                      <button
                        onClick={() => setIsExecuting(false)}
                        className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700"
                      >
                        🛑 Arrêter
                      </button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p className="text-lg">Aucun scénario sélectionné</p>
                  <p className="text-sm">Sélectionnez un scénario dans l'onglet Scénarios pour commencer</p>
                </div>
              )}
            </div>

            {/* Historique des résultats */}
            {executionResults.length > 0 && (
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">📈 Historique des Résultats</h3>
                
                <div className="space-y-3">
                  {executionResults.slice(0, 5).map((result, index) => (
                    <div key={result.testId} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <div>
                        <div className="font-medium text-gray-900">{result.testId}</div>
                        <div className="text-sm text-gray-600">
                          {result.startTime.toLocaleString()} - Durée: {Math.round(result.duration / 1000)}s
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          result.status === 'passed' ? 'bg-green-100 text-green-800' :
                          result.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {result.status}
                        </span>
                        <div className="text-sm text-gray-600">
                          {Math.round(result.metrics.responseTime.avg)}ms avg
                        </div>
                        <div className="text-sm text-gray-600">
                          {Math.round(result.metrics.throughput.requestsPerSecond)} req/s
                        </div>
                        <button className="text-blue-600 hover:text-blue-800 text-sm">
                          Détails
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadSimulator;
