import React, { useState, useEffect, useCallback } from 'react';
import { AutomatedTestingFramework, TestExecution, TestResult } from './automated_testing_framework';
import { PerformanceTestingSystem, PerformanceTestResult } from './performance_testing';
import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';

/**
 * Système de Métriques de Qualité pour la Sandbox Hanuman
 * Collecte, analyse et présente les métriques de qualité du code et des tests
 */

export interface QualityMetrics {
  timestamp: Date;
  version: string;
  testMetrics: {
    coverage: CoverageMetrics;
    performance: PerformanceMetrics;
    reliability: ReliabilityMetrics;
    security: SecurityMetrics;
  };
  codeMetrics: {
    complexity: ComplexityMetrics;
    maintainability: MaintainabilityMetrics;
    duplication: DuplicationMetrics;
    documentation: DocumentationMetrics;
  };
  trends: {
    testCoverage: TrendData[];
    performance: TrendData[];
    bugs: TrendData[];
    security: TrendData[];
  };
  recommendations: QualityRecommendation[];
  score: QualityScore;
}

export interface CoverageMetrics {
  statements: number;
  branches: number;
  functions: number;
  lines: number;
  overall: number;
  threshold: number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
}

export interface PerformanceMetrics {
  responseTime: {
    avg: number;
    p95: number;
    p99: number;
    threshold: number;
  };
  throughput: {
    current: number;
    target: number;
    unit: string;
  };
  resourceUsage: {
    cpu: number;
    memory: number;
    storage: number;
  };
  score: number;
}

export interface ReliabilityMetrics {
  testPassRate: number;
  flakyTests: number;
  testStability: number;
  errorRate: number;
  mtbf: number; // Mean Time Between Failures
  mttr: number; // Mean Time To Recovery
}

export interface SecurityMetrics {
  vulnerabilities: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  riskScore: number;
  complianceScore: number;
  lastScanDate: Date;
}

export interface ComplexityMetrics {
  cyclomaticComplexity: number;
  cognitiveComplexity: number;
  linesOfCode: number;
  technicalDebt: number;
  maintainabilityIndex: number;
}

export interface MaintainabilityMetrics {
  codeSmells: number;
  duplicatedLines: number;
  coupling: number;
  cohesion: number;
  testability: number;
}

export interface DuplicationMetrics {
  duplicatedBlocks: number;
  duplicatedLines: number;
  duplicatedFiles: number;
  duplicationRatio: number;
}

export interface DocumentationMetrics {
  coverage: number;
  quality: number;
  outdated: number;
  missing: number;
}

export interface TrendData {
  timestamp: Date;
  value: number;
  target?: number;
}

export interface QualityRecommendation {
  id: string;
  type: 'performance' | 'security' | 'maintainability' | 'testing' | 'documentation';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
  actions: string[];
  estimatedTime: number; // hours
}

export interface QualityScore {
  overall: number;
  categories: {
    testing: number;
    performance: number;
    security: number;
    maintainability: number;
    documentation: number;
  };
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  trend: 'improving' | 'stable' | 'declining';
}

export interface QualityReport {
  id: string;
  timestamp: Date;
  metrics: QualityMetrics;
  summary: string;
  alerts: QualityAlert[];
  exportFormats: ('json' | 'html' | 'pdf' | 'csv')[];
}

export interface QualityAlert {
  id: string;
  type: 'threshold' | 'regression' | 'anomaly';
  severity: 'info' | 'warning' | 'error' | 'critical';
  metric: string;
  message: string;
  value: number;
  threshold: number;
  timestamp: Date;
}

export class QualityMetricsSystem {
  private testingFramework: AutomatedTestingFramework;
  private performanceSystem: PerformanceTestingSystem;
  private infrastructure: SandboxInfrastructure;
  private metricsHistory: Map<string, QualityMetrics[]> = new Map();
  private alerts: QualityAlert[] = [];
  private thresholds: Map<string, number> = new Map();

  constructor(
    testingFramework: AutomatedTestingFramework,
    performanceSystem: PerformanceTestingSystem,
    infrastructure: SandboxInfrastructure
  ) {
    this.testingFramework = testingFramework;
    this.performanceSystem = performanceSystem;
    this.infrastructure = infrastructure;
    this.initializeThresholds();
  }

  /**
   * Initialise les seuils de qualité
   */
  private initializeThresholds(): void {
    this.thresholds.set('coverage.overall', 80);
    this.thresholds.set('performance.responseTime', 200);
    this.thresholds.set('security.riskScore', 20);
    this.thresholds.set('maintainability.index', 70);
    this.thresholds.set('reliability.passRate', 95);
    this.thresholds.set('complexity.cyclomatic', 10);
    this.thresholds.set('duplication.ratio', 5);
    this.thresholds.set('documentation.coverage', 80);
  }

  /**
   * Collecte toutes les métriques de qualité
   */
  async collectMetrics(projectId: string): Promise<QualityMetrics> {
    console.log(`📊 Collecte des métriques de qualité pour: ${projectId}`);

    const metrics: QualityMetrics = {
      timestamp: new Date(),
      version: await this.getProjectVersion(projectId),
      testMetrics: await this.collectTestMetrics(projectId),
      codeMetrics: await this.collectCodeMetrics(projectId),
      trends: await this.collectTrends(projectId),
      recommendations: await this.generateRecommendations(projectId),
      score: { overall: 0, categories: { testing: 0, performance: 0, security: 0, maintainability: 0, documentation: 0 }, grade: 'F', trend: 'stable' }
    };

    // Calculer le score global
    metrics.score = this.calculateQualityScore(metrics);

    // Sauvegarder dans l'historique
    this.saveMetricsToHistory(projectId, metrics);

    // Vérifier les alertes
    await this.checkAlerts(metrics);

    return metrics;
  }

  /**
   * Collecte les métriques de tests
   */
  private async collectTestMetrics(projectId: string): Promise<QualityMetrics['testMetrics']> {
    // Récupérer les dernières exécutions de tests
    const executions = await this.testingFramework.getRecentExecutions(projectId, 10);
    const performanceResults = await this.performanceSystem.getRecentResults(projectId, 5);

    return {
      coverage: await this.calculateCoverageMetrics(executions),
      performance: await this.calculatePerformanceMetrics(performanceResults),
      reliability: await this.calculateReliabilityMetrics(executions),
      security: await this.calculateSecurityMetrics(executions)
    };
  }

  /**
   * Collecte les métriques de code
   */
  private async collectCodeMetrics(projectId: string): Promise<QualityMetrics['codeMetrics']> {
    return {
      complexity: await this.analyzeComplexity(projectId),
      maintainability: await this.analyzeMaintainability(projectId),
      duplication: await this.analyzeDuplication(projectId),
      documentation: await this.analyzeDocumentation(projectId)
    };
  }

  /**
   * Calcule les métriques de couverture
   */
  private async calculateCoverageMetrics(executions: TestExecution[]): Promise<CoverageMetrics> {
    if (executions.length === 0) {
      return {
        statements: 0, branches: 0, functions: 0, lines: 0, overall: 0,
        threshold: this.thresholds.get('coverage.overall') || 80,
        status: 'critical'
      };
    }

    const latestExecution = executions[0];
    const coverage = latestExecution.summary.coverage;
    const threshold = this.thresholds.get('coverage.overall') || 80;

    let status: CoverageMetrics['status'] = 'critical';
    if (coverage >= threshold) status = 'excellent';
    else if (coverage >= threshold * 0.8) status = 'good';
    else if (coverage >= threshold * 0.6) status = 'warning';

    return {
      statements: coverage,
      branches: coverage * 0.9, // Simulation
      functions: coverage * 0.95,
      lines: coverage * 0.92,
      overall: coverage,
      threshold,
      status
    };
  }

  /**
   * Calcule le score de qualité global
   */
  private calculateQualityScore(metrics: QualityMetrics): QualityScore {
    const weights = {
      testing: 0.3,
      performance: 0.25,
      security: 0.2,
      maintainability: 0.15,
      documentation: 0.1
    };

    const scores = {
      testing: this.calculateTestingScore(metrics.testMetrics),
      performance: metrics.testMetrics.performance.score,
      security: Math.max(0, 100 - metrics.testMetrics.security.riskScore),
      maintainability: metrics.codeMetrics.maintainability.testability,
      documentation: metrics.codeMetrics.documentation.coverage
    };

    const overall = Object.entries(scores).reduce(
      (sum, [category, score]) => sum + score * weights[category as keyof typeof weights],
      0
    );

    let grade: QualityScore['grade'] = 'F';
    if (overall >= 90) grade = 'A';
    else if (overall >= 80) grade = 'B';
    else if (overall >= 70) grade = 'C';
    else if (overall >= 60) grade = 'D';

    return {
      overall: Math.round(overall),
      categories: scores,
      grade,
      trend: this.calculateTrend(metrics.trends)
    };
  }

  /**
   * Calcule le score de testing
   */
  private calculateTestingScore(testMetrics: QualityMetrics['testMetrics']): number {
    const coverageScore = testMetrics.coverage.overall;
    const reliabilityScore = testMetrics.reliability.testPassRate;
    const performanceScore = testMetrics.performance.score;

    return Math.round((coverageScore * 0.4 + reliabilityScore * 0.4 + performanceScore * 0.2));
  }

  /**
   * Calcule la tendance
   */
  private calculateTrend(trends: QualityMetrics['trends']): QualityScore['trend'] {
    // Analyser les tendances des 30 derniers jours
    const recentTrends = trends.testCoverage.slice(-30);
    if (recentTrends.length < 2) return 'stable';

    const first = recentTrends[0].value;
    const last = recentTrends[recentTrends.length - 1].value;
    const change = ((last - first) / first) * 100;

    if (change > 5) return 'improving';
    if (change < -5) return 'declining';
    return 'stable';
  }

  /**
   * Analyse la complexité du code
   */
  private async analyzeComplexity(projectId: string): Promise<ComplexityMetrics> {
    // Simulation d'analyse de complexité
    return {
      cyclomaticComplexity: Math.floor(Math.random() * 15) + 5,
      cognitiveComplexity: Math.floor(Math.random() * 20) + 10,
      linesOfCode: Math.floor(Math.random() * 10000) + 5000,
      technicalDebt: Math.floor(Math.random() * 50) + 10,
      maintainabilityIndex: Math.floor(Math.random() * 40) + 60
    };
  }

  /**
   * Analyse la maintenabilité
   */
  private async analyzeMaintainability(projectId: string): Promise<MaintainabilityMetrics> {
    return {
      codeSmells: Math.floor(Math.random() * 20) + 5,
      duplicatedLines: Math.floor(Math.random() * 500) + 100,
      coupling: Math.floor(Math.random() * 30) + 10,
      cohesion: Math.floor(Math.random() * 30) + 70,
      testability: Math.floor(Math.random() * 20) + 80
    };
  }

  /**
   * Analyse la duplication
   */
  private async analyzeDuplication(projectId: string): Promise<DuplicationMetrics> {
    const duplicatedLines = Math.floor(Math.random() * 500) + 50;
    const totalLines = Math.floor(Math.random() * 10000) + 5000;

    return {
      duplicatedBlocks: Math.floor(Math.random() * 50) + 10,
      duplicatedLines,
      duplicatedFiles: Math.floor(Math.random() * 10) + 2,
      duplicationRatio: (duplicatedLines / totalLines) * 100
    };
  }

  /**
   * Analyse la documentation
   */
  private async analyzeDocumentation(projectId: string): Promise<DocumentationMetrics> {
    return {
      coverage: Math.floor(Math.random() * 30) + 70,
      quality: Math.floor(Math.random() * 20) + 80,
      outdated: Math.floor(Math.random() * 10) + 2,
      missing: Math.floor(Math.random() * 15) + 5
    };
  }

  /**
   * Calcule les métriques de performance
   */
  private async calculatePerformanceMetrics(results: PerformanceTestResult[]): Promise<PerformanceMetrics> {
    if (results.length === 0) {
      return {
        responseTime: { avg: 0, p95: 0, p99: 0, threshold: 200 },
        throughput: { current: 0, target: 1000, unit: 'req/s' },
        resourceUsage: { cpu: 0, memory: 0, storage: 0 },
        score: 0
      };
    }

    const latest = results[0];
    const responseTime = latest.metrics.responseTime;
    const throughput = latest.metrics.throughput;
    const resources = latest.metrics.resources;

    const score = this.calculatePerformanceScore(responseTime.avg, throughput.requestsPerSecond);

    return {
      responseTime: {
        avg: responseTime.avg,
        p95: responseTime.p95,
        p99: responseTime.p99,
        threshold: this.thresholds.get('performance.responseTime') || 200
      },
      throughput: {
        current: throughput.requestsPerSecond,
        target: 1000,
        unit: 'req/s'
      },
      resourceUsage: {
        cpu: resources.cpu.usage,
        memory: resources.memory.usage,
        storage: (resources.storage.readBytes + resources.storage.writeBytes) / 1024 / 1024
      },
      score
    };
  }

  /**
   * Calcule le score de performance
   */
  private calculatePerformanceScore(responseTime: number, throughput: number): number {
    const responseScore = Math.max(0, 100 - (responseTime / 10));
    const throughputScore = Math.min(100, (throughput / 10));
    return Math.round((responseScore + throughputScore) / 2);
  }

  /**
   * Calcule les métriques de fiabilité
   */
  private async calculateReliabilityMetrics(executions: TestExecution[]): Promise<ReliabilityMetrics> {
    if (executions.length === 0) {
      return {
        testPassRate: 0,
        flakyTests: 0,
        testStability: 0,
        errorRate: 0,
        mtbf: 0,
        mttr: 0
      };
    }

    const totalTests = executions.reduce((sum, exec) => sum + exec.summary.total, 0);
    const passedTests = executions.reduce((sum, exec) => sum + exec.summary.passed, 0);
    const failedTests = executions.reduce((sum, exec) => sum + exec.summary.failed, 0);

    return {
      testPassRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
      flakyTests: Math.floor(Math.random() * 5),
      testStability: Math.floor(Math.random() * 10) + 90,
      errorRate: totalTests > 0 ? (failedTests / totalTests) * 100 : 0,
      mtbf: Math.floor(Math.random() * 100) + 50, // heures
      mttr: Math.floor(Math.random() * 30) + 10 // minutes
    };
  }

  /**
   * Calcule les métriques de sécurité
   */
  private async calculateSecurityMetrics(executions: TestExecution[]): Promise<SecurityMetrics> {
    // Simulation de métriques de sécurité
    const vulnerabilities = {
      critical: Math.floor(Math.random() * 3),
      high: Math.floor(Math.random() * 5),
      medium: Math.floor(Math.random() * 10),
      low: Math.floor(Math.random() * 15)
    };

    const totalVulns = Object.values(vulnerabilities).reduce((sum, count) => sum + count, 0);
    const riskScore = vulnerabilities.critical * 10 + vulnerabilities.high * 5 +
                     vulnerabilities.medium * 2 + vulnerabilities.low * 1;

    return {
      vulnerabilities,
      riskScore,
      complianceScore: Math.floor(Math.random() * 20) + 80,
      lastScanDate: new Date()
    };
  }

  /**
   * Collecte les tendances historiques
   */
  private async collectTrends(projectId: string): Promise<QualityMetrics['trends']> {
    const history = this.metricsHistory.get(projectId) || [];

    return {
      testCoverage: history.map(m => ({
        timestamp: m.timestamp,
        value: m.testMetrics.coverage.overall,
        target: m.testMetrics.coverage.threshold
      })),
      performance: history.map(m => ({
        timestamp: m.timestamp,
        value: m.testMetrics.performance.score
      })),
      bugs: history.map(m => ({
        timestamp: m.timestamp,
        value: m.testMetrics.reliability.errorRate
      })),
      security: history.map(m => ({
        timestamp: m.timestamp,
        value: m.testMetrics.security.riskScore
      }))
    };
  }

  /**
   * Génère des recommandations
   */
  private async generateRecommendations(projectId: string): Promise<QualityRecommendation[]> {
    const recommendations: QualityRecommendation[] = [];

    // Recommandations basées sur les seuils
    const metrics = await this.collectTestMetrics(projectId);

    if (metrics.coverage.overall < this.thresholds.get('coverage.overall')!) {
      recommendations.push({
        id: 'coverage-low',
        type: 'testing',
        priority: 'high',
        title: 'Améliorer la couverture de tests',
        description: `La couverture de tests (${metrics.coverage.overall}%) est en dessous du seuil requis (${this.thresholds.get('coverage.overall')}%)`,
        impact: 'Risque accru de bugs en production',
        effort: 'medium',
        actions: [
          'Identifier les modules non couverts',
          'Écrire des tests unitaires manquants',
          'Ajouter des tests d\'intégration'
        ],
        estimatedTime: 16
      });
    }

    if (metrics.performance.score < 70) {
      recommendations.push({
        id: 'performance-low',
        type: 'performance',
        priority: 'medium',
        title: 'Optimiser les performances',
        description: `Le score de performance (${metrics.performance.score}) peut être amélioré`,
        impact: 'Expérience utilisateur dégradée',
        effort: 'high',
        actions: [
          'Profiler les goulots d\'étranglement',
          'Optimiser les requêtes lentes',
          'Implémenter la mise en cache'
        ],
        estimatedTime: 24
      });
    }

    return recommendations;
  }

  /**
   * Sauvegarde les métriques dans l'historique
   */
  private saveMetricsToHistory(projectId: string, metrics: QualityMetrics): void {
    const history = this.metricsHistory.get(projectId) || [];
    history.push(metrics);

    // Garder seulement les 100 dernières entrées
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }

    this.metricsHistory.set(projectId, history);
  }

  /**
   * Vérifie les alertes
   */
  private async checkAlerts(metrics: QualityMetrics): Promise<void> {
    const newAlerts: QualityAlert[] = [];

    // Vérifier les seuils de couverture
    if (metrics.testMetrics.coverage.overall < metrics.testMetrics.coverage.threshold) {
      newAlerts.push({
        id: `alert-coverage-${Date.now()}`,
        type: 'threshold',
        severity: 'warning',
        metric: 'test.coverage',
        message: `Couverture de tests en dessous du seuil (${metrics.testMetrics.coverage.overall}% < ${metrics.testMetrics.coverage.threshold}%)`,
        value: metrics.testMetrics.coverage.overall,
        threshold: metrics.testMetrics.coverage.threshold,
        timestamp: new Date()
      });
    }

    // Vérifier les performances
    if (metrics.testMetrics.performance.responseTime.avg > metrics.testMetrics.performance.responseTime.threshold) {
      newAlerts.push({
        id: `alert-performance-${Date.now()}`,
        type: 'threshold',
        severity: 'error',
        metric: 'performance.responseTime',
        message: `Temps de réponse trop élevé (${metrics.testMetrics.performance.responseTime.avg}ms > ${metrics.testMetrics.performance.responseTime.threshold}ms)`,
        value: metrics.testMetrics.performance.responseTime.avg,
        threshold: metrics.testMetrics.performance.responseTime.threshold,
        timestamp: new Date()
      });
    }

    // Vérifier la sécurité
    if (metrics.testMetrics.security.vulnerabilities.critical > 0) {
      newAlerts.push({
        id: `alert-security-${Date.now()}`,
        type: 'threshold',
        severity: 'critical',
        metric: 'security.vulnerabilities',
        message: `Vulnérabilités critiques détectées (${metrics.testMetrics.security.vulnerabilities.critical})`,
        value: metrics.testMetrics.security.vulnerabilities.critical,
        threshold: 0,
        timestamp: new Date()
      });
    }

    this.alerts.push(...newAlerts);

    // Garder seulement les 50 dernières alertes
    if (this.alerts.length > 50) {
      this.alerts.splice(0, this.alerts.length - 50);
    }
  }

  /**
   * Obtient la version du projet
   */
  private async getProjectVersion(projectId: string): Promise<string> {
    // Simulation - dans un vrai projet, cela viendrait du système de versioning
    return `v1.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 100)}`;
  }

  /**
   * Génère un rapport de qualité complet
   */
  async generateQualityReport(projectId: string): Promise<QualityReport> {
    const metrics = await this.collectMetrics(projectId);

    return {
      id: `report-${projectId}-${Date.now()}`,
      timestamp: new Date(),
      metrics,
      summary: this.generateSummary(metrics),
      alerts: this.alerts.slice(-10), // 10 dernières alertes
      exportFormats: ['json', 'html', 'pdf']
    };
  }

  /**
   * Génère un résumé textuel
   */
  private generateSummary(metrics: QualityMetrics): string {
    const score = metrics.score;
    let summary = `Score de qualité global: ${score.overall}/100 (Grade ${score.grade})\n\n`;

    summary += `📊 Détails par catégorie:\n`;
    summary += `• Tests: ${score.categories.testing}/100\n`;
    summary += `• Performance: ${score.categories.performance}/100\n`;
    summary += `• Sécurité: ${score.categories.security}/100\n`;
    summary += `• Maintenabilité: ${score.categories.maintainability}/100\n`;
    summary += `• Documentation: ${score.categories.documentation}/100\n\n`;

    summary += `📈 Tendance: ${score.trend === 'improving' ? '📈 En amélioration' :
                                score.trend === 'declining' ? '📉 En dégradation' : '➡️ Stable'}\n\n`;

    if (metrics.recommendations.length > 0) {
      summary += `🎯 Recommandations prioritaires:\n`;
      metrics.recommendations.slice(0, 3).forEach((rec, index) => {
        summary += `${index + 1}. ${rec.title} (${rec.priority})\n`;
      });
    }

    return summary;
  }

  /**
   * Exporte les métriques
   */
  async exportMetrics(projectId: string, format: 'json' | 'csv' | 'html'): Promise<string> {
    const report = await this.generateQualityReport(projectId);

    switch (format) {
      case 'json':
        return JSON.stringify(report, null, 2);
      case 'csv':
        return this.convertToCSV(report.metrics);
      case 'html':
        return this.generateHTMLReport(report);
      default:
        throw new Error(`Format non supporté: ${format}`);
    }
  }

  /**
   * Convertit en CSV
   */
  private convertToCSV(metrics: QualityMetrics): string {
    const headers = ['Metric', 'Value', 'Threshold', 'Status'];
    const rows = [
      ['Test Coverage', metrics.testMetrics.coverage.overall.toString(), metrics.testMetrics.coverage.threshold.toString(), metrics.testMetrics.coverage.status],
      ['Performance Score', metrics.testMetrics.performance.score.toString(), '70', metrics.testMetrics.performance.score >= 70 ? 'good' : 'warning'],
      ['Security Risk', metrics.testMetrics.security.riskScore.toString(), '20', metrics.testMetrics.security.riskScore <= 20 ? 'good' : 'warning']
    ];

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  /**
   * Génère un rapport HTML
   */
  private generateHTMLReport(report: QualityReport): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Rapport de Qualité - ${report.id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .score { font-size: 2em; font-weight: bold; color: #2196F3; }
        .metric { margin: 10px 0; padding: 10px; border-left: 4px solid #2196F3; }
        .alert { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .critical { background-color: #ffebee; border-left: 4px solid #f44336; }
        .warning { background-color: #fff3e0; border-left: 4px solid #ff9800; }
    </style>
</head>
<body>
    <h1>Rapport de Qualité</h1>
    <p>Généré le: ${report.timestamp.toLocaleString()}</p>

    <div class="score">Score Global: ${report.metrics.score.overall}/100 (${report.metrics.score.grade})</div>

    <h2>Métriques Détaillées</h2>
    <div class="metric">
        <strong>Couverture de Tests:</strong> ${report.metrics.testMetrics.coverage.overall}%
    </div>
    <div class="metric">
        <strong>Score Performance:</strong> ${report.metrics.testMetrics.performance.score}/100
    </div>
    <div class="metric">
        <strong>Risque Sécurité:</strong> ${report.metrics.testMetrics.security.riskScore}
    </div>

    <h2>Alertes</h2>
    ${report.alerts.map(alert => `
        <div class="alert ${alert.severity}">
            <strong>${alert.severity.toUpperCase()}:</strong> ${alert.message}
        </div>
    `).join('')}

    <h2>Résumé</h2>
    <pre>${report.summary}</pre>
</body>
</html>`;
  }

  /**
   * Obtient les alertes actives
   */
  getActiveAlerts(): QualityAlert[] {
    return this.alerts.slice(-10);
  }

  /**
   * Obtient l'historique des métriques
   */
  getMetricsHistory(projectId: string): QualityMetrics[] {
    return this.metricsHistory.get(projectId) || [];
  }
}
