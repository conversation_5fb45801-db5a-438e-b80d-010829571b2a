{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "../", "jsx": "react-jsx", "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "baseUrl": "./hanuman_sandbox", "paths": {"@/*": ["./hanuman_sandbox/*"], "@infrastructure/*": ["./hanuman_sandbox/infrastructure/*"], "@environments/*": ["./hanuman_sandbox/environments/*"], "@security/*": ["./hanuman_sandbox/security/*"], "@interfaces/*": ["./hanuman_sandbox/interfaces/*"], "@tests/*": ["./hanuman_sandbox/tests/*"]}}, "include": ["**/*.ts", "**/*.tsx", "../hanuman-working/services/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": false, "compilerOptions": {"module": "CommonJS", "target": "ES2020"}}}