import { EventEmitter } from 'events';
import { SandboxContainer, SandboxInfrastructure } from '../../infrastructure/sandbox_infrastructure';

// Types pour VS Code Server
export interface VSCodeServerConfig {
  version: string;
  port: number;
  password?: string;
  extensions: string[];
  settings: Record<string, any>;
  workspaceFolder: string;
  tunnelConfig?: {
    enabled: boolean;
    subdomain?: string;
    auth: 'password' | 'github' | 'microsoft';
  };
}

export interface VSCodeInstance {
  id: string;
  containerId: string;
  agentId?: string;
  organId?: string;
  config: VSCodeServerConfig;
  status: 'starting' | 'running' | 'stopped' | 'error';
  url: string;
  tunnelUrl?: string;
  createdAt: Date;
  lastActivity: Date;
  resources: {
    cpu: number;
    memory: number;
    storage: number;
  };
}

export interface RooCoder {
  enabled: boolean;
  version: string;
  apiKey?: string;
  model: 'gpt-4' | 'claude-3' | 'codellama' | 'custom';
  templates: string[];
  customPrompts: Record<string, string>;
  autoComplete: boolean;
  codeGeneration: boolean;
  contextAware: boolean;
}

/**
 * Gestionnaire VS Code Server pour la Sandbox Hanuman
 * Déploie et gère des instances VS Code avec Roo Coder pour chaque agent
 */
export class VSCodeServerManager extends EventEmitter {
  private infrastructure: SandboxInfrastructure;
  private instances: Map<string, VSCodeInstance> = new Map();
  private defaultConfig: VSCodeServerConfig;
  private rooCoder: RooCoder;

  constructor(infrastructure: SandboxInfrastructure) {
    super();
    this.infrastructure = infrastructure;
    this.initializeDefaultConfig();
    this.setupRooCoder();
    this.setupEventHandlers();
  }

  /**
   * Initialise la configuration par défaut de VS Code Server
   */
  private initializeDefaultConfig(): void {
    this.defaultConfig = {
      version: '4.20.0',
      port: 8080,
      extensions: [
        'ms-vscode.vscode-typescript-next',
        'bradlc.vscode-tailwindcss',
        'esbenp.prettier-vscode',
        'ms-vscode.vscode-json',
        'ms-python.python',
        'ms-vscode.vscode-eslint',
        'GitLab.gitlab-workflow',
        'ms-vscode.vscode-docker',
        'ms-kubernetes-tools.vscode-kubernetes-tools',
        'roo-coder.roo-coder' // Extension Roo Coder
      ],
      settings: {
        'workbench.colorTheme': 'Hanuman Dark',
        'editor.fontSize': 14,
        'editor.tabSize': 2,
        'editor.insertSpaces': true,
        'editor.formatOnSave': true,
        'editor.codeActionsOnSave': {
          'source.fixAll.eslint': true
        },
        'typescript.preferences.importModuleSpecifier': 'relative',
        'git.enableSmartCommit': true,
        'git.confirmSync': false,
        'rooCoder.enabled': true,
        'rooCoder.autoComplete': true,
        'rooCoder.contextAware': true
      },
      workspaceFolder: '/workspace',
      tunnelConfig: {
        enabled: true,
        auth: 'password'
      }
    };
  }

  /**
   * Configure Roo Coder pour les agents Hanuman
   */
  private setupRooCoder(): void {
    this.rooCoder = {
      enabled: true,
      version: 'latest',
      model: 'gpt-4',
      templates: [
        'hanuman-agent-template',
        'hanuman-organ-template',
        'hanuman-interface-template',
        'hanuman-service-template',
        'react-component-template',
        'typescript-class-template',
        'test-suite-template'
      ],
      customPrompts: {
        'agent-creation': 'Créer un nouvel agent Hanuman avec les capacités spécifiées, en respectant l\'architecture des organes existants.',
        'organ-enhancement': 'Améliorer cet organe Hanuman en ajoutant de nouvelles fonctionnalités tout en maintenant la compatibilité.',
        'interface-design': 'Concevoir une interface React moderne et accessible pour cet agent Hanuman.',
        'service-integration': 'Intégrer ce service avec l\'écosystème Hanuman en utilisant les patterns établis.',
        'test-generation': 'Générer des tests complets pour ce composant Hanuman, incluant les cas limites.',
        'documentation': 'Créer une documentation complète pour ce module Hanuman avec exemples d\'usage.'
      },
      autoComplete: true,
      codeGeneration: true,
      contextAware: true
    };
  }

  /**
   * Configure les gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    this.infrastructure.on('container:created', this.onContainerCreated.bind(this));
    this.infrastructure.on('container:destroyed', this.onContainerDestroyed.bind(this));
  }

  /**
   * Gestionnaire pour la création de conteneur
   */
  private async onContainerCreated(container: SandboxContainer): Promise<void> {
    // Déployer automatiquement VS Code pour les conteneurs de développement
    if (container.type === 'development' && (container.agentId || container.organId)) {
      await this.deployVSCodeServer(container);
    }
  }

  /**
   * Gestionnaire pour la destruction de conteneur
   */
  private async onContainerDestroyed(container: SandboxContainer): Promise<void> {
    const instance = Array.from(this.instances.values())
      .find(i => i.containerId === container.id);
    
    if (instance) {
      await this.stopVSCodeServer(instance.id);
    }
  }

  /**
   * Déploie une instance VS Code Server dans un conteneur
   */
  async deployVSCodeServer(container: SandboxContainer, customConfig?: Partial<VSCodeServerConfig>): Promise<VSCodeInstance> {
    const config = { ...this.defaultConfig, ...customConfig };
    
    const instance: VSCodeInstance = {
      id: `vscode_${container.id}`,
      containerId: container.id,
      agentId: container.agentId,
      organId: container.organId,
      config,
      status: 'starting',
      url: `http://localhost:${config.port}`,
      createdAt: new Date(),
      lastActivity: new Date(),
      resources: {
        cpu: 1,
        memory: 2048,
        storage: 5000
      }
    };

    try {
      // Installer VS Code Server dans le conteneur
      await this.installVSCodeServer(instance);
      
      // Configurer les extensions
      await this.installExtensions(instance);
      
      // Configurer Roo Coder
      await this.configureRooCoder(instance);
      
      // Démarrer le serveur
      await this.startVSCodeServer(instance);
      
      // Configurer le tunnel si activé
      if (config.tunnelConfig?.enabled) {
        await this.setupTunnel(instance);
      }

      instance.status = 'running';
      this.instances.set(instance.id, instance);
      
      this.emit('vscode:deployed', instance);
      console.log(`🚀 VS Code Server déployé pour ${container.name}: ${instance.url}`);
      
      return instance;

    } catch (error) {
      instance.status = 'error';
      this.emit('vscode:error', { instance, error });
      console.error(`❌ Erreur lors du déploiement VS Code pour ${container.name}:`, error);
      throw error;
    }
  }

  /**
   * Installe VS Code Server dans le conteneur
   */
  private async installVSCodeServer(instance: VSCodeInstance): Promise<void> {
    const installScript = `
      # Télécharger et installer code-server
      curl -fsSL https://code-server.dev/install.sh | sh -s -- --version=${instance.config.version}
      
      # Créer le dossier de workspace
      mkdir -p ${instance.config.workspaceFolder}
      
      # Configurer les permissions
      chown -R coder:coder ${instance.config.workspaceFolder}
    `;

    // Simuler l'exécution du script d'installation
    console.log(`📦 Installation de VS Code Server ${instance.config.version}...`);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  /**
   * Installe les extensions VS Code
   */
  private async installExtensions(instance: VSCodeInstance): Promise<void> {
    console.log(`🔌 Installation des extensions VS Code...`);
    
    for (const extension of instance.config.extensions) {
      console.log(`   - Installation de ${extension}`);
      // Simuler l'installation d'extension
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  /**
   * Configure Roo Coder pour l'instance
   */
  private async configureRooCoder(instance: VSCodeInstance): Promise<void> {
    console.log(`🤖 Configuration de Roo Coder...`);
    
    const rooCodingConfig = {
      ...this.rooCoder,
      workspaceContext: {
        agentId: instance.agentId,
        organId: instance.organId,
        projectType: 'hanuman-agent',
        architecture: 'microservices',
        framework: 'react-typescript'
      },
      customTemplates: this.generateCustomTemplates(instance)
    };

    // Appliquer la configuration Roo Coder
    instance.config.settings['rooCoder.config'] = rooCodingConfig;
    
    console.log(`   ✅ Roo Coder configuré avec ${this.rooCoder.templates.length} templates`);
  }

  /**
   * Génère des templates personnalisés pour l'agent
   */
  private generateCustomTemplates(instance: VSCodeInstance): Record<string, string> {
    const agentName = instance.agentId || instance.organId || 'unknown';
    
    return {
      'hanuman-component': `
import React from 'react';

interface ${agentName}Props {
  // Props pour ${agentName}
}

export const ${agentName}: React.FC<${agentName}Props> = (props) => {
  return (
    <div className="hanuman-component">
      {/* Implémentation ${agentName} */}
    </div>
  );
};

export default ${agentName};
      `,
      'hanuman-service': `
import { EventEmitter } from 'events';

export class ${agentName}Service extends EventEmitter {
  constructor() {
    super();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    // Initialisation du service ${agentName}
  }

  // Méthodes du service ${agentName}
}
      `,
      'hanuman-test': `
import { ${agentName} } from './${agentName}';

describe('${agentName}', () => {
  test('should initialize correctly', () => {
    // Tests pour ${agentName}
  });
});
      `
    };
  }

  /**
   * Démarre le serveur VS Code
   */
  private async startVSCodeServer(instance: VSCodeInstance): Promise<void> {
    console.log(`🚀 Démarrage du serveur VS Code...`);
    
    const startCommand = `
      code-server \\
        --bind-addr 0.0.0.0:${instance.config.port} \\
        --auth password \\
        --password "${instance.config.password || 'hanuman123'}" \\
        --disable-telemetry \\
        --disable-update-check \\
        ${instance.config.workspaceFolder}
    `;

    // Simuler le démarrage du serveur
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    instance.url = `http://localhost:${instance.config.port}`;
    console.log(`   ✅ Serveur démarré sur ${instance.url}`);
  }

  /**
   * Configure un tunnel sécurisé pour l'accès externe
   */
  private async setupTunnel(instance: VSCodeInstance): Promise<void> {
    if (!instance.config.tunnelConfig?.enabled) return;

    console.log(`🌐 Configuration du tunnel sécurisé...`);
    
    // Générer une URL de tunnel unique
    const subdomain = instance.config.tunnelConfig.subdomain || 
                     `${instance.agentId || instance.organId}-${instance.id.slice(-8)}`;
    
    instance.tunnelUrl = `https://${subdomain}.hanuman-sandbox.dev`;
    
    // Simuler la configuration du tunnel
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log(`   ✅ Tunnel configuré: ${instance.tunnelUrl}`);
  }

  /**
   * Arrête une instance VS Code Server
   */
  async stopVSCodeServer(instanceId: string): Promise<void> {
    const instance = this.instances.get(instanceId);
    if (!instance) return;

    try {
      console.log(`🛑 Arrêt de VS Code Server ${instanceId}...`);
      
      instance.status = 'stopped';
      this.instances.delete(instanceId);
      
      this.emit('vscode:stopped', instance);
      console.log(`   ✅ VS Code Server arrêté`);

    } catch (error) {
      console.error(`❌ Erreur lors de l'arrêt de VS Code Server:`, error);
      this.emit('vscode:error', { instance, error });
    }
  }

  /**
   * Redémarre une instance VS Code Server
   */
  async restartVSCodeServer(instanceId: string): Promise<void> {
    const instance = this.instances.get(instanceId);
    if (!instance) throw new Error(`Instance ${instanceId} non trouvée`);

    await this.stopVSCodeServer(instanceId);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const container = this.infrastructure.getContainer(instance.containerId);
    if (container) {
      await this.deployVSCodeServer(container, instance.config);
    }
  }

  /**
   * Met à jour la configuration d'une instance
   */
  async updateInstanceConfig(instanceId: string, newConfig: Partial<VSCodeServerConfig>): Promise<void> {
    const instance = this.instances.get(instanceId);
    if (!instance) throw new Error(`Instance ${instanceId} non trouvée`);

    instance.config = { ...instance.config, ...newConfig };
    this.instances.set(instanceId, instance);
    
    // Redémarrer pour appliquer la nouvelle configuration
    await this.restartVSCodeServer(instanceId);
    
    this.emit('vscode:config-updated', instance);
  }

  /**
   * Obtient toutes les instances VS Code
   */
  getInstances(): VSCodeInstance[] {
    return Array.from(this.instances.values());
  }

  /**
   * Obtient une instance par ID
   */
  getInstance(instanceId: string): VSCodeInstance | undefined {
    return this.instances.get(instanceId);
  }

  /**
   * Obtient les instances d'un agent
   */
  getInstancesByAgent(agentId: string): VSCodeInstance[] {
    return Array.from(this.instances.values())
      .filter(i => i.agentId === agentId);
  }

  /**
   * Obtient les instances d'un organe
   */
  getInstancesByOrgan(organId: string): VSCodeInstance[] {
    return Array.from(this.instances.values())
      .filter(i => i.organId === organId);
  }

  /**
   * Obtient les statistiques des instances
   */
  getStats() {
    const instances = this.getInstances();
    
    return {
      total: instances.length,
      running: instances.filter(i => i.status === 'running').length,
      stopped: instances.filter(i => i.status === 'stopped').length,
      error: instances.filter(i => i.status === 'error').length,
      totalResources: {
        cpu: instances.reduce((sum, i) => sum + i.resources.cpu, 0),
        memory: instances.reduce((sum, i) => sum + i.resources.memory, 0),
        storage: instances.reduce((sum, i) => sum + i.resources.storage, 0)
      }
    };
  }
}

export default VSCodeServerManager;
