import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';

export interface AgentInfo {
  id: string;
  name: string;
  type: AgentType;
  brainRegion: BrainRegion;
  status: AgentStatus;
  capabilities: string[];
  specializations: string[];
  healthScore: number;
  lastActivity: Date;
  connectionInfo: {
    url: string;
    port: number;
    protocol: 'http' | 'websocket' | 'grpc';
  };
  metrics: {
    tasksCompleted: number;
    averageResponseTime: number;
    successRate: number;
    currentLoad: number;
  };
}

export enum AgentType {
  // Cortex Spécialisés
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  QA = 'qa',
  
  // Cervelet (Coordination)
  DEVOPS = 'devops',
  SEO = 'seo',
  
  // Tronc Cérébral (Fonctions Vitales)
  SECURITY = 'security',
  PERFORMANCE = 'performance',
  
  // Système Limbique (Émotions/Relations)
  MARKETING = 'marketing',
  
  // Organes Sensoriels
  WEB_RESEARCH = 'web-research',
  DATA_COLLECTOR = 'data-collector',
  API_MONITOR = 'api-monitor',
  MCP_CONNECTOR = 'mcp-connector',
  
  // Aires Spécialisées
  TRANSLATION = 'translation',
  DOCUMENTATION = 'documentation',
  MIGRATION = 'migration',
  COMPLIANCE = 'compliance',
  EVOLUTION = 'evolution'
}

export enum BrainRegion {
  CORTEX_CENTRAL = 'cortex-central',
  CORTEX_DECISION = 'cortex-decision',
  CORTEX_CREATIVE = 'cortex-creative',
  CORTEX_LOGICAL = 'cortex-logical',
  CORTEX_ANALYTICAL = 'cortex-analytical',
  CORTEX_PREFRONTAL = 'cortex-prefrontal',
  CORTEX_MOTOR = 'cortex-motor',
  LIMBIC_SYSTEM = 'limbic-system',
  CEREBELLUM_TECHNICAL = 'cerebellum-technical',
  CEREBELLUM_MARKETING = 'cerebellum-marketing',
  BRAINSTEM_IMMUNE = 'brainstem-immune',
  BRAINSTEM_VITAL = 'brainstem-vital',
  SENSORY_VISION = 'sensory-vision',
  SENSORY_HEARING = 'sensory-hearing',
  SENSORY_TOUCH = 'sensory-touch',
  SENSORY_TASTE_SMELL = 'sensory-taste-smell',
  BROCA_AREA = 'broca-area',
  WERNICKE_AREA = 'wernicke-area',
  NEUROPLASTICITY = 'neuroplasticity'
}

export enum AgentStatus {
  INITIALIZING = 'initializing',
  ACTIVE = 'active',
  BUSY = 'busy',
  IDLE = 'idle',
  MAINTENANCE = 'maintenance',
  ERROR = 'error',
  OFFLINE = 'offline'
}

export interface AgentRegistryConfig {
  memory: CentralMemory;
  communication: SynapticCommunication;
  healthCheckInterval?: number;
  maxRetries?: number;
}

/**
 * Registre des Agents - Gestion de l'Organisme IA Vivant
 * 
 * Gère les 17 agents spécialisés organisés comme un cerveau humain
 * avec leurs connexions synaptiques et leur santé cognitive.
 */
export class AgentRegistry extends EventEmitter {
  private memory: CentralMemory;
  private communication: SynapticCommunication;
  private agents: Map<string, AgentInfo> = new Map();
  private healthCheckInterval: number;
  private maxRetries: number;
  private isInitialized: boolean = false;
  private healthCheckTimer?: NodeJS.Timeout;

  // Cartographie anatomique des agents
  private readonly BRAIN_ANATOMY: Record<AgentType, BrainRegion> = {
    [AgentType.FRONTEND]: BrainRegion.CORTEX_CREATIVE,
    [AgentType.BACKEND]: BrainRegion.CORTEX_LOGICAL,
    [AgentType.QA]: BrainRegion.CORTEX_ANALYTICAL,
    [AgentType.DEVOPS]: BrainRegion.CEREBELLUM_TECHNICAL,
    [AgentType.SEO]: BrainRegion.CEREBELLUM_MARKETING,
    [AgentType.SECURITY]: BrainRegion.BRAINSTEM_IMMUNE,
    [AgentType.PERFORMANCE]: BrainRegion.BRAINSTEM_VITAL,
    [AgentType.MARKETING]: BrainRegion.LIMBIC_SYSTEM,
    [AgentType.WEB_RESEARCH]: BrainRegion.SENSORY_VISION,
    [AgentType.DATA_COLLECTOR]: BrainRegion.SENSORY_HEARING,
    [AgentType.API_MONITOR]: BrainRegion.SENSORY_TASTE_SMELL,
    [AgentType.MCP_CONNECTOR]: BrainRegion.SENSORY_TOUCH,
    [AgentType.TRANSLATION]: BrainRegion.BROCA_AREA,
    [AgentType.DOCUMENTATION]: BrainRegion.WERNICKE_AREA,
    [AgentType.MIGRATION]: BrainRegion.CORTEX_MOTOR,
    [AgentType.COMPLIANCE]: BrainRegion.CORTEX_PREFRONTAL,
    [AgentType.EVOLUTION]: BrainRegion.NEUROPLASTICITY
  };

  constructor(config: AgentRegistryConfig) {
    super();
    
    this.memory = config.memory;
    this.communication = config.communication;
    this.healthCheckInterval = config.healthCheckInterval || 30000; // 30 secondes
    this.maxRetries = config.maxRetries || 3;
  }

  /**
   * Initialise le registre des agents
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('🧠 Initialisation du Registre des Agents...');

      // Chargement des agents depuis la mémoire
      await this.loadAgentsFromMemory();

      // Découverte automatique des agents
      await this.discoverAgents();

      // Démarrage du monitoring de santé
      this.startHealthMonitoring();

      // Configuration des événements de communication
      this.setupCommunicationEvents();

      this.isInitialized = true;
      logger.info(`✅ Registre des Agents initialisé avec ${this.agents.size} agents`);

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation du Registre des Agents:', error);
      throw error;
    }
  }

  /**
   * Enregistre un nouvel agent
   */
  public async registerAgent(agentInfo: Partial<AgentInfo>): Promise<void> {
    if (!agentInfo.id || !agentInfo.type) {
      throw new Error('ID et type d\'agent requis pour l\'enregistrement');
    }

    const agent: AgentInfo = {
      id: agentInfo.id,
      name: agentInfo.name || agentInfo.id,
      type: agentInfo.type,
      brainRegion: this.BRAIN_ANATOMY[agentInfo.type],
      status: AgentStatus.INITIALIZING,
      capabilities: agentInfo.capabilities || [],
      specializations: agentInfo.specializations || [],
      healthScore: 100,
      lastActivity: new Date(),
      connectionInfo: agentInfo.connectionInfo || {
        url: `http://agent-${agentInfo.type}`,
        port: 3000,
        protocol: 'http'
      },
      metrics: {
        tasksCompleted: 0,
        averageResponseTime: 0,
        successRate: 100,
        currentLoad: 0
      }
    };

    this.agents.set(agent.id, agent);
    
    // Sauvegarde en mémoire
    await this.saveAgentToMemory(agent);

    // Vérification de santé initiale
    await this.checkAgentHealth(agent.id);

    logger.info(`🤖 Agent enregistré: ${agent.name} (${agent.type}) dans ${agent.brainRegion}`);
    
    this.emit('agent-registered', {
      agent,
      timestamp: new Date()
    });
  }

  /**
   * Obtient un agent par ID
   */
  public getAgent(agentId: string): AgentInfo | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Obtient tous les agents d'un type donné
   */
  public getAgentsByType(type: AgentType): AgentInfo[] {
    return Array.from(this.agents.values()).filter(agent => agent.type === type);
  }

  /**
   * Obtient tous les agents d'une région cérébrale
   */
  public getAgentsByBrainRegion(region: BrainRegion): AgentInfo[] {
    return Array.from(this.agents.values()).filter(agent => agent.brainRegion === region);
  }

  /**
   * Obtient les agents disponibles pour une tâche
   */
  public getAvailableAgents(capabilities?: string[]): AgentInfo[] {
    return Array.from(this.agents.values()).filter(agent => {
      const isAvailable = agent.status === AgentStatus.ACTIVE || agent.status === AgentStatus.IDLE;
      const hasCapabilities = !capabilities || capabilities.some(cap => 
        agent.capabilities.includes(cap) || agent.specializations.includes(cap)
      );
      return isAvailable && hasCapabilities;
    });
  }

  /**
   * Met à jour le statut d'un agent
   */
  public async updateAgentStatus(agentId: string, status: AgentStatus): Promise<void> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent non trouvé: ${agentId}`);
    }

    const oldStatus = agent.status;
    agent.status = status;
    agent.lastActivity = new Date();

    this.agents.set(agentId, agent);
    await this.saveAgentToMemory(agent);

    logger.debug(`🔄 Statut de l'agent ${agent.name} changé: ${oldStatus} → ${status}`);

    this.emit('agent-status-changed', {
      agentId,
      oldStatus,
      newStatus: status,
      timestamp: new Date()
    });
  }

  /**
   * Met à jour les métriques d'un agent
   */
  public async updateAgentMetrics(agentId: string, metrics: Partial<AgentInfo['metrics']>): Promise<void> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent non trouvé: ${agentId}`);
    }

    agent.metrics = { ...agent.metrics, ...metrics };
    agent.lastActivity = new Date();

    // Calcul du score de santé basé sur les métriques
    agent.healthScore = this.calculateHealthScore(agent);

    this.agents.set(agentId, agent);
    await this.saveAgentToMemory(agent);

    this.emit('agent-metrics-updated', {
      agentId,
      metrics: agent.metrics,
      healthScore: agent.healthScore,
      timestamp: new Date()
    });
  }

  /**
   * Découverte automatique des agents
   */
  private async discoverAgents(): Promise<void> {
    logger.info('🔍 Découverte automatique des agents...');

    const expectedAgents = Object.values(AgentType);
    
    for (const agentType of expectedAgents) {
      const agentId = `agent-${agentType}`;
      
      if (!this.agents.has(agentId)) {
        try {
          // Tentative de connexion à l'agent
          const connectionInfo = {
            url: `http://agent-${agentType}`,
            port: 3000,
            protocol: 'http' as const
          };

          const isReachable = await this.testAgentConnection(connectionInfo);
          
          if (isReachable) {
            await this.registerAgent({
              id: agentId,
              name: `Agent ${agentType.charAt(0).toUpperCase() + agentType.slice(1)}`,
              type: agentType,
              connectionInfo
            });
          } else {
            logger.warn(`⚠️ Agent ${agentType} non accessible à ${connectionInfo.url}:${connectionInfo.port}`);
          }
        } catch (error) {
          logger.debug(`Agent ${agentType} non découvert:`, error.message);
        }
      }
    }
  }

  /**
   * Teste la connexion à un agent
   */
  private async testAgentConnection(connectionInfo: AgentInfo['connectionInfo']): Promise<boolean> {
    try {
      // Implémentation simplifiée - devrait faire un vrai test de connexion
      // const response = await fetch(`${connectionInfo.url}:${connectionInfo.port}/health`);
      // return response.ok;
      return true; // Pour la démo
    } catch (error) {
      return false;
    }
  }

  /**
   * Calcule le score de santé d'un agent
   */
  private calculateHealthScore(agent: AgentInfo): number {
    let score = 100;

    // Pénalité pour faible taux de succès
    if (agent.metrics.successRate < 90) {
      score -= (90 - agent.metrics.successRate);
    }

    // Pénalité pour temps de réponse élevé
    if (agent.metrics.averageResponseTime > 1000) {
      score -= Math.min(20, (agent.metrics.averageResponseTime - 1000) / 100);
    }

    // Pénalité pour charge élevée
    if (agent.metrics.currentLoad > 80) {
      score -= (agent.metrics.currentLoad - 80) / 4;
    }

    // Pénalité pour inactivité
    const inactiveTime = Date.now() - agent.lastActivity.getTime();
    if (inactiveTime > 300000) { // 5 minutes
      score -= Math.min(30, inactiveTime / 60000); // -1 point par minute
    }

    return Math.max(0, Math.round(score));
  }

  /**
   * Vérifie la santé d'un agent
   */
  private async checkAgentHealth(agentId: string): Promise<void> {
    const agent = this.agents.get(agentId);
    if (!agent) return;

    try {
      // Test de connexion
      const isReachable = await this.testAgentConnection(agent.connectionInfo);
      
      if (isReachable) {
        if (agent.status === AgentStatus.ERROR || agent.status === AgentStatus.OFFLINE) {
          await this.updateAgentStatus(agentId, AgentStatus.ACTIVE);
        }
      } else {
        if (agent.status !== AgentStatus.OFFLINE) {
          await this.updateAgentStatus(agentId, AgentStatus.OFFLINE);
        }
      }

      // Recalcul du score de santé
      agent.healthScore = this.calculateHealthScore(agent);
      
      if (agent.healthScore < 50) {
        logger.warn(`⚠️ Santé dégradée pour l'agent ${agent.name}: ${agent.healthScore}%`);
        this.emit('agent-health-degraded', {
          agentId,
          healthScore: agent.healthScore,
          timestamp: new Date()
        });
      }

    } catch (error) {
      logger.error(`❌ Erreur lors de la vérification de santé de l'agent ${agentId}:`, error);
      await this.updateAgentStatus(agentId, AgentStatus.ERROR);
    }
  }

  /**
   * Démarrage du monitoring de santé
   */
  private startHealthMonitoring(): void {
    this.healthCheckTimer = setInterval(async () => {
      for (const agentId of this.agents.keys()) {
        await this.checkAgentHealth(agentId);
      }
    }, this.healthCheckInterval);

    logger.info(`💓 Monitoring de santé démarré (intervalle: ${this.healthCheckInterval}ms)`);
  }

  /**
   * Configuration des événements de communication
   */
  private setupCommunicationEvents(): void {
    this.communication.on('agent-heartbeat', (data) => {
      this.handleAgentHeartbeat(data);
    });

    this.communication.on('agent-task-completed', (data) => {
      this.handleAgentTaskCompleted(data);
    });
  }

  /**
   * Gestion des battements de cœur des agents
   */
  private async handleAgentHeartbeat(data: any): Promise<void> {
    const { agentId, metrics } = data;
    
    if (this.agents.has(agentId)) {
      await this.updateAgentMetrics(agentId, metrics);
    }
  }

  /**
   * Gestion de la completion de tâches par les agents
   */
  private async handleAgentTaskCompleted(data: any): Promise<void> {
    const { agentId, success, responseTime } = data;
    const agent = this.agents.get(agentId);
    
    if (agent) {
      const newMetrics = {
        tasksCompleted: agent.metrics.tasksCompleted + 1,
        averageResponseTime: (agent.metrics.averageResponseTime + responseTime) / 2,
        successRate: success ? 
          Math.min(100, agent.metrics.successRate + 0.1) : 
          Math.max(0, agent.metrics.successRate - 1)
      };

      await this.updateAgentMetrics(agentId, newMetrics);
    }
  }

  /**
   * Charge les agents depuis la mémoire
   */
  private async loadAgentsFromMemory(): Promise<void> {
    try {
      const storedAgents = await this.memory.retrieve('agent_registry');
      if (storedAgents && Array.isArray(storedAgents)) {
        for (const agentData of storedAgents) {
          this.agents.set(agentData.id, agentData);
        }
        logger.info(`📚 ${storedAgents.length} agents chargés depuis la mémoire`);
      }
    } catch (error) {
      logger.warn('⚠️ Impossible de charger les agents depuis la mémoire:', error);
    }
  }

  /**
   * Sauvegarde un agent en mémoire
   */
  private async saveAgentToMemory(agent: AgentInfo): Promise<void> {
    try {
      const allAgents = Array.from(this.agents.values());
      await this.memory.store('agent_registry', allAgents);
    } catch (error) {
      logger.error('❌ Erreur lors de la sauvegarde de l\'agent:', error);
    }
  }

  /**
   * Obtient les statistiques du registre
   */
  public getRegistryStats() {
    const agents = Array.from(this.agents.values());
    const statusCounts = agents.reduce((acc, agent) => {
      acc[agent.status] = (acc[agent.status] || 0) + 1;
      return acc;
    }, {} as Record<AgentStatus, number>);

    const brainRegionCounts = agents.reduce((acc, agent) => {
      acc[agent.brainRegion] = (acc[agent.brainRegion] || 0) + 1;
      return acc;
    }, {} as Record<BrainRegion, number>);

    return {
      totalAgents: agents.length,
      statusDistribution: statusCounts,
      brainRegionDistribution: brainRegionCounts,
      averageHealthScore: agents.reduce((sum, agent) => sum + agent.healthScore, 0) / agents.length,
      activeAgents: agents.filter(a => a.status === AgentStatus.ACTIVE).length,
      lastUpdate: new Date()
    };
  }

  /**
   * Arrêt gracieux du registre
   */
  public async shutdown(): Promise<void> {
    logger.info('🛑 Arrêt du Registre des Agents...');
    
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    // Sauvegarde finale
    const allAgents = Array.from(this.agents.values());
    await this.memory.store('agent_registry', allAgents);

    this.isInitialized = false;
    logger.info('✅ Registre des Agents arrêté');
  }
}
