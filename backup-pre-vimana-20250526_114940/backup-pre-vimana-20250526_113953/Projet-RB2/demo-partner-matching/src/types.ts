export type PartnerTier = 'PARTNER' | 'CERTIFIED' | 'PREMIUM';

export interface MatchingCriteria {
  location: string;
  startDate: Date | null;
  endDate: Date | null;
  participants: number;
  type: 'venue' | 'catering' | 'travel' | 'wellness';
  budget: number;
}

export interface Partner {
  id: string;
  name: string;
  type: 'venue' | 'catering' | 'travel' | 'wellness';
  location: string;
  rating: number;
  reviewCount: number;
  priceRange: [number, number];
  availability: string;
  maxCapacity: number;
  matchScore: number;
  avatar: string;
  matchReasons: string[];
}

export interface PartnerTierConfig {
  name: string;
  description: string;
  features: string[];
  limitations: string[];
  price: string;
}
