import React, { useState, useEffect } from 'react';
import {
  Box,
  Badge,
  IconButton,
  Popover,
  List,
  ListItem,
  ListItemText,
  Typography,
  Divider,
  Button,
  CircularProgress,
  Paper
} from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import PaymentIcon from '@mui/icons-material/Payment';
import SubscriptionsIcon from '@mui/icons-material/Subscriptions';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

// Types de notifications
enum NotificationType {
  SUBSCRIPTION_CREATED = 'SUBSCRIPTION_CREATED',
  SUBSCRIPTION_CANCELLED = 'SUBSCRIPTION_CANCELLED',
  SUBSCRIPTION_CHANGED = 'SUBSCRIPTION_CHANGED',
  PAYMENT_SUCCEEDED = 'PAYMENT_SUCCEEDED',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  SUBSCRIPTION_EXPIRING = 'SUBSCRIPTION_EXPIRING',
  SUBSCRIPTION_RENEWED = 'SUBSCRIPTION_RENEWED',
}

// Interface pour les notifications
interface Notification {
  id: string;
  type: NotificationType;
  partnerId: string;
  subscriptionId?: string;
  paymentId?: string;
  data: string;
  read: boolean;
  createdAt: string;
}

const NotificationCenter: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  const open = Boolean(anchorEl);
  const id = open ? 'notification-popover' : undefined;

  // Simuler le chargement des notifications
  useEffect(() => {
    const fetchNotifications = async () => {
      setLoading(true);
      try {
        // Simuler un délai de chargement
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Données de démonstration
        const mockNotifications: Notification[] = [
          {
            id: '1',
            type: NotificationType.SUBSCRIPTION_CREATED,
            partnerId: 'partner_123',
            subscriptionId: 'sub_123',
            data: JSON.stringify({
              newTier: 'PREMIUM',
              amount: 199,
              currency: 'EUR'
            }),
            read: false,
            createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '2',
            type: NotificationType.PAYMENT_SUCCEEDED,
            partnerId: 'partner_123',
            subscriptionId: 'sub_123',
            paymentId: 'pay_123',
            data: JSON.stringify({
              amount: 199,
              currency: 'EUR'
            }),
            read: false,
            createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '3',
            type: NotificationType.SUBSCRIPTION_EXPIRING,
            partnerId: 'partner_123',
            subscriptionId: 'sub_123',
            data: JSON.stringify({
              expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
            }),
            read: true,
            createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
          }
        ];
        
        setNotifications(mockNotifications);
        setUnreadCount(mockNotifications.filter(n => !n.read).length);
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, []);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMarkAsRead = (notificationId: string) => {
    setNotifications(notifications.map(notification => 
      notification.id === notificationId 
        ? { ...notification, read: true } 
        : notification
    ));
    
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const handleMarkAllAsRead = () => {
    setNotifications(notifications.map(notification => ({ ...notification, read: true })));
    setUnreadCount(0);
  };

  // Fonction pour obtenir l'icône selon le type de notification
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case NotificationType.SUBSCRIPTION_CREATED:
      case NotificationType.SUBSCRIPTION_CHANGED:
      case NotificationType.SUBSCRIPTION_RENEWED:
        return <SubscriptionsIcon color="primary" />;
      case NotificationType.SUBSCRIPTION_CANCELLED:
        return <SubscriptionsIcon color="error" />;
      case NotificationType.PAYMENT_SUCCEEDED:
        return <PaymentIcon color="success" />;
      case NotificationType.PAYMENT_FAILED:
        return <PaymentIcon color="error" />;
      case NotificationType.SUBSCRIPTION_EXPIRING:
        return <AccessTimeIcon color="warning" />;
      default:
        return <ErrorOutlineIcon />;
    }
  };

  // Fonction pour obtenir le texte selon le type de notification
  const getNotificationText = (notification: Notification) => {
    const data = JSON.parse(notification.data);
    
    switch (notification.type) {
      case NotificationType.SUBSCRIPTION_CREATED:
        return `Votre abonnement ${data.newTier} a été créé avec succès. Montant: ${data.amount} ${data.currency}.`;
      case NotificationType.SUBSCRIPTION_CANCELLED:
        return `Votre abonnement a été annulé. Il expirera le ${new Date(data.expiryDate).toLocaleDateString()}.`;
      case NotificationType.SUBSCRIPTION_CHANGED:
        return `Votre abonnement est passé de ${data.oldTier} à ${data.newTier}. Nouveau montant: ${data.amount} ${data.currency}.`;
      case NotificationType.PAYMENT_SUCCEEDED:
        return `Votre paiement de ${data.amount} ${data.currency} a été traité avec succès.`;
      case NotificationType.PAYMENT_FAILED:
        return `Votre paiement de ${data.amount} ${data.currency} a échoué. Veuillez mettre à jour vos informations de paiement.`;
      case NotificationType.SUBSCRIPTION_EXPIRING:
        return `Votre abonnement expire le ${new Date(data.expiryDate).toLocaleDateString()}. Renouvelez-le pour continuer à bénéficier de tous les avantages.`;
      case NotificationType.SUBSCRIPTION_RENEWED:
        return `Votre abonnement a été renouvelé avec succès.`;
      default:
        return 'Notification sans détails.';
    }
  };

  // Fonction pour formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffMins < 60) {
      return `Il y a ${diffMins} minute${diffMins > 1 ? 's' : ''}`;
    } else if (diffHours < 24) {
      return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else {
      return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    }
  };

  return (
    <>
      <IconButton
        aria-describedby={id}
        onClick={handleClick}
        color="inherit"
        size="large"
      >
        <Badge badgeContent={unreadCount} color="error">
          <NotificationsIcon />
        </Badge>
      </IconButton>
      
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: { width: 360, maxHeight: 500 }
        }}
      >
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Notifications</Typography>
          {unreadCount > 0 && (
            <Button size="small" onClick={handleMarkAllAsRead}>
              Tout marquer comme lu
            </Button>
          )}
        </Box>
        
        <Divider />
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress size={30} />
          </Box>
        ) : notifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Aucune notification
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {notifications.map((notification) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  alignItems="flex-start"
                  sx={{
                    bgcolor: notification.read ? 'inherit' : 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                  secondaryAction={
                    !notification.read && (
                      <IconButton 
                        edge="end" 
                        size="small" 
                        onClick={() => handleMarkAsRead(notification.id)}
                      >
                        <CheckCircleIcon fontSize="small" />
                      </IconButton>
                    )
                  }
                >
                  <Box sx={{ mr: 2, mt: 1 }}>
                    {getNotificationIcon(notification.type)}
                  </Box>
                  <ListItemText
                    primary={getNotificationText(notification)}
                    secondary={formatDate(notification.createdAt)}
                    primaryTypographyProps={{
                      variant: 'body2',
                      color: notification.read ? 'text.primary' : 'text.primary',
                      fontWeight: notification.read ? 'normal' : 'medium'
                    }}
                    secondaryTypographyProps={{
                      variant: 'caption',
                      color: 'text.secondary'
                    }}
                  />
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))}
          </List>
        )}
        
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Button size="small" fullWidth>
            Voir toutes les notifications
          </Button>
        </Box>
      </Popover>
    </>
  );
};

export default NotificationCenter;
