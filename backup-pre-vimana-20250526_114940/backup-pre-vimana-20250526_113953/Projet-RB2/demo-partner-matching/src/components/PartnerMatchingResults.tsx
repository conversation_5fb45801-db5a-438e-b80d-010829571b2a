import React, { useState } from 'react';
import {
  Grid,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Rating,
  Button,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Paper
} from '@mui/material';
import { Partner, PartnerTier } from '../types';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import GroupIcon from '@mui/icons-material/Group';
import EventIcon from '@mui/icons-material/Event';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import LockIcon from '@mui/icons-material/Lock';

interface PartnerMatchingResultsProps {
  matches: Partner[];
  isLoading: boolean;
  selectedTier: PartnerTier;
}

const PartnerMatchingResults: React.FC<PartnerMatchingResultsProps> = ({
  matches,
  isLoading,
  selectedTier
}) => {
  const [selectedPartners, setSelectedPartners] = useState<string[]>([]);

  // Fonctionnalités disponibles selon le niveau d'abonnement
  const tierFeatures = {
    PARTNER: {
      canSeeContactInfo: false,
      canSeeDetailedRatings: false,
      canSeeAvailability: false,
      maxSelections: 1
    },
    CERTIFIED: {
      canSeeContactInfo: true,
      canSeeDetailedRatings: false,
      canSeeAvailability: true,
      maxSelections: 2
    },
    PREMIUM: {
      canSeeContactInfo: true,
      canSeeDetailedRatings: true,
      canSeeAvailability: true,
      maxSelections: 5
    }
  };

  const features = tierFeatures[selectedTier];

  const handleSelectPartner = (partnerId: string) => {
    if (selectedPartners.includes(partnerId)) {
      setSelectedPartners(selectedPartners.filter(id => id !== partnerId));
    } else {
      if (selectedPartners.length < features.maxSelections) {
        setSelectedPartners([...selectedPartners, partnerId]);
      }
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (matches.length === 0) {
    return (
      <Box sx={{ py: 4 }}>
        <Alert severity="info">
          Aucun partenaire ne correspond à vos critères. Essayez d'élargir votre recherche.
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          {matches.length} partenaires trouvés
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Vous pouvez sélectionner jusqu'à {features.maxSelections} partenaires
        </Typography>
      </Box>

      {selectedTier !== 'PREMIUM' && (
        <Alert severity="info" sx={{ mb: 3 }}>
          {selectedTier === 'PARTNER'
            ? "Avec le niveau Partner standard, vous ne pouvez voir que 3 résultats et sélectionner 1 partenaire."
            : "Avec le niveau Certified Partner, vous pouvez voir jusqu'à 5 résultats et sélectionner 2 partenaires."}
        </Alert>
      )}

      <Grid container spacing={3}>
        {matches.map((partner) => (
          <Grid item xs={12} key={partner.id}>
            <Card
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                position: 'relative',
                border: selectedPartners.includes(partner.id) ? '2px solid #4caf50' : 'none'
              }}
            >
              {selectedPartners.includes(partner.id) && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 10,
                    right: 10,
                    bgcolor: '#4caf50',
                    color: 'white',
                    borderRadius: '50%',
                    p: 0.5,
                    zIndex: 1
                  }}
                >
                  <CheckCircleIcon />
                </Box>
              )}

              <CardMedia
                component="img"
                sx={{ width: { xs: '100%', md: 200 }, height: { xs: 200, md: '100%' } }}
                image={partner.avatar}
                alt={partner.name}
              />

              <Box sx={{ display: 'flex', flexDirection: 'column', flex: '1 0 auto' }}>
                <CardContent sx={{ flex: '1 0 auto' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Box>
                      <Typography component="div" variant="h5">
                        {partner.name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Rating value={partner.rating} precision={0.1} readOnly size="small" />
                        <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                          ({partner.reviewCount} avis)
                        </Typography>
                      </Box>
                    </Box>

                    <Chip
                      label={`${partner.matchScore}% compatible`}
                      color={partner.matchScore > 90 ? "success" : partner.matchScore > 75 ? "primary" : "default"}
                      sx={{ fontWeight: 'bold' }}
                    />
                  </Box>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    <Chip
                      icon={<LocationOnIcon />}
                      label={partner.location}
                      size="small"
                      variant="outlined"
                    />
                    <Chip
                      icon={<GroupIcon />}
                      label={`Jusqu'à ${partner.maxCapacity} pers.`}
                      size="small"
                      variant="outlined"
                    />
                    <Chip
                      icon={<AttachMoneyIcon />}
                      label={`${partner.priceRange[0]} - ${partner.priceRange[1]} €`}
                      size="small"
                      variant="outlined"
                    />
                    {features.canSeeAvailability && (
                      <Chip
                        icon={<EventIcon />}
                        label={partner.availability}
                        size="small"
                        variant="outlined"
                      />
                    )}
                  </Box>

                  <Typography variant="body2" color="text.secondary" paragraph>
                    Pourquoi ce partenaire correspond à vos critères:
                  </Typography>

                  <List dense disablePadding>
                    {partner.matchReasons.map((reason, index) => (
                      <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 30 }}>
                          <CheckCircleIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary={reason} />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>

                <Divider />

                <Box sx={{ display: 'flex', alignItems: 'center', px: 2, py: 1 }}>
                  <Button
                    variant={selectedPartners.includes(partner.id) ? "contained" : "outlined"}
                    color={selectedPartners.includes(partner.id) ? "success" : "primary"}
                    onClick={() => handleSelectPartner(partner.id)}
                    disabled={!selectedPartners.includes(partner.id) && selectedPartners.length >= features.maxSelections}
                    sx={{ mr: 2 }}
                  >
                    {selectedPartners.includes(partner.id) ? "Sélectionné" : "Sélectionner"}
                  </Button>

                  <Button
                    variant="outlined"
                    disabled={!features.canSeeContactInfo}
                    startIcon={!features.canSeeContactInfo ? <LockIcon /> : null}
                  >
                    {features.canSeeContactInfo ? "Contacter" : "Contact verrouillé"}
                  </Button>

                  {!features.canSeeContactInfo && (
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                      Niveau supérieur requis
                    </Typography>
                  )}
                </Box>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {selectedTier !== 'PREMIUM' && (
        <Paper sx={{ mt: 4, p: 2, bgcolor: '#f1f8e9' }}>
          <Typography variant="subtitle1" gutterBottom>
            Débloquez plus de fonctionnalités
          </Typography>
          <Typography variant="body2" paragraph>
            Passez au niveau Premium Partner pour:
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="body2">Résultats illimités</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="body2">Sélection de 5 partenaires</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="body2">Évaluations détaillées</Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}
    </Box>
  );
};

export default PartnerMatchingResults;
