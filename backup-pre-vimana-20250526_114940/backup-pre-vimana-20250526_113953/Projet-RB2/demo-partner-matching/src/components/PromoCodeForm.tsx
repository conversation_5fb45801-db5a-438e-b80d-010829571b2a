import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Chip,
  CircularProgress,
  Paper
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PercentIcon from '@mui/icons-material/Percent';
import MoneyOffIcon from '@mui/icons-material/MoneyOff';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';

// Types de réduction
enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED_AMOUNT = 'FIXED_AMOUNT',
  FREE_MONTHS = 'FREE_MONTHS',
}

// Interface pour un code promo validé
interface ValidatedPromoCode {
  code: string;
  description: string;
  discountType: DiscountType;
  discountValue: number;
  validUntil: Date;
}

interface PromoCodeFormProps {
  onApply: (promoCode: ValidatedPromoCode) => void;
  onRemove: () => void;
  appliedCode: ValidatedPromoCode | null;
  disabled?: boolean;
}

const PromoCodeForm: React.FC<PromoCodeFormProps> = ({
  onApply,
  onRemove,
  appliedCode,
  disabled = false
}) => {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<ValidatedPromoCode | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!code.trim()) {
      setError('Veuillez entrer un code promo');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Simuler une validation de code promo
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Pour la démonstration, nous acceptons certains codes prédéfinis
      if (code.toUpperCase() === 'WELCOME20') {
        const promoCode: ValidatedPromoCode = {
          code: 'WELCOME20',
          description: 'Réduction de 20% sur votre premier abonnement',
          discountType: DiscountType.PERCENTAGE,
          discountValue: 20,
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        };
        
        setSuccess(promoCode);
        onApply(promoCode);
        setCode('');
      } else if (code.toUpperCase() === 'PREMIUM50') {
        const promoCode: ValidatedPromoCode = {
          code: 'PREMIUM50',
          description: '50€ de réduction sur l\'abonnement Premium',
          discountType: DiscountType.FIXED_AMOUNT,
          discountValue: 50,
          validUntil: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)
        };
        
        setSuccess(promoCode);
        onApply(promoCode);
        setCode('');
      } else if (code.toUpperCase() === 'FREEMONTH') {
        const promoCode: ValidatedPromoCode = {
          code: 'FREEMONTH',
          description: '1 mois gratuit sur votre abonnement',
          discountType: DiscountType.FREE_MONTHS,
          discountValue: 1,
          validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
        };
        
        setSuccess(promoCode);
        onApply(promoCode);
        setCode('');
      } else {
        setError('Code promo invalide ou expiré');
      }
    } catch (err) {
      setError('Erreur lors de la validation du code promo');
      console.error('Error validating promo code:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRemove = () => {
    setSuccess(null);
    onRemove();
  };

  // Fonction pour obtenir l'icône selon le type de réduction
  const getDiscountIcon = (type: DiscountType) => {
    switch (type) {
      case DiscountType.PERCENTAGE:
        return <PercentIcon fontSize="small" />;
      case DiscountType.FIXED_AMOUNT:
        return <MoneyOffIcon fontSize="small" />;
      case DiscountType.FREE_MONTHS:
        return <CalendarMonthIcon fontSize="small" />;
    }
  };

  // Fonction pour formater la valeur de la réduction
  const formatDiscountValue = (type: DiscountType, value: number) => {
    switch (type) {
      case DiscountType.PERCENTAGE:
        return `${value}%`;
      case DiscountType.FIXED_AMOUNT:
        return `${value}€`;
      case DiscountType.FREE_MONTHS:
        return `${value} mois`;
    }
  };

  return (
    <Box>
      {appliedCode ? (
        <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="subtitle2">
                Code promo appliqué
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Chip
                  icon={getDiscountIcon(appliedCode.discountType)}
                  label={`${appliedCode.code} - ${formatDiscountValue(appliedCode.discountType, appliedCode.discountValue)}`}
                  color="success"
                  variant="outlined"
                  sx={{ mr: 1 }}
                />
                <Typography variant="caption" color="text.secondary">
                  Valide jusqu'au {appliedCode.validUntil.toLocaleDateString()}
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ mt: 1 }}>
                {appliedCode.description}
              </Typography>
            </Box>
            <IconButton size="small" onClick={handleRemove} disabled={disabled}>
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </Paper>
      ) : (
        <Box component="form" onSubmit={handleSubmit} sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Vous avez un code promo ?
          </Typography>
          
          <Box sx={{ display: 'flex' }}>
            <TextField
              size="small"
              placeholder="Entrez votre code"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              disabled={loading || disabled}
              sx={{ mr: 1, flex: 1 }}
            />
            <Button
              variant="outlined"
              type="submit"
              disabled={loading || disabled || !code.trim()}
            >
              {loading ? <CircularProgress size={24} /> : 'Appliquer'}
            </Button>
          </Box>
          
          <Collapse in={!!error}>
            <Alert 
              severity="error" 
              sx={{ mt: 1 }}
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => setError(null)}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
            >
              {error}
            </Alert>
          </Collapse>
          
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Codes de démonstration: WELCOME20, PREMIUM50, FREEMONTH
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default PromoCodeForm;
