const express = require('express');
const Transport = require('../models/Transport');
const router = express.Router();

// Get all transports
router.get('/', async (req, res) => {
  try {
    const transports = await Transport.find();
    res.json(transports);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération des transports', error: error.message });
  }
});

// Search transports
router.post('/search', async (req, res) => {
  try {
    const {
      departure,
      destination,
      date,
      passengers,
      type,
      maxPrice,
      features,
    } = req.body;

    const query = {
      departureLocation: new RegExp(departure, 'i'),
      arrivalLocation: new RegExp(destination, 'i'),
      capacity: { $gte: passengers || 1 },
      available: true,
    };

    if (date) {
      const searchDate = new Date(date);
      const nextDay = new Date(searchDate);
      nextDay.setDate(nextDay.getDate() + 1);
      
      query.departureTime = {
        $gte: searchDate,
        $lt: nextDay,
      };
    }

    if (type) {
      query.type = type;
    }

    if (maxPrice) {
      query.price = { $lte: maxPrice };
    }

    if (features && features.length > 0) {
      query.features = { $all: features };
    }

    const transports = await Transport.find(query)
      .sort({ departureTime: 1, price: 1 });

    res.json(transports);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la recherche', error: error.message });
  }
});

// Get transport by ID
router.get('/:id', async (req, res) => {
  try {
    const transport = await Transport.findById(req.params.id);
    if (!transport) {
      return res.status(404).json({ message: 'Transport non trouvé' });
    }
    res.json(transport);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération du transport', error: error.message });
  }
});

// Add review to transport
router.post('/:id/reviews', async (req, res) => {
  try {
    const { rating, comment, userId } = req.body;
    const transport = await Transport.findById(req.params.id);

    if (!transport) {
      return res.status(404).json({ message: 'Transport non trouvé' });
    }

    transport.reviews.push({
      user: userId,
      rating,
      comment,
    });

    // Update average rating
    const totalRating = transport.reviews.reduce((sum, review) => sum + review.rating, 0);
    transport.rating = totalRating / transport.reviews.length;

    await transport.save();
    res.json(transport);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de l\'ajout de l\'avis', error: error.message });
  }
});

module.exports = router;
