import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Grid, Card, CardContent, Button } from '@mui/material';
import { useAuth } from '../contexts/AuthContext.js';

const ReservationsPage = () => {
  const [reservations, setReservations] = useState([]);
  const { user } = useAuth();

  useEffect(() => {
    const fetchReservations = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/reservations', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });
        const data = await response.json();
        setReservations(data);
      } catch (error) {
        console.error('Error fetching reservations:', error);
      }
    };

    fetchReservations();
  }, []);

  const handleCancelReservation = async (reservationId) => {
    try {
      await fetch(`http://localhost:3001/api/reservations/${reservationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bear<PERSON> ${localStorage.getItem('token')}`
        }
      });
      setReservations(reservations.filter(res => res.id !== reservationId));
    } catch (error) {
      console.error('Error canceling reservation:', error);
    }
  };

  return (
    <Container>
      <Box sx={{ mt: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          My Reservations
        </Typography>
        <Grid container spacing={2}>
          {reservations.map((reservation) => (
            <Grid item xs={12} sm={6} md={4} key={reservation.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" component="h2">
                    {reservation.transport.departure} → {reservation.transport.destination}
                  </Typography>
                  <Typography color="textSecondary">
                    Date: {new Date(reservation.transport.date).toLocaleDateString()}
                  </Typography>
                  <Typography>
                    Price: ${reservation.transport.price}
                  </Typography>
                  <Typography>
                    Status: {reservation.status}
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Button
                      variant="contained"
                      color="error"
                      onClick={() => handleCancelReservation(reservation.id)}
                    >
                      Cancel Reservation
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Container>
  );
};

export default ReservationsPage;
