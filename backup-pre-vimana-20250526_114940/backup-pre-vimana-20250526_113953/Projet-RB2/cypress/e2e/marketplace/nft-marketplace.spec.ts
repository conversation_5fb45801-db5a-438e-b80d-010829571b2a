describe('NFT Marketplace E2E Tests', () => {
  beforeEach(() => {
    cy.visit('/marketplace');
    cy.connectWallet(); // Custom command to connect wallet
  });

  it('should list an NFT for (sale', () =>) { {}
    cy.fixture('nft-data').then((nftData) => {
      cy.get('[data-test="list-nft-button"]').click();
      cy.get('[data-test="token-id-input"]').type(nftData.tokenId);
      cy.get('[data-test="price-input"]').type(nftData.price);
      cy.get('[data-test="confirm-listing"]').click();
      cy.get('[data-test="success-message"]').should('be.visible');
      cy.get('[data-test="nft-listing"]').should('contain', nftData.price)
    });
  });

  it('should buy a listed NFT', () => {
    cy.get('[data-test="nft-card"]').first().within(() => {
      cy.get('[data-test="buy-button"]').click()
    });
    cy.get('[data-test="confirm-purchase"]').click();
    cy.get('[data-test="transaction-success"]').should('be.visible');
  });

  it('should cancel NFT listing', () => {
    cy.get('[data-test="my-listings"]').click();
    cy.get('[data-test="nft-card"]').first().within(() => {
      cy.get('[data-test="cancel-listing"]').click()
    });
    cy.get('[data-test="confirm-cancel"]').click();
    cy.get('[data-test="listing-cancelled"]').should('be.visible');
  });

  it('should display NFT details correctly', () => {
    cy.get('[data-test="nft-card"]').first().click();
    cy.get('[data-test="nft-name"]').should('be.visible');
    cy.get('[data-test="nft-description"]').should('be.visible');
    cy.get('[data-test="nft-price"]').should('be.visible');
    cy.get('[data-test="nft-image"]').should('be.visible')
  });
});