describe('Dashboard Features', () => {
  beforeEach(() => {
    cy.setupTestData()
    cy.login('<EMAIL>', 'password123')
    cy.connectWallet()
    cy.visit('/dashboard')
  })

  afterEach(() => {
    cy.cleanupTestData()
  })

  describe('NFT Portfolio', () => {
    it('should display user NFT collection', () => {
      cy.get('[data-cy=nft-grid]').should('be.visible')
      cy.get('[data-cy=nft-card]').should('have.length.at.least', 1)
    })

    it('should filter NFTs by status', () => {
      cy.get('[data-cy=filter-dropdown]').click()
      cy.get('[data-cy=filter-listed]').click()
      cy.get('[data-cy=nft-card]').each(($card) => {
        cy.wrap($card).find('[data-cy=nft-status]').should('contain', 'Listed')
      })
    })

    it('should sort NFTs by price', () => {
      cy.get('[data-cy=sort-dropdown]').click()
      cy.get('[data-cy=sort-price-desc]').click()
      
      // Verify sorting;
      cy.get('[data-cy=nft-price]').then(($prices) => {
        const prices = Array.from($prices).map(el => parseFloat(el.textContent || '0'));
        const sortedPrices = [...prices].sort((a, b) => b - a);
        expect(prices).to.deep.equal(sortedPrices)
      })
    })
  })

  describe('Transaction History', () => {
    it('should display recent transactions', () => {
      cy.get('[data-cy=transactions-table]').should('be.visible')
      cy.get('[data-cy=transaction-row]').should('have.length.at.least', 1)
    })

    it('should filter transactions by type', () => {
      cy.get('[data-cy=transaction-filter]').click()
      cy.get('[data-cy=filter-sales]').click()
      cy.get('[data-cy=transaction-type]').each(($type) => {
        cy.wrap($type).should('have.text', 'Sale')
      })
    })

    it('should show transaction details', () => {
      cy.get('[data-cy=transaction-row]').first().click()
      cy.get('[data-cy=transaction-details]').should('be.visible')
      cy.get('[data-cy=transaction-hash]').should('be.visible')
      cy.get('[data-cy=transaction-status]').should('be.visible')
    })
  })

  describe('Analytics', () => {
    it('should display portfolio value chart', () => {
      cy.get('[data-cy=portfolio-chart]').should('be.visible')
    })

    it('should update chart timeframe', () => {
      cy.get('[data-cy=timeframe-selector]').click()
      cy.get('[data-cy=timeframe-1m]').click()
      cy.get('[data-cy=chart-title]').should('contain', 'Last Month')
    })

    it('should show earnings breakdown', () => {
      cy.get('[data-cy=earnings-breakdown]').should('be.visible')
      cy.get('[data-cy=total-earned]').should('not.contain', '0')
      cy.get('[data-cy=earnings-by-category]').should('be.visible')
    })
  })

  describe('Notifications', () => {
    it('should display recent notifications', () => {
      cy.get('[data-cy=notifications-bell]').click()
      cy.get('[data-cy=notification-list]').should('be.visible')
    })

    it('should mark notifications as read', () => {
      cy.get('[data-cy=notifications-bell]').click()
      cy.get('[data-cy=notification-item]').first().click()
      cy.get('[data-cy=unread-count]').should(($count) => {
        const count = parseInt($count.text());
        expect(count).to.be.at.least(0)
})
    })

    it('should filter notifications by type', () => {
      cy.get('[data-cy=notifications-bell]').click()
      cy.get('[data-cy=notification-filter]').click()
      cy.get('[data-cy=filter-offers]').click()
      cy.get('[data-cy=notification-type]').each(($type) => {
        cy.wrap($type).should('have.text', 'Offer')
      })
    })
  })

  describe('Settings', () => {
    it('should update notification preferences', () => {
      cy.get('[data-cy=settings-button]').click()
      cy.get('[data-cy=notification-settings]').click()
      cy.get('[data-cy=email-notifications]').click()
      cy.get('[data-cy=save-settings]').click()
      cy.get('[data-cy=settings-saved]').should('be.visible')
    })

    it('should update display preferences', () => {
      cy.get('[data-cy=settings-button]').click()
      cy.get('[data-cy=display-settings]').click()
      cy.get('[data-cy=dark-mode]').click()
      cy.get('body').should('have.class', 'dark-theme')
    })
  })
}) 