// Mock pour aws-sdk
class S3Mock {
  constructor() {
    this.uploadResult = {
      promise: jest.fn().mockResolvedValue({ 
        Location: 'https://s3.mock.com/bucket/key',
        ETag: 'mock-etag'
      })
    };
    this.deleteResult = {
      promise: jest.fn().mockResolvedValue({})
    };
    this.listResult = {
      promise: jest.fn().mockResolvedValue({
        Contents: [
          { Key: 'test1.jpg' },
          { Key: 'test2.png' }
        ]
      })
    };
    this.getSignedUrlResult = 'https://s3.mock.com/bucket/signed-url';
  }

  upload() {
    return this.uploadResult;
  }

  deleteObject() {
    return this.deleteResult;
  }

  listObjects() {
    return this.listResult;
  }

  getSignedUrl(operation, params) {
    return this.getSignedUrlResult;
  }
}

const aws = {
  S3: S3Mock,
  config: {
    update: jest.fn()
  }
};

module.exports = aws;
module.exports.default = module.exports; 