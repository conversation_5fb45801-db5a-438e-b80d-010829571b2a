// Mock pour bullmq
class Queue {
  constructor(name, options = {}) {
    this.name = name;
    this.options = options;
    this.jobs = new Map();
    this.closed = false;
  }

  async add(name, data, options = {}) {
    const id = `${name}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    const job = {
      id,
      name,
      data,
      opts: options,
      timestamp: Date.now(),
      progress: 0,
      returnvalue: null,
      stacktrace: [],
      logs: []
    };
    this.jobs.set(id, job);
    return job;
  }

  async getJob(jobId) {
    return this.jobs.get(jobId) || null;
  }

  async pause() {
    this.paused = true;
    return;
  }

  async resume() {
    this.paused = false;
    return;
  }

  async clean(grace, limit, status) {
    // Nettoie les jobs selon le statut et l'âge
    return [];
  }

  async getJobCounts() {
    return {
      active: 0,
      completed: 0,
      failed: 0,
      delayed: 0,
      waiting: 0
    };
  }

  async close() {
    this.closed = true;
    return;
  }

  get client() {
    return Promise.resolve({
      ping: async () => 'PONG'
    });
  }
}

class Worker {
  constructor(queueName, processor, options = {}) {
    this.queueName = queueName;
    this.processor = processor;
    this.options = options;
    this.closed = false;
  }

  async close() {
    this.closed = true;
    return;
  }

  on(event, callback) {
    // Gérer les événements
    return this;
  }

  async pause() {
    this.paused = true;
    return;
  }

  async resume() {
    this.paused = false;
    return;
  }
}

class QueueEvents {
  constructor(queueName, options = {}) {
    this.queueName = queueName;
    this.options = options;
    this.closed = false;
  }

  async close() {
    this.closed = true;
    return;
  }

  on(event, callback) {
    // Gérer les événements
    return this;
  }
}

class FlowProducer {
  constructor(options = {}) {
    this.options = options;
    this.closed = false;
  }

  async add(flow) {
    // Ajouter un flux de jobs
    return { id: 'mock-flow-id', jobs: {} };
  }

  async close() {
    this.closed = true;
    return;
  }
}

// Exportations du module bullmq
module.exports = {
  Queue,
  Worker,
  QueueEvents,
  FlowProducer
};

module.exports.default = module.exports; 