import * as webVitals from "web-vitals";
import { Analytics, analytics } from "./analytics";
import { MonitoringConfig } from "./types";

// Définition des interfaces manquantes
interface Metric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string
}

interface MetricData {
  id: string;
  name: string;
  description: string;
  category: string;
  currentValue: number;
  unit: string;
  trend: number
}

interface WebVitalsMetric extends Metric {
  navigationType?: string;
  attribution?: {
    element?: string;
    sources?: Array<{
      node?: string;
      value?: number
    }>;
  }
  timestamp?: number;
}

export class WebVitalsService {
  private static instance: WebVitalsService;
  private analytics: Analytics;
  private metricsHistory: Map<string, Array<WebVitalsMetric>> = new Map();
  private config: MonitoringConfig;

  private constructor() {
    this.analytics = analytics;
    // Configuration par défaut
    this.config = {
      webVitals: {
        LCP: { good: 2500, poor: 4000 },
        FID: { good: 100, poor: 300 },
        CLS: { good: 0.1, poor: 0.25 },
        FCP: { good: 1800, poor: 3000 },
        TTFB: { good: 800, poor: 1800 }
      },
      debug: {
        enabled: false,
        consoleEvents: true,
        logLevel: 'warn'
      },
      sentry: {
        tracesSampleRate: 0.1,
        replaysSessionSampleRate: 0.1,
        replaysOnErrorSampleRate: 1.0,
        maxBreadcrumbs: 50,
        environment: 'production'
      },
      customMetrics: {
        apiResponseTime: { good: 300, poor: 1000 },
        renderTime: { good: 50, poor: 200 }
      },
      analytics: {
        sampling: {
          performance: 0.1,
          error: 1.0,
          interaction: 0.5
        },
        batchSize: 10,
        rateLimit: 100,
        endpoint: '/api/analytics',
        retryAttempts: 3,
        retryDelay: 1000,
        batchTimeout: 5000
      },
      sampling: {
        webVitals: 0.1,
        errors: 1.0,
        userEvents: 0.5
      },
      alerts: {
        enabled: true,
        channels: [],
        throttling: 300000 // 5 minutes
      },
      reporting: {
        autoSchedule: true,
        retention: 90, // jours
        aggregation: 'daily'
      },
      logging: {
        level: 'warning',
        console: true,
        remote: true
      }
    }
  }

  public static getInstance(): WebVitalsService {
    if (!WebVitalsService.instance) {
      WebVitalsService.instance = new WebVitalsService()
    }
    return WebVitalsService.instance;
  }

  public async getMetricsHistory(timeRange: '1d' | '7d' | '30d' | '90d'): Promise<MetricData[]> {
    const now = Date.now();
    const ranges = {
      '1d': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000
    }
    const metrics: MetricData[] = [];
    const rangeMs = ranges[timeRange];
    const startTime = now - rangeMs;

    // Convert stored metrics to MetricData format
    for (const [name, values] of this.metricsHistory.entries()) {
      const recentValues = values.filter(v => v.timestamp && v.timestamp > startTime);
      if (recentValues.length > 0) {
        const currentValue = recentValues[recentValues.length - 1].value;
        const previousValue = recentValues[0].value;
        
        metrics.push({
          id: `webvital-${name}`,
          name,
          description: `Web Vital metric: ${name}`,
          category: 'Web Vitals',
          currentValue,
          unit: name === 'CLS' ? 'score' : 'ms',
          trend: ((currentValue - previousValue) / previousValue) * 100
        });
      }
    }

    return metrics;
  }

  public initializeMonitoring(): void {
    if (process.env.NODE_ENV === 'production') {
      this.reportWebVitals(this.handleWebVitalsReport.bind(this))
    }
  }

  private reportWebVitals(onPerfEntry: (metric: webVitals.Metric) => void): void {
    if (typeof webVitals.onCLS === 'function') {
      webVitals.onCLS(onPerfEntry);
      webVitals.onFID(onPerfEntry);
      webVitals.onFCP(onPerfEntry);
      webVitals.onLCP(onPerfEntry);
      webVitals.onTTFB(onPerfEntry)
    }
  }

  private handleWebVitalsReport(metric: webVitals.Metric): void {
    // Enrich metric data with additional context
    const enrichedMetric: WebVitalsMetric = {
      ...metric,
      navigationType: performance?.navigation?.type?.toString(),
      timestamp: Date.now()
    }

    // Store for historical analysis
    if (!this.metricsHistory.has(metric.name)) {
      this.metricsHistory.set(metric.name, [])
    }
    this.metricsHistory.get(metric.name)?.push(enrichedMetric);

    // Track the metric using our analytics service
    this.analytics.trackEvent('web_vitals', {
      metric_name: metric.name,
      metric_value: metric.value,
      metric_rating: metric.rating,
      timestamp: enrichedMetric.timestamp,
      path: window.location.pathname
    });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Web Vitals:', enrichedMetric)
    }
  }

  public trackCustomPerformanceMetric(name: string, value: number): void {
    this.analytics.trackEvent('custom_performance', {
      metric_name: name,
      metric_value: value,
      timestamp: Date.now(),
      path: window.location.pathname
    });
  }

  /**
   * Récupère la configuration actuelle du service;
   */
  public getConfig(): MonitoringConfig {
    return this.config;
  }

  /**
   * Met à jour la configuration du service;
   * @param newConfig Nouvelle configuration;
   */
  public updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('WebVitals configuration updated:', this.config);
  }
}
