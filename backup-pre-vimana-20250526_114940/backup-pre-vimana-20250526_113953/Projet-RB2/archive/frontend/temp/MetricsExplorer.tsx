import React, { useState, useEffect } from "react";
import { WebVitalsService } from "./WebVitalsService";
import { CustomAnalyticsService } from "./CustomAnalyticsService";

// Ajout des interfaces manquantes
interface MetricData {
  id: string;
  name: string;
  description: string;
  category: string;
  currentValue: number;
  unit: string;
  trend: number;
}

type TimeRange = '1d' | '7d' | '30d' | '90d';

interface MetricsExplorerProps {
  onMetricSelect?: (metricName: string) => void;
}

// Stubs pour les services avec des méthodes temporaires
// Ces adaptateurs permettent de faire face au manque de définition des méthodes
class WebVitalsServiceAdapter {
  private service: WebVitalsService;

  constructor(service: WebVitalsService) {
    this.service = service;
  }

  public async getMetricsHistory(timeRange: TimeRange): Promise<MetricData[]> {
    // Simuler la récupération des données, implémentation temporaire
    console.log('Récupération des Web Vitals pour:', timeRange);
    return []; // Retourne un tableau vide comme donnée temporaire
  }
}

class CustomAnalyticsServiceAdapter {
  private service: any; // Type 'any' temporaire

  constructor(service: any) {
    this.service = service;
  }

  public async getCustomMetrics(): Promise<MetricData[]> {
    // Simuler la récupération des données, implémentation temporaire
    console.log('Récupération des métriques personnalisées');
    return []; // Retourne un tableau vide comme donnée temporaire
  }
}

const MetricsExplorer: React.FC<MetricsExplorerProps> = ({ onMetricSelect }) => {
  const [metrics, setMetrics] = useState<MetricData[]>([]);
  const [filteredMetrics, setFilteredMetrics] = useState<MetricData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [timeRange, setTimeRange] = useState<TimeRange>('7d');
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);

  // Utiliser les adaptateurs pour les services
  const webVitalsService = WebVitalsService.getInstance();
  const webVitalsAdapter = new WebVitalsServiceAdapter(webVitalsService);
  const analyticsService = CustomAnalyticsService.getInstance();
  const analyticsAdapter = new CustomAnalyticsServiceAdapter(analyticsService);

  useEffect(() => {
    const fetchMetrics = async (): Promise<void> => {
      try {
        setLoading(true);

        // Récupérer les métriques Web Vitals via l'adaptateur
        const webVitalsMetrics = await webVitalsService.getMetricsHistory(timeRange);

        // Récupérer les métriques personnalisées via l'adaptateur
        const customMetrics = await analyticsAdapter.getCustomMetrics();

        // Combiner toutes les métriques
        const allMetrics = [...webVitalsMetrics, ...customMetrics];

        setMetrics(allMetrics);
        setFilteredMetrics(allMetrics);
        setError(null);
      } catch (err) {
        setError(
          'Erreur lors du chargement des métriques: ' +
            (err instanceof Error ? err.message : String(err))
        );
      } finally {
        setLoading(false);
      }
    };

    fetchMetrics();
  }, [timeRange]);

  useEffect(() => {
    // Filtrer les métriques en fonction de la recherche et de la catégorie
    let filtered = metrics;

    if (searchTerm) {
      filtered = filtered.filter(
        (metric: MetricData) =>
          metric.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          metric.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter((metric: MetricData) => metric.category === selectedCategory);
    }

    setFilteredMetrics(filtered);
  }, [searchTerm, selectedCategory, metrics]);

  const handleMetricSelect = (metricName: string) => {
    setSelectedMetric(metricName);
    if (onMetricSelect) {
      onMetricSelect(metricName);
    }
  };

  const getUniqueCategories = () => {
    const categories = new Set(metrics.map(metric => metric.category));
    return ['all', ...Array.from(categories)];
  };

  return (
    <div className="metrics-explorer p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Explorateur de Métriques</h2>

      <div className="filters mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Recherche</label>
            <input
              type="text"
              className="border rounded px-3 py-2 w-full"
              placeholder="Rechercher une métrique..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Catégorie</label>
            <select
              className="border rounded px-3 py-2 w-full"
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
            >
              {getUniqueCategories().map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'Toutes les catégories' : category}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Période</label>
            <select
              className="border rounded px-3 py-2 w-full"
              value={timeRange}
              onChange={e => setTimeRange(e.target.value as TimeRange)}
            >
              <option value="1d">Dernières 24 heures</option>
              <option value="7d">7 derniers jours</option>
              <option value="30d">30 derniers jours</option>
              <option value="90d">90 derniers jours</option>
            </select>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          <p className="mt-2 text-gray-600">Chargement des métriques...</p>
        </div>
      ) : error ? (
        <div className="bg-red-100 text-red-700 p-4 rounded-lg">{error}</div>
      ) : filteredMetrics.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          Aucune métrique ne correspond à vos critères de recherche.
        </div>
      ) : (
        <div className="metrics-list">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2">Nom</th>
                <th className="text-left py-2">Catégorie</th>
                <th className="text-left py-2">Dernière valeur</th>
                <th className="text-left py-2">Tendance</th>
                <th className="text-left py-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredMetrics.map(metric => (
                <tr
                  key={metric.id}
                  className={`border-b hover:bg-gray-50 ${selectedMetric === metric.name ? 'bg-blue-50' : ''}`}
                >
                  <td className="py-3">
                    <div className="font-medium">{metric.name}</div>
                    <div className="text-sm text-gray-500">{metric.description}</div>
                  </td>
                  <td className="py-3">
                    <span className="px-2 py-1 bg-gray-100 rounded text-xs">{metric.category}</span>
                  </td>
                  <td className="py-3">
                    {metric.currentValue} {metric.unit}
                  </td>
                  <td className="py-3">
                    {metric.trend > 0 ? (
                      <span className="text-green-500 flex items-center">
                        <span className="material-icons text-xs mr-1">arrow_upward</span>
                        {metric.trend}%
                      </span>
                    ) : metric.trend < 0 ? (
                      <span className="text-red-500 flex items-center">
                        <span className="material-icons text-xs mr-1">arrow_downward</span>
                        {Math.abs(metric.trend)}%
                      </span>
                    ) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </td>
                  <td className="py-3">
                    <button
                      onClick={() => handleMetricSelect(metric.name)}
                      className="text-blue-500 hover:text-blue-700 mr-2"
                    >
                      <span className="material-icons text-sm">visibility</span>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default MetricsExplorer;
