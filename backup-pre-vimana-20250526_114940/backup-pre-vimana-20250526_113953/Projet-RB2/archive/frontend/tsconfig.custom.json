{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src/index.tsx", "src/App.tsx", "src/AppWithChatBot.tsx", "src/routes.tsx", "src/components/common/LoadingFallback.tsx", "src/components/layouts/MainLayout.tsx", "src/components/navigation/Navbar.tsx", "src/components/navigation/Footer.tsx"]}