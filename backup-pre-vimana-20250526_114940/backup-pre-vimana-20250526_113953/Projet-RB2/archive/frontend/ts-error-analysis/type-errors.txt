src/atomic/docs/examples/ContactForm.stories.tsx(227,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(157,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(168,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(176,11): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(190,17): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(236,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(247,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(262,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(279,17): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(285,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(298,17): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityNotifications.tsx(306,48): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityRules.tsx(139,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityRules.tsx(174,15): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityRules.tsx(193,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityRules.tsx(291,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityRules.tsx(309,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityRules.tsx(314,21): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityRules.tsx(340,19): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SecurityRules.tsx(354,19): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SessionManager.tsx(170,16): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SessionManager.tsx(221,7): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SessionManager.tsx(231,19): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SessionManager.tsx(252,29): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SessionManager.tsx(259,27): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SessionManager.tsx(295,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SessionManager.tsx(339,17): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SessionManager.tsx(366,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SessionManager.tsx(384,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(292,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(311,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(325,19): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(333,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(347,19): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(353,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(379,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(393,19): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(400,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(453,15): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SmartAlerts.tsx(498,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SystemHealth.tsx(150,3): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SystemHealth.tsx(155,11): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SystemHealth.tsx(186,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SystemHealth.tsx(254,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SystemHealth.tsx(267,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SystemHealth.tsx(279,11): error TS2657: JSX expressions must have one parent element.
src/components/Admin/SystemHealth.tsx(288,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(177,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(193,19): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(213,21): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(226,15): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(242,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(250,17): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(270,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(288,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(295,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(306,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(317,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(339,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/TwoFactorAuth.tsx(384,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserDialog.tsx(43,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserDialog.tsx(78,7): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(80,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(114,15): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(117,13): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(135,9): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(247,5): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(257,7): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(272,15): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(285,17): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(306,17): error TS2657: JSX expressions must have one parent element.
src/components/Admin/UserManagement.tsx(313,19): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateBadges.tsx(35,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateBadges.tsx(74,11): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateBadges.tsx(80,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateBadges.tsx(111,13): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateBadges.tsx(117,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateBadges.tsx(123,21): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateBadges.tsx(134,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateBadges.tsx(139,19): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateChallenges.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateChallenges.tsx(75,11): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateChallenges.tsx(88,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateChallenges.tsx(103,13): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateExclusiveRewards.tsx(50,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateExclusiveRewards.tsx(82,13): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateExclusiveRewards.tsx(105,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateLeaderboard.tsx(31,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateLeaderboard.tsx(68,11): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateLeaderboard.tsx(80,15): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateList.tsx(31,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateList.tsx(35,11): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateNotifications.tsx(55,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateNotifications.tsx(64,9): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateNotifications.tsx(95,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(110,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(115,11): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(125,7): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(131,9): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(135,9): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(139,9): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(145,7): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(152,9): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(191,7): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(225,13): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(236,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliatePoints.tsx(241,15): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateProgressCharts.tsx(46,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateProgressCharts.tsx(76,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateProgressCharts.tsx(98,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateProgressCharts.tsx(135,15): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(47,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(56,11): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(60,11): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(67,7): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(76,7): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(80,13): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(88,15): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(94,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(100,15): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(106,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(112,19): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(124,7): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(155,19): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(164,19): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(168,19): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(174,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(185,7): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(192,11): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateREferralProgram.tsx(196,11): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateRegistration.tsx(42,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateRegistration.tsx(61,7): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateSeasonalEvents.tsx(39,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateSeasonalEvents.tsx(66,13): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateSeasonalEvents.tsx(79,15): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateSeasonalEvents.tsx(90,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateSeasonalEvents.tsx(96,17): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateSeasonalEvents.tsx(105,23): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateSeasonalEvents.tsx(114,19): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateSeasonalEvents.tsx(122,23): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateSeasonalEvents.tsx(126,23): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateStats.tsx(33,5): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateStats.tsx(41,9): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateStats.tsx(45,9): error TS2657: JSX expressions must have one parent element.
src/components/affiliate/AffiliateStats.tsx(49,9): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(214,11): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(261,13): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(267,15): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(273,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(279,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(303,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(340,21): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(355,23): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(380,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(424,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(434,21): error TS2657: JSX expressions must have one parent element.
src/components/ai/AIAssistant.tsx(454,23): error TS2657: JSX expressions must have one parent element.
src/components/ai/MatchingResults.tsx(13,5): error TS2657: JSX expressions must have one parent element.
src/components/ai/MatchingResults.tsx(21,11): error TS2657: JSX expressions must have one parent element.
src/components/ai/MatchingResults.tsx(37,19): error TS2657: JSX expressions must have one parent element.
src/components/ai/MatchingResults.tsx(46,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/MatchingResults.tsx(52,19): error TS2657: JSX expressions must have one parent element.
src/components/ai/MatchingResults.tsx(56,19): error TS2657: JSX expressions must have one parent element.
src/components/ai/MatchingResults.tsx(60,19): error TS2657: JSX expressions must have one parent element.
src/components/ai/MatchingResults.tsx(66,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/MatchingResults.tsx(76,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(26,5): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(34,11): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(37,15): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(54,19): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(77,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(100,17): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(119,13): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(132,15): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(144,15): error TS2657: JSX expressions must have one parent element.
src/components/ai/PartnerMatchingAI.tsx(158,13): error TS2657: JSX expressions must have one parent element.
src/components/AIAgentManager.tsx(22,5): error TS2657: JSX expressions must have one parent element.
src/components/AIAgentManager.tsx(39,9): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(85,7): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(99,5): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(102,9): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(109,15): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(126,15): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(148,11): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(152,13): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(177,13): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(232,11): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(245,11): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(251,17): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(255,19): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(285,27): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(298,17): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(311,11): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(317,17): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(331,11): error TS2657: JSX expressions must have one parent element.
src/components/analysis/AnalysisResultsCard.tsx(337,17): error TS2657: JSX expressions must have one parent element.
src/components/analytics/AnalyticsChart.tsx(14,5): error TS2657: JSX expressions must have one parent element.
src/components/analytics/AnalyticsChart.tsx(19,7): error TS2657: JSX expressions must have one parent element.
src/components/analytics/BookingTrendsChart.tsx(73,5): error TS2657: JSX expressions must have one parent element.
src/components/analytics/BookingTrendsChart.tsx(77,7): error TS2657: JSX expressions must have one parent element.
src/components/analytics/OrganizerDashboard.tsx(119,5): error TS2657: JSX expressions must have one parent element.
src/components/analytics/OrganizerDashboard.tsx(125,11): error TS2657: JSX expressions must have one parent element.
src/components/analytics/PartnerAnalytics.tsx(33,5): error TS2657: JSX expressions must have one parent element.
src/components/analytics/PartnerAnalytics.tsx(41,11): error TS2657: JSX expressions must have one parent element.
src/components/analytics/PartnerAnalytics.tsx(62,7): error TS2657: JSX expressions must have one parent element.
src/components/analytics/PartnerAnalytics.tsx(66,9): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RetreatAnalytics.tsx(56,11): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RetreatAnalytics.tsx(61,15): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RetreatAnalytics.tsx(81,11): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(176,5): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(192,9): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(204,9): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(236,7): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(244,7): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(272,5): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(282,7): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(298,11): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(310,19): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(318,19): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(330,7): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(364,9): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(381,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(395,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(409,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(415,9): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(432,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(446,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(460,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(466,9): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(483,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(497,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(511,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(517,9): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(534,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(548,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(562,13): error TS2657: JSX expressions must have one parent element.
src/components/analytics/RoadmapKPIDashboard.tsx(576,13): error TS2657: JSX expressions must have one parent element.
src/components/analyzer/AnalyzerDashboard.tsx(4,3): error TS2657: JSX expressions must have one parent element.
src/components/atomic/molecules/FormField/FormField.tsx(59,5): error TS2657: JSX expressions must have one parent element.
src/components/atoms/Button/AccessibleButton.tsx(51,11): error TS2657: JSX expressions must have one parent element.
src/components/atoms/Button/Button.stories.tsx(109,5): error TS2657: JSX expressions must have one parent element.
src/components/atoms/Button/Button.stories.tsx(119,5): error TS2657: JSX expressions must have one parent element.
src/components/atoms/Form/AccessibleInput.tsx(20,7): error TS2657: JSX expressions must have one parent element.
src/components/atoms/Icon/Icon.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/components/atoms/Input.tsx(40,7): error TS2657: JSX expressions must have one parent element.
src/components/atoms/LoadingSpinner.tsx(13,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/AuthButtons.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/AuthButtons.tsx(14,7): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(50,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(65,9): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(69,17): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(73,19): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(90,17): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(94,19): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(101,15): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(112,13): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(122,11): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(130,17): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(153,11): error TS2657: JSX expressions must have one parent element.
src/components/auth/LoginPage.tsx(159,13): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterForm.tsx(160,13): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterForm.tsx(191,13): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterForm.tsx(240,11): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterForm.tsx(244,15): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterForm.tsx(270,7): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterPage.tsx(42,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterPage.tsx(49,9): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterPage.tsx(64,13): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterPage.tsx(77,13): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterPage.tsx(90,13): error TS2657: JSX expressions must have one parent element.
src/components/auth/RegisterPage.tsx(100,11): error TS2657: JSX expressions must have one parent element.
src/components/auth/RequestPasswordResetForm.tsx(27,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/RequestPasswordResetForm.tsx(46,9): error TS2657: JSX expressions must have one parent element.
src/components/auth/RequestPasswordResetForm.tsx(57,11): error TS2657: JSX expressions must have one parent element.
src/components/auth/ResetPasswordForm.tsx(42,7): error TS2657: JSX expressions must have one parent element.
src/components/auth/ResetPasswordForm.tsx(51,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/ResetPasswordForm.tsx(68,9): error TS2657: JSX expressions must have one parent element.
src/components/auth/TwoFactorVerification.tsx(89,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/TwoFactorVerification.tsx(113,9): error TS2657: JSX expressions must have one parent element.
src/components/auth/TwoFactorVerification.tsx(138,13): error TS2657: JSX expressions must have one parent element.
src/components/auth/UnauthorizedPage.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/UnauthorizedPage.tsx(14,9): error TS2657: JSX expressions must have one parent element.
src/components/auth/Verify2FAForm.tsx(53,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/Verify2FAForm.tsx(65,11): error TS2657: JSX expressions must have one parent element.
src/components/auth/Verify2FAForm.tsx(71,9): error TS2657: JSX expressions must have one parent element.
src/components/auth/Verify2FAForm.tsx(103,17): error TS2657: JSX expressions must have one parent element.
src/components/auth/VerifyEmailForm.tsx(39,5): error TS2657: JSX expressions must have one parent element.
src/components/auth/VerifyEmailForm.tsx(52,11): error TS2657: JSX expressions must have one parent element.
src/components/BlogHighlights.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/BlogHighlights.tsx(49,13): error TS2657: JSX expressions must have one parent element.
src/components/BlogHighlights.tsx(60,15): error TS2657: JSX expressions must have one parent element.
src/components/BlogHighlights.tsx(69,17): error TS2657: JSX expressions must have one parent element.
src/components/BlogHighlights.tsx(88,9): error TS2657: JSX expressions must have one parent element.
src/components/booking/RetreatBooking.tsx(112,5): error TS2657: JSX expressions must have one parent element.
src/components/booking/RetreatBooking.tsx(153,9): error TS2657: JSX expressions must have one parent element.
src/components/booking/RetreatBooking.tsx(181,15): error TS2657: JSX expressions must have one parent element.
src/components/booking/RetreatBooking.tsx(194,11): error TS2657: JSX expressions must have one parent element.
src/components/booking/SecureBookingForm.tsx(37,5): error TS2657: JSX expressions must have one parent element.
src/components/booking/SecureBookingForm.tsx(70,7): error TS2657: JSX expressions must have one parent element.
src/components/booking/SecureBookingForm.tsx(85,7): error TS2657: JSX expressions must have one parent element.
src/components/booking/SecureBookingForm.tsx(91,13): error TS2657: JSX expressions must have one parent element.
src/components/booking/SecureBookingForm.tsx(102,7): error TS2657: JSX expressions must have one parent element.
src/components/booking/SecureBookingForm.tsx(115,7): error TS2657: JSX expressions must have one parent element.
src/components/booking/SecureBookingForm.tsx(126,7): error TS2657: JSX expressions must have one parent element.
src/components/booking/SecureBookingForm.tsx(142,7): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(125,15): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(148,19): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(159,19): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(173,11): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(179,15): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(212,11): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(218,15): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(230,13): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(237,19): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(248,19): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(263,11): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(289,5): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(293,11): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(306,11): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(328,7): error TS2657: JSX expressions must have one parent element.
src/components/booking/SmartBooking.tsx(335,9): error TS2657: JSX expressions must have one parent element.
src/components/BookingList.tsx(54,5): error TS2657: JSX expressions must have one parent element.
src/components/BookingList.tsx(62,13): error TS2657: JSX expressions must have one parent element.
src/components/BookingList.tsx(81,19): error TS2657: JSX expressions must have one parent element.
src/components/BookingList.tsx(88,17): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventCalendar.tsx(182,15): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventCalendar.tsx(232,15): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventCalendar.tsx(241,15): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(87,7): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(101,25): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(110,17): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(138,29): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(153,21): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(167,17): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(182,21): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(221,23): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(225,23): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(229,23): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(233,23): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventDetailsDialog.tsx(248,15): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventFilters.tsx(28,5): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventFilters.tsx(117,19): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventFilters.tsx(123,11): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(184,5): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(207,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(221,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(235,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(253,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(271,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(281,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(291,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(301,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(311,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(321,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(331,16): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(333,25): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventForm.tsx(338,7): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventNotifications.tsx(27,9): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventNotifications.tsx(41,17): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventReminderDialog.tsx(149,5): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventReminderDialog.tsx(176,25): error TS2657: JSX expressions must have one parent element.
src/components/Calendar/EventReminderDialog.tsx(333,7): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarCard.tsx(18,7): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarList.tsx(13,7): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(55,9): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(57,19): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(73,18): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(75,19): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(88,17): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(90,19): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(104,17): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(106,19): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(109,26): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(114,24): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(123,11): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(125,19): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(139,17): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(142,24): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(145,22): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(151,9): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(154,24): error TS2657: JSX expressions must have one parent element.
src/components/car-rental/CarSearchForm.tsx(161,7): error TS2657: JSX expressions must have one parent element.
src/components/CarRentalManager.tsx(41,5): error TS2657: JSX expressions must have one parent element.
src/components/charts/MetricsChart.tsx(21,5): error TS2657: JSX expressions must have one parent element.
src/components/charts/MetricsChart.tsx(27,9): error TS2657: JSX expressions must have one parent element.
src/components/charts/MetricsChart.tsx(67,11): error TS2657: JSX expressions must have one parent element.
src/components/charts/MetricsChart.tsx(73,13): error TS2657: JSX expressions must have one parent element.
src/components/charts/MetricsChart.tsx(78,13): error TS2657: JSX expressions must have one parent element.
src/components/charts/MetricsChart.tsx(83,13): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(129,7): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(137,7): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(153,7): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(171,19): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(184,15): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(199,19): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(204,21): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(223,11): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(227,11): error TS2657: JSX expressions must have one parent element.
src/components/Chat/ChatWindow.tsx(230,11): error TS2657: JSX expressions must have one parent element.
src/components/chatbot/Chatbot.tsx(63,7): error TS2657: JSX expressions must have one parent element.
src/components/chatbot/Chatbot.tsx(72,9): error TS2657: JSX expressions must have one parent element.
src/components/chatbot/Chatbot.tsx(78,13): error TS2657: JSX expressions must have one parent element.
src/components/chatbot/Chatbot.tsx(89,15): error TS2657: JSX expressions must have one parent element.
src/components/chatbot/Chatbot.tsx(108,15): error TS2657: JSX expressions must have one parent element.
src/components/chatbot/Chatbot.tsx(119,11): error TS2657: JSX expressions must have one parent element.
src/components/common/ConfirmDialog.tsx(41,5): error TS2657: JSX expressions must have one parent element.
src/components/common/ConfirmDialog.tsx(44,9): error TS2657: JSX expressions must have one parent element.
src/components/common/ConfirmDialog.tsx(57,7): error TS2657: JSX expressions must have one parent element.
src/components/common/ConfirmDialog.tsx(60,7): error TS2657: JSX expressions must have one parent element.
src/components/common/CustomSnackbar.tsx(28,5): error TS2657: JSX expressions must have one parent element.
src/components/common/ErrorBoundary.tsx(62,9): error TS2657: JSX expressions must have one parent element.
src/components/common/ErrorBoundary.tsx(80,13): error TS2657: JSX expressions must have one parent element.
src/components/common/ErrorFeedback.tsx(37,5): error TS2657: JSX expressions must have one parent element.
src/components/common/ErrorFeedback.tsx(44,9): error TS2657: JSX expressions must have one parent element.
src/components/common/ErrorFeedback.tsx(48,7): error TS2657: JSX expressions must have one parent element.
src/components/common/ErrorFeedback.tsx(53,11): error TS2657: JSX expressions must have one parent element.
src/components/common/ErrorFeedback.tsx(62,15): error TS2657: JSX expressions must have one parent element.
src/components/common/ErrorFeedback.tsx(79,7): error TS2657: JSX expressions must have one parent element.
src/components/common/FormField.tsx(77,9): error TS2657: JSX expressions must have one parent element.
src/components/common/FormField.tsx(98,9): error TS2657: JSX expressions must have one parent element.
src/components/common/FormField.tsx(113,9): error TS2657: JSX expressions must have one parent element.
src/components/common/Image0ptimizer.tsx(57,7): error TS2657: JSX expressions must have one parent element.
src/components/common/LanguageSelector.tsx(21,5): error TS2657: JSX expressions must have one parent element.
src/components/common/LanguageSwitcher.tsx(34,5): error TS2657: JSX expressions must have one parent element.
src/components/common/LanguageSwitcher.tsx(49,9): error TS2657: JSX expressions must have one parent element.
src/components/common/LanguageSwitcher.tsx(62,9): error TS2657: JSX expressions must have one parent element.
src/components/common/Layout.tsx(18,5): error TS2657: JSX expressions must have one parent element.
src/components/common/LazyRoute.tsx(10,3): error TS2657: JSX expressions must have one parent element.
src/components/common/LoadingFallback.tsx(20,5): error TS2657: JSX expressions must have one parent element.
src/components/common/LoadingScreen.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/components/common/LoadingSpinner.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/components/common/LoadingView.tsx(27,9): error TS2657: JSX expressions must have one parent element.
src/components/common/LoadingView.tsx(45,15): error TS2657: JSX expressions must have one parent element.
src/components/common/Modal.tsx(48,5): error TS2657: JSX expressions must have one parent element.
src/components/common/Modal.tsx(61,13): error TS2657: JSX expressions must have one parent element.
src/components/common/Notification.tsx(13,5): error TS2657: JSX expressions must have one parent element.
src/components/common/NotificationProvider.tsx(71,9): error TS2657: JSX expressions must have one parent element.
src/components/common/OptimizedPage.tsx(41,5): error TS2657: JSX expressions must have one parent element.
src/components/common/OptimizedPage.tsx(45,11): error TS2657: JSX expressions must have one parent element.
src/components/common/OptimizedPage.tsx(55,9): error TS2657: JSX expressions must have one parent element.
src/components/common/PageHeader.tsx(71,5): error TS2657: JSX expressions must have one parent element.
src/components/common/PageHelmet.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/components/common/PageTransition.tsx(47,5): error TS2657: JSX expressions must have one parent element.
src/components/common/PageTransition.tsx(77,7): error TS2657: JSX expressions must have one parent element.
src/components/common/Pagination.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/common/Pagination.tsx(52,11): error TS2657: JSX expressions must have one parent element.
src/components/common/PrivateRoute.tsx(11,7): error TS2657: JSX expressions must have one parent element.
src/components/common/RetreatCard.tsx(68,5): error TS2657: JSX expressions must have one parent element.
src/components/common/RetreatCard.tsx(98,11): error TS2657: JSX expressions must have one parent element.
src/components/common/RetreatCard.tsx(106,13): error TS2657: JSX expressions must have one parent element.
src/components/common/RetreatCard.tsx(145,7): error TS2657: JSX expressions must have one parent element.
src/components/common/RetreatCard.tsx(151,13): error TS2657: JSX expressions must have one parent element.
src/components/common/RetreatCard.tsx(163,13): error TS2657: JSX expressions must have one parent element.
src/components/common/SearchAndFilter.tsx(123,11): error TS2657: JSX expressions must have one parent element.
src/components/common/SearchAndFilter.tsx(152,11): error TS2657: JSX expressions must have one parent element.
src/components/common/SearchAndFilter.tsx(157,13): error TS2657: JSX expressions must have one parent element.
src/components/common/SearchAndFilter.tsx(200,11): error TS2657: JSX expressions must have one parent element.
src/components/common/SearchAndFilter.tsx(223,5): error TS2657: JSX expressions must have one parent element.
src/components/common/SearchAndFilter.tsx(239,7): error TS2657: JSX expressions must have one parent element.
src/components/common/SearchAndFilter.tsx(246,7): error TS2657: JSX expressions must have one parent element.
src/components/common/SearchAndFilter.tsx(262,5): error TS2657: JSX expressions must have one parent element.
src/components/common/SecureInput.tsx(87,7): error TS2657: JSX expressions must have one parent element.
src/components/common/Sidebar.tsx(29,7): error TS2657: JSX expressions must have one parent element.
src/components/common/Sidebar.tsx(32,11): error TS2657: JSX expressions must have one parent element.
src/components/common/Sidebar.tsx(46,15): error TS2657: JSX expressions must have one parent element.
src/components/common/Skeleton.tsx(12,5): error TS2657: JSX expressions must have one parent element.
src/components/common/SkeletonLoader.tsx(58,3): error TS2657: JSX expressions must have one parent element.
src/components/common/SkeletonLoader.tsx(72,3): error TS2657: JSX expressions must have one parent element.
src/components/common/SkeletonPatterns.tsx(5,3): error TS2657: JSX expressions must have one parent element.
src/components/common/SkeletonPatterns.tsx(30,7): error TS2657: JSX expressions must have one parent element.
src/components/common/SkeletonPatterns.tsx(43,3): error TS2657: JSX expressions must have one parent element.
src/components/common/SkeletonPatterns.tsx(48,5): error TS2657: JSX expressions must have one parent element.
src/components/common/SkeletonPatterns.tsx(52,5): error TS2657: JSX expressions must have one parent element.
src/components/common/Table/Table.tsx(62,5): error TS2657: JSX expressions must have one parent element.
src/components/common/ThemeToggle.tsx(11,5): error TS2657: JSX expressions must have one parent element.
src/components/common/Toast.tsx(33,5): error TS2657: JSX expressions must have one parent element.
src/components/common/VirtualScroll.tsx(81,5): error TS2657: JSX expressions must have one parent element.
src/components/common/withImageOptimization.tsx(87,9): error TS2657: JSX expressions must have one parent element.
src/components/common/withLazyLoading.tsx(22,5): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityFeed.tsx(44,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityFeed.tsx(55,13): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityFeed.tsx(65,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityFeed.tsx(73,13): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityFeed.tsx(78,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityFeed.tsx(82,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityFilters.tsx(40,7): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityFilters.tsx(51,13): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityMemberCard.tsx(31,5): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityMemberCard.tsx(57,9): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityMemberCard.tsx(69,7): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityMemberCard.tsx(73,38): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityMemberList.tsx(23,7): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityMemberList.tsx(31,7): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityMemberList.tsx(40,7): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunityMemberList.tsx(50,9): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunitySearch.tsx(70,5): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunitySearch.tsx(81,7): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunitySearch.tsx(104,13): error TS2657: JSX expressions must have one parent element.
src/components/community/CommunitySearch.tsx(109,11): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(164,7): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(182,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(250,13): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(256,11): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(288,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(299,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(306,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(313,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(337,13): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(343,9): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(364,15): error TS2657: JSX expressions must have one parent element.
src/components/community/CreatePost.tsx(381,11): error TS2657: JSX expressions must have one parent element.
src/components/community/GroupVirtualEvents.tsx(107,47): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/components/community/GroupVirtualEvents.tsx(268,11): error TS2657: JSX expressions must have one parent element.
src/components/community/GroupVirtualEvents.tsx(283,21): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(152,7): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(169,15): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(184,17): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(187,17): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(193,13): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(196,13): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(203,15): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(253,21): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(265,11): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(284,13): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(291,13): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(301,15): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(332,9): error TS2657: JSX expressions must have one parent element.
src/components/community/Post.tsx(350,11): error TS2657: JSX expressions must have one parent element.
src/components/community/PostFeed.tsx(73,7): error TS2657: JSX expressions must have one parent element.
src/components/community/PostFeed.tsx(81,7): error TS2657: JSX expressions must have one parent element.
src/components/community/PostFeed.tsx(92,7): error TS2657: JSX expressions must have one parent element.
src/components/community/PostList.tsx(83,7): error TS2657: JSX expressions must have one parent element.
src/components/community/PostList.tsx(91,7): error TS2657: JSX expressions must have one parent element.
src/components/community/PostList.tsx(101,7): error TS2657: JSX expressions must have one parent element.
src/components/community/PostList.tsx(133,7): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(37,5): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(54,7): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(73,9): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(82,11): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(92,7): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(122,5): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(153,7): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(237,7): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(248,11): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(266,11): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(278,19): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(285,11): error TS2657: JSX expressions must have one parent element.
src/components/community/ThematicGroups.tsx(310,11): error TS2657: JSX expressions must have one parent element.
src/components/community/VirtualizedMemberList.tsx(47,7): error TS2657: JSX expressions must have one parent element.
src/components/ConfigForm.tsx(18,5): error TS2657: JSX expressions must have one parent element.
src/components/ConfigForm.tsx(31,13): error TS2657: JSX expressions must have one parent element.
src/components/ConfigForm.tsx(41,15): error TS2657: JSX expressions must have one parent element.
src/components/ConflictManager.tsx(38,5): error TS2657: JSX expressions must have one parent element.
src/components/ConflictManager.tsx(47,7): error TS2657: JSX expressions must have one parent element.
src/components/ConflictManager.tsx(56,9): error TS2657: JSX expressions must have one parent element.
src/components/ConflictManager.tsx(65,7): error TS2657: JSX expressions must have one parent element.
src/components/contact/SecureContactForm.tsx(74,5): error TS2657: JSX expressions must have one parent element.
src/components/contact/SecureContactForm.tsx(89,7): error TS2657: JSX expressions must have one parent element.
src/components/contact/SecureContactForm.tsx(104,7): error TS2657: JSX expressions must have one parent element.
src/components/contact/SecureContactForm.tsx(118,7): error TS2657: JSX expressions must have one parent element.
src/components/contact/SecureContactForm.tsx(123,9): error TS2657: JSX expressions must have one parent element.
src/components/contact/SecureContactForm.tsx(140,7): error TS2657: JSX expressions must have one parent element.
src/components/contact/SecureContactForm.tsx(150,9): error TS2657: JSX expressions must have one parent element.
src/components/contact/SecureContactForm.tsx(160,7): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsCard.tsx(17,5): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsCharts.tsx(127,11): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsCharts.tsx(151,15): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsCharts.tsx(162,11): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsCharts.tsx(194,11): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(44,5): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(49,7): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(60,9): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(68,9): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(73,9): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(79,13): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(99,9): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(105,13): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(115,9): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/AnalyticsDashboard.tsx(121,13): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/Dashboard.tsx(22,5): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/Dashboard.tsx(34,7): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/Dashboard.tsx(44,5): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/Dashboard.tsx(57,7): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/Dashboard.tsx(67,5): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/Dashboard.tsx(79,9): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/Dashboard.tsx(86,11): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/Dashboard.tsx(93,9): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/DashboardMessages.tsx(12,5): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/DashboardMessages.tsx(31,15): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/DashboardMessages.tsx(37,17): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/DashboardMessages.tsx(58,11): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/DashboardMessages.tsx(66,7): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(77,7): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(84,5): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(88,9): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(108,13): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(114,13): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(122,17): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(129,15): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(136,17): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(143,15): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(149,17): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(164,15): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(181,23): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(207,23): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(219,25): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(234,31): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(243,31): error TS2657: JSX expressions must have one parent element.
src/components/dashboard/PartnerDashboard.tsx(263,19): error TS2657: JSX expressions must have one parent element.
src/components/devops/APIGatewayMonitor.tsx(4,3): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseCard.tsx(16,7): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseCard.tsx(30,11): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseCard.tsx(36,9): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseCard.tsx(47,11): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseCard.tsx(52,11): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseCard.tsx(57,11): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseCard.tsx(64,9): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseFilters.tsx(20,5): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseFilters.tsx(24,11): error TS2657: JSX expressions must have one parent element.
src/components/education/CourseGrid.tsx(12,11): error TS2657: JSX expressions must have one parent element.
src/components/EducationManager.tsx(105,7): error TS2657: JSX expressions must have one parent element.
src/components/EducationManager.tsx(159,5): error TS2657: JSX expressions must have one parent element.
src/components/error/RouteErrorBoundary.tsx(22,5): error TS2657: JSX expressions must have one parent element.
src/components/error/RouteErrorBoundary.tsx(29,13): error TS2657: JSX expressions must have one parent element.
src/components/error/RouteErrorBoundary.tsx(39,11): error TS2657: JSX expressions must have one parent element.
src/components/error/RouteErrorBoundary.tsx(63,11): error TS2657: JSX expressions must have one parent element.
src/components/ErrorBoundary.tsx(38,9): error TS2657: JSX expressions must have one parent element.
src/components/errors/ErrorDisplay.tsx(118,7): error TS2657: JSX expressions must have one parent element.
src/components/errors/ErrorDisplay.tsx(146,5): error TS2657: JSX expressions must have one parent element.
src/components/errors/ErrorDisplay.tsx(168,7): error TS2657: JSX expressions must have one parent element.
src/components/errors/ErrorDisplay.tsx(189,9): error TS2657: JSX expressions must have one parent element.
src/components/errors/ErrorFallback.tsx(14,5): error TS2657: JSX expressions must have one parent element.
src/components/errors/ErrorFallback.tsx(20,9): error TS2657: JSX expressions must have one parent element.
src/components/events/EventCalendar.tsx(28,5): error TS2657: JSX expressions must have one parent element.
src/components/events/EventCalendar.tsx(33,11): error TS2657: JSX expressions must have one parent element.
src/components/events/EventCalendar.tsx(43,15): error TS2657: JSX expressions must have one parent element.
src/components/events/EventCalendar.tsx(53,19): error TS2657: JSX expressions must have one parent element.
src/components/events/EventCalendar.tsx(58,19): error TS2657: JSX expressions must have one parent element.
src/components/examples/UserProfileWithErrorHandling.tsx(175,11): error TS2657: JSX expressions must have one parent element.
src/components/examples/UserProfileWithErrorHandling.tsx(179,13): error TS2657: JSX expressions must have one parent element.
src/components/examples/UserProfileWithErrorHandling.tsx(182,13): error TS2657: JSX expressions must have one parent element.
src/components/examples/UserProfileWithErrorHandling.tsx(186,15): error TS2657: JSX expressions must have one parent element.
src/components/examples/UserProfileWithErrorHandling.tsx(216,3): error TS2657: JSX expressions must have one parent element.
src/components/ExpenseManager.tsx(43,5): error TS2657: JSX expressions must have one parent element.
src/components/ExpenseManager.tsx(76,11): error TS2657: JSX expressions must have one parent element.
src/components/export/ExportConfigDialog.tsx(124,5): error TS2657: JSX expressions must have one parent element.
src/components/export/ExportConfigDialog.tsx(133,7): error TS2657: JSX expressions must have one parent element.
src/components/export/ExportConfigDialog.tsx(151,11): error TS2657: JSX expressions must have one parent element.
src/components/export/ExportConfigDialog.tsx(189,11): error TS2657: JSX expressions must have one parent element.
src/components/export/ExportConfigDialog.tsx(242,15): error TS2657: JSX expressions must have one parent element.
src/components/export/ExportConfigDialog.tsx(264,19): error TS2657: JSX expressions must have one parent element.
src/components/export/ExportConfigDialog.tsx(320,7): error TS2657: JSX expressions must have one parent element.
src/components/FeaturedRetreats.tsx(28,5): error TS2657: JSX expressions must have one parent element.
src/components/Features.tsx(25,5): error TS2657: JSX expressions must have one parent element.
src/components/Features.tsx(33,13): error TS2657: JSX expressions must have one parent element.
src/components/FileList/Filelist.tsx(46,5): error TS2657: JSX expressions must have one parent element.
src/components/FileList/Filelist.tsx(55,11): error TS2657: JSX expressions must have one parent element.
src/components/FileList/Filelist.tsx(61,13): error TS2657: JSX expressions must have one parent element.
src/components/FileUploader/FileUploader.tsx(42,5): error TS2657: JSX expressions must have one parent element.
src/components/FileUploader/FileUploader.tsx(51,13): error TS2657: JSX expressions must have one parent element.
src/components/FileUploader/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/components/FlightFinder.tsx(26,5): error TS2657: JSX expressions must have one parent element.
src/components/flights/AirportSearch.tsx(59,5): error TS2657: JSX expressions must have one parent element.
src/components/flights/AirportSearch.tsx(81,13): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightDatePicker.tsx(14,5): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(32,9): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(36,17): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(39,15): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(49,17): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(55,15): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(68,15): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(78,17): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(92,15): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(96,13): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightResults.tsx(100,13): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightSearch.tsx(21,5): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightSearch.tsx(48,9): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightSearch.tsx(56,13): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightSearch.tsx(62,11): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightSearch.tsx(75,15): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightSearch.tsx(83,11): error TS2657: JSX expressions must have one parent element.
src/components/flights/FlightSearch.tsx(90,11): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(43,5): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(61,9): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(68,15): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(78,17): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(92,13): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(97,15): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(107,17): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(121,13): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(126,15): error TS2657: JSX expressions must have one parent element.
src/components/flights/PassengerSelector.tsx(136,17): error TS2657: JSX expressions must have one parent element.
src/components/Form/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/components/Form/UserProfileForm.tsx(129,11): error TS2657: JSX expressions must have one parent element.
src/components/Form/UserProfileForm.tsx(149,15): error TS2657: JSX expressions must have one parent element.
src/components/Form/UserProfileForm.tsx(168,15): error TS2657: JSX expressions must have one parent element.
src/components/Form/UserProfileForm.tsx(187,15): error TS2657: JSX expressions must have one parent element.
src/components/Form/UserProfileForm.tsx(206,15): error TS2657: JSX expressions must have one parent element.
src/components/Form/UserProfileForm.tsx(225,15): error TS2657: JSX expressions must have one parent element.
src/components/Form/UserProfileForm.tsx(252,13): error TS2657: JSX expressions must have one parent element.
src/components/Form/UserProfileForm.tsx(271,17): error TS2657: JSX expressions must have one parent element.
src/components/Form/UserProfileForm.tsx(285,17): error TS2657: JSX expressions must have one parent element.
src/components/Foundation.tsx(24,5): error TS2657: JSX expressions must have one parent element.
src/components/Foundation.tsx(35,13): error TS2657: JSX expressions must have one parent element.
src/components/Foundation.tsx(49,19): error TS2657: JSX expressions must have one parent element.
src/components/Foundation.tsx(56,17): error TS2657: JSX expressions must have one parent element.
src/components/Foundation.tsx(69,9): error TS2657: JSX expressions must have one parent element.
src/components/gamification/Achievements.tsx(84,5): error TS2657: JSX expressions must have one parent element.
src/components/gamification/Achievements.tsx(106,11): error TS2657: JSX expressions must have one parent element.
src/components/gamification/Achievements.tsx(117,19): error TS2657: JSX expressions must have one parent element.
src/components/gamification/Achievements.tsx(124,15): error TS2657: JSX expressions must have one parent element.
src/components/gamification/Achievements.tsx(135,17): error TS2657: JSX expressions must have one parent element.
src/components/gamification/Achievements.tsx(151,17): error TS2657: JSX expressions must have one parent element.
src/components/gamification/Achievements.tsx(183,7): error TS2657: JSX expressions must have one parent element.
src/components/gamification/Achievements.tsx(194,13): error TS2657: JSX expressions must have one parent element.
src/components/gamification/AchievementSystem.tsx(50,5): error TS2657: JSX expressions must have one parent element.
src/components/gamification/AchievementSystem.tsx(55,11): error TS2657: JSX expressions must have one parent element.
src/components/gamification/AchievementSystem.tsx(65,15): error TS2657: JSX expressions must have one parent element.
src/components/gamification/AchievementSystem.tsx(75,17): error TS2657: JSX expressions must have one parent element.
src/components/gamification/AchievementSystem.tsx(86,19): error TS2657: JSX expressions must have one parent element.
src/components/gdpr/ConsentManager.tsx(108,5): error TS2657: JSX expressions must have one parent element.
src/components/gdpr/ConsentManager.tsx(128,13): error TS2657: JSX expressions must have one parent element.
src/components/gdpr/ConsentManager.tsx(141,19): error TS2657: JSX expressions must have one parent element.
src/components/gdpr/SubjectRequestForm.tsx(75,5): error TS2657: JSX expressions must have one parent element.
src/components/gdpr/SubjectRequestForm.tsx(93,9): error TS2657: JSX expressions must have one parent element.
src/components/gdpr/SubjectRequestForm.tsx(119,11): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCard.tsx(108,9): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCard.tsx(115,7): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCard.tsx(126,7): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCard.tsx(163,9): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardAdmin.tsx(120,7): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardAdmin.tsx(128,5): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardAdmin.tsx(135,9): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardAdmin.tsx(142,7): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardAdmin.tsx(158,9): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardAdmin.tsx(170,9): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardAdmin.tsx(186,9): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardList.tsx(46,5): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardList.tsx(58,7): error TS2657: JSX expressions must have one parent element.
src/components/gift-cards/GiftCardList.tsx(76,9): error TS2657: JSX expressions must have one parent element.
src/components/groups/CreateGroupModal.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/groups/CreateGroupModal.tsx(47,11): error TS2657: JSX expressions must have one parent element.
src/components/groups/CreateGroupModal.tsx(65,17): error TS2657: JSX expressions must have one parent element.
src/components/groups/CreateGroupModal.tsx(78,17): error TS2657: JSX expressions must have one parent element.
src/components/Header/HeaderCarousel.tsx(64,5): error TS2657: JSX expressions must have one parent element.
src/components/Header/HeaderCarousel.tsx(67,11): error TS2657: JSX expressions must have one parent element.
src/components/Header/HeaderCarousel.tsx(77,13): error TS2657: JSX expressions must have one parent element.
src/components/Header/HeaderCarousel.tsx(82,15): error TS2657: JSX expressions must have one parent element.
src/components/Home.tsx(5,3): error TS2657: JSX expressions must have one parent element.
src/components/HotelBookingManager.tsx(46,5): error TS2657: JSX expressions must have one parent element.
src/components/HotelBookingManager.tsx(49,7): error TS2657: JSX expressions must have one parent element.
src/components/HotelBookingManager.tsx(61,9): error TS2657: JSX expressions must have one parent element.
src/components/HotelBookingManager.tsx(72,9): error TS2657: JSX expressions must have one parent element.
src/components/HotelBookingManager.tsx(93,9): error TS2657: JSX expressions must have one parent element.
src/components/i18n/TranslationStatus.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/components/i18n/TranslationStatus.tsx(34,5): error TS2657: JSX expressions must have one parent element.
src/components/i18n/TranslationStatus.tsx(39,11): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceComparison.tsx(12,5): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceComparison.tsx(20,11): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceComparison.tsx(33,17): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceComparison.tsx(40,19): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceComparison.tsx(45,27): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceComparison.tsx(53,21): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceComparison.tsx(57,27): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceComparison.tsx(66,19): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceQuoteForm.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceQuoteForm.tsx(44,11): error TS2657: JSX expressions must have one parent element.
src/components/insurance/InsuranceQuoteForm.tsx(53,11): error TS2657: JSX expressions must have one parent element.
src/components/insurance/PolicyDetails.tsx(12,5): error TS2657: JSX expressions must have one parent element.
src/components/insurance/PolicyDetails.tsx(33,9): error TS2657: JSX expressions must have one parent element.
src/components/insurance/PolicyDetails.tsx(40,13): error TS2657: JSX expressions must have one parent element.
src/components/insurance/PolicyDetails.tsx(44,13): error TS2657: JSX expressions must have one parent element.
src/components/insurance/PolicyDetails.tsx(53,13): error TS2657: JSX expressions must have one parent element.
src/components/insurance/PolicyDetails.tsx(56,15): error TS2657: JSX expressions must have one parent element.
src/components/insurance/PolicyDetails.tsx(63,17): error TS2657: JSX expressions must have one parent element.
src/components/insurance/PolicyDetails.tsx(70,19): error TS2657: JSX expressions must have one parent element.
src/components/InsuranceComparator.tsx(28,5): error TS2657: JSX expressions must have one parent element.
src/components/InteractiveMap.tsx(21,11): error TS2657: JSX expressions must have one parent element.
src/components/InteractiveMap.tsx(44,11): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceActions.tsx(35,5): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceActions.tsx(44,7): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceActions.tsx(52,7): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(14,7): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(27,9): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(35,9): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(53,9): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(63,13): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(75,9): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(80,11): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(84,11): error TS2657: JSX expressions must have one parent element.
src/components/invoice/InvoiceTemplate.tsx(93,9): error TS2657: JSX expressions must have one parent element.
src/components/layout/ErrorBoundary.tsx(35,9): error TS2657: JSX expressions must have one parent element.
src/components/layout/Footer.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(82,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(91,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(150,15): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(177,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(202,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(206,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(210,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(220,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(230,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(263,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/Header.tsx(270,9): error TS2657: JSX expressions must have one parent element.
src/components/layout/MainLayout.tsx(63,5): error TS2657: JSX expressions must have one parent element.
src/components/layout/MainLayout.tsx(67,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/MainLayout.tsx(73,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/MainLayout.tsx(81,15): error TS2657: JSX expressions must have one parent element.
src/components/layout/MainLayout.tsx(89,15): error TS2657: JSX expressions must have one parent element.
src/components/layout/MainLayout.tsx(119,17): error TS2657: JSX expressions must have one parent element.
src/components/layout/MainLayout.tsx(132,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/MainLayout.tsx(156,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/MobileDrawer/MobileDrawer.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/MobileDrawer/MobileDrawer.tsx(57,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/MobileOptimizedLayout.tsx(86,5): error TS2657: JSX expressions must have one parent element.
src/components/layout/MobileOptimizedLayout.tsx(106,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/MobileOptimizedLayout.tsx(114,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/MobileOptimizedLayout.tsx(135,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/MobileOptimizedLayout.tsx(143,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/MobileOptimizedLayout.tsx(156,9): error TS2657: JSX expressions must have one parent element.
src/components/layout/MobileOptimizedLayout.tsx(162,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/PrivateLayout.tsx(12,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/PrivateLayout.tsx(28,7): error TS2657: JSX expressions must have one parent element.
src/components/layout/ProfileMenu/ProfileMenu.tsx(26,5): error TS2657: JSX expressions must have one parent element.
src/components/layout/ProfileMenu/ProfileMenu.tsx(44,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/ProfileMenu/ProfileMenu.tsx(49,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/PublicLayout.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/components/layout/PublicNavbar.tsx(9,5): error TS2657: JSX expressions must have one parent element.
src/components/layout/PublicNavbar.tsx(14,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/PublicNavbar.tsx(51,15): error TS2657: JSX expressions must have one parent element.
src/components/layout/PublicNavbar.tsx(68,9): error TS2657: JSX expressions must have one parent element.
src/components/layout/PublicNavbar.tsx(83,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/ResponsiveLayout.tsx(41,5): error TS2657: JSX expressions must have one parent element.
src/components/layout/SearchBar/SearchBar.tsx(89,13): error TS2657: JSX expressions must have one parent element.
src/components/layout/SearchBar/SearchBar.tsx(165,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/SearchBar/SearchBar.tsx(172,15): error TS2657: JSX expressions must have one parent element.
src/components/layout/Sidebar.tsx(60,5): error TS2657: JSX expressions must have one parent element.
src/components/layout/Sidebar.tsx(99,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/Sidebar.tsx(113,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/TopBar.tsx(11,5): error TS2657: JSX expressions must have one parent element.
src/components/layout/TopBar.tsx(18,11): error TS2657: JSX expressions must have one parent element.
src/components/layout/TopBar.tsx(25,15): error TS2657: JSX expressions must have one parent element.
src/components/layout/TopBar.tsx(42,15): error TS2657: JSX expressions must have one parent element.
src/components/layouts/AdminLayout.tsx(24,7): error TS2657: JSX expressions must have one parent element.
src/components/layouts/AdminLayout.tsx(47,11): error TS2657: JSX expressions must have one parent element.
src/components/layouts/AdminLayout.tsx(56,9): error TS2657: JSX expressions must have one parent element.
src/components/layouts/MainLayout.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/components/LiveRetreats.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/components/LiveRetreats.tsx(10,13): error TS2657: JSX expressions must have one parent element.
src/components/LiveRetreats.tsx(12,15): error TS2657: JSX expressions must have one parent element.
src/components/livestream/LiveChat.tsx(19,5): error TS2657: JSX expressions must have one parent element.
src/components/livestream/LiveChat.tsx(22,9): error TS2657: JSX expressions must have one parent element.
src/components/livestream/LiveChat.tsx(38,7): error TS2657: JSX expressions must have one parent element.
src/components/livestream/LiveChat.tsx(44,11): error TS2657: JSX expressions must have one parent element.
src/components/livestream/LivestreamRetreat.tsx(43,7): error TS2657: JSX expressions must have one parent element.
src/components/livestream/LivestreamRetreat.tsx(50,9): error TS2657: JSX expressions must have one parent element.
src/components/livestream/LivestreamRetreat.tsx(54,11): error TS2657: JSX expressions must have one parent element.
src/components/livestream/LivestreamRetreat.tsx(64,11): error TS2657: JSX expressions must have one parent element.
src/components/livestream/LivestreamRetreat.tsx(73,17): error TS2657: JSX expressions must have one parent element.
src/components/livestream/ParticipantsList.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/components/livestream/ParticipantsList.tsx(35,13): error TS2657: JSX expressions must have one parent element.
src/components/livestream/ParticipantsList.tsx(47,11): error TS2657: JSX expressions must have one parent element.
src/components/livestream/ParticipantsList.tsx(50,13): error TS2657: JSX expressions must have one parent element.
src/components/livestream/StreamControls.tsx(20,5): error TS2657: JSX expressions must have one parent element.
src/components/livestream/StreamControls.tsx(52,9): error TS2657: JSX expressions must have one parent element.
src/components/livestream/StreamControls.tsx(57,7): error TS2657: JSX expressions must have one parent element.
src/components/LivestreamSection.tsx(48,5): error TS2657: JSX expressions must have one parent element.
src/components/LivestreamSection.tsx(57,13): error TS2657: JSX expressions must have one parent element.
src/components/LivestreamSection.tsx(74,17): error TS2657: JSX expressions must have one parent element.
src/components/LivestreamSection.tsx(94,11): error TS2657: JSX expressions must have one parent element.
src/components/LivestreamSection.tsx(100,11): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/LoyaltyCard.tsx(29,5): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/LoyaltyCard.tsx(38,1): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/LoyaltyCard.tsx(44,1): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/LoyaltyTierCard.tsx(31,5): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/LoyaltyTierCard.tsx(43,1): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/LoyaltyTierCard.tsx(52,1): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/LoyaltyTierCard.tsx(70,1): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/LoyaltyTierCard.tsx(75,13): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/PointsConverter.tsx(39,5): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/PointsConverter.tsx(50,1): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/PointsConverter.tsx(65,13): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/PointsConverter.tsx(72,1): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/PointsHistory.tsx(11,5): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/PointsHistory.tsx(15,11): error TS2657: JSX expressions must have one parent element.
src/components/loyalty/PointsHistory.tsx(31,1): error TS2657: JSX expressions must have one parent element.
src/components/LoyaltyProgramManager.tsx(30,5): error TS2657: JSX expressions must have one parent element.
src/components/MarketplaceManager.tsx(41,5): error TS2657: JSX expressions must have one parent element.
src/components/MarketplaceManager.tsx(51,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/ChatHeader.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/components/messaging/ChatHeader.tsx(31,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/ChatHeader.tsx(38,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/ChatHeader.tsx(42,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/ChatHeader.tsx(45,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessageList.tsx(34,11): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessagingPanel.tsx(144,7): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessagingPanel.tsx(153,7): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessagingPanel.tsx(167,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessagingPanel.tsx(191,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessagingPanel.tsx(202,19): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessagingPanel.tsx(232,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessagingPanel.tsx(253,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessagingPanel.tsx(267,1): error TS2657: JSX expressions must have one parent element.
src/components/messaging/MessagingPanel.tsx(282,13): error TS2657: JSX expressions must have one parent element.
src/components/MessagingManager.tsx(35,5): error TS2657: JSX expressions must have one parent element.
src/components/MessagingManager.tsx(39,11): error TS2657: JSX expressions must have one parent element.
src/components/MessagingManager.tsx(46,7): error TS2657: JSX expressions must have one parent element.
src/components/MessagingManager.tsx(60,11): error TS2657: JSX expressions must have one parent element.
src/components/molecules/BookingForm.tsx(77,7): error TS2657: JSX expressions must have one parent element.
src/components/molecules/BookingForm.tsx(95,7): error TS2657: JSX expressions must have one parent element.
src/components/molecules/BookingForm.tsx(113,7): error TS2657: JSX expressions must have one parent element.
src/components/molecules/BookingForm.tsx(133,7): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Footer.tsx(31,5): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Footer.tsx(35,11): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Footer.tsx(46,15): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Footer.tsx(49,17): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Footer.tsx(62,15): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Footer.tsx(65,17): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Footer.tsx(78,15): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Footer.tsx(81,17): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Footer.tsx(90,9): error TS2657: JSX expressions must have one parent element.
src/components/molecules/FormField/FormField.tsx(51,5): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Modal/AccessibleModal.tsx(67,5): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Modal/AccessibleModal.tsx(78,9): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Modal/AccessibleModal.tsx(84,13): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Navbar.tsx(84,5): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Navbar.tsx(142,11): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Navbar.tsx(167,13): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Navbar.tsx(190,13): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Navbar.tsx(202,17): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Navbar.tsx(209,17): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Navbar.tsx(217,19): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Navigation/AccessibleNavigation.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Navigation/Navigation.tsx(28,11): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Pagination/AccessiblePagination.tsx(54,5): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Pagination/AccessiblePagination.tsx(87,7): error TS2657: JSX expressions must have one parent element.
src/components/molecules/SearchBar/SearchBar.tsx(86,5): error TS2657: JSX expressions must have one parent element.
src/components/molecules/SearchBar/SearchBar.tsx(96,9): error TS2657: JSX expressions must have one parent element.
src/components/molecules/StatCard/StatCard.tsx(51,5): error TS2657: JSX expressions must have one parent element.
src/components/molecules/StatCard/StatCard.tsx(66,9): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Table/AccessibleTable.tsx(57,5): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Table/AccessibleTable.tsx(69,19): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Table/AccessibleTable.tsx(95,19): error TS2657: JSX expressions must have one parent element.
src/components/molecules/Table/AccessibleTable.tsx(120,13): error TS2657: JSX expressions must have one parent element.
src/components/molecules/WalletConnect.tsx(177,7): error TS2657: JSX expressions must have one parent element.
src/components/molecules/WalletConnect.tsx(185,9): error TS2657: JSX expressions must have one parent element.
src/components/molecules/WalletConnect.tsx(192,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertRuleForm.tsx(72,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertRuleForm.tsx(93,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertRuleForm.tsx(102,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertRuleForm.tsx(146,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertsManager.tsx(94,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertsManager.tsx(111,7): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertsManager.tsx(126,15): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertsManager.tsx(133,17): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertsManager.tsx(145,17): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertsManager.tsx(150,17): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertsManager.tsx(159,19): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertsManager.tsx(174,7): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/AlertsManager.tsx(188,19): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/charts/ReportChart.tsx(66,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/charts/ReportChart.tsx(71,7): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(141,7): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(152,7): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(159,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(189,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(200,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(213,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(223,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(233,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(244,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(259,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(289,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/Dashboard.tsx(293,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/exports/ReportExport.tsx(80,7): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/exports/ReportExport.tsx(93,11): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/exports/ReportExport.tsx(108,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/exports/ReportExport.tsx(120,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/exports/ReportExport.tsx(155,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(149,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(155,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(160,11): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(166,7): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(180,11): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(193,11): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(203,11): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(213,11): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(236,19): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(247,11): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/filters/ReportFilters.tsx(270,19): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/MetricCard.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/PerformanceChart.tsx(41,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/PerformanceChart.tsx(47,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/PerformanceMetrics.tsx(25,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/PerformanceMetrics.tsx(58,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(177,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(197,7): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(213,15): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(225,17): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(230,17): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(241,17): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(247,17): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(256,19): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(260,19): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(285,7): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(310,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(318,19): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ReportsManager.tsx(345,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ScheduledReportForm.tsx(237,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ScheduledReportForm.tsx(243,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ScheduledReportForm.tsx(312,13): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ScheduledReportForm.tsx(328,46): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/ScheduledReportForm.tsx(364,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/TestDetails.tsx(40,5): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/TestDetails.tsx(46,9): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/TestDetails.tsx(60,17): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/TestDetails.tsx(65,19): error TS2657: JSX expressions must have one parent element.
src/components/monitoring/TestDetails.tsx(82,19): error TS2657: JSX expressions must have one parent element.
src/components/navigation/ContextualNavigation.tsx(64,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/ContextualNavigation.tsx(81,17): error TS2657: JSX expressions must have one parent element.
src/components/navigation/ContextualNavigation.tsx(98,25): error TS2657: JSX expressions must have one parent element.
src/components/navigation/ContextualNavigation.tsx(133,13): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(101,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(123,13): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(139,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(146,7): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(175,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(187,7): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(194,15): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(199,21): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(224,21): error TS2657: JSX expressions must have one parent element.
src/components/navigation/CustomShortcutsManager.tsx(231,21): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Footer.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Footer.tsx(39,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Footer.tsx(45,13): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Footer.tsx(75,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Footer.tsx(88,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/GestureVisualizer.tsx(89,7): error TS2657: JSX expressions must have one parent element.
src/components/navigation/GestureVisualizer.tsx(108,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/KeyboardShortcuts.tsx(70,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/KeyboardShortcuts.tsx(80,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/KeyboardShortcuts.tsx(87,7): error TS2657: JSX expressions must have one parent element.
src/components/navigation/KeyboardShortcuts.tsx(103,19): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Navbar.tsx(44,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Navbar.tsx(47,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Navbar.tsx(58,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Navbar.tsx(76,7): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Navbar.tsx(79,13): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationHistory.tsx(82,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationHistory.tsx(88,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationHistory.tsx(94,7): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationHistory.tsx(108,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationPreferences.tsx(57,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationPreferences.tsx(66,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationPreferences.tsx(76,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationPreferences.tsx(87,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationPreferences.tsx(105,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationPreferences.tsx(150,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationPreferences.tsx(186,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavigationPreferences.tsx(237,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/NavItem.tsx(35,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Sidebar.tsx(51,5): error TS2657: JSX expressions must have one parent element.
src/components/navigation/Sidebar.tsx(58,9): error TS2657: JSX expressions must have one parent element.
src/components/navigation/VoiceNavigation.tsx(92,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/VoiceNavigation.tsx(106,19): error TS2657: JSX expressions must have one parent element.
src/components/navigation/VoiceNavigation.tsx(124,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/VoiceNavigation.tsx(144,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/VoiceNavigation.tsx(161,11): error TS2657: JSX expressions must have one parent element.
src/components/navigation/VoiceNavigation.tsx(172,9): error TS2657: JSX expressions must have one parent element.
src/components/NetworkStatsMonitor.tsx(199,5): error TS2657: JSX expressions must have one parent element.
src/components/NetworkStatsMonitor.tsx(217,9): error TS2657: JSX expressions must have one parent element.
src/components/NetworkStatsMonitor.tsx(225,13): error TS2657: JSX expressions must have one parent element.
src/components/NetworkStatsMonitor.tsx(230,13): error TS2657: JSX expressions must have one parent element.
src/components/NetworkStatsMonitor.tsx(237,11): error TS2657: JSX expressions must have one parent element.
src/components/NetworkStatsMonitor.tsx(244,13): error TS2657: JSX expressions must have one parent element.
src/components/NetworkStatsMonitor.tsx(249,13): error TS2657: JSX expressions must have one parent element.
src/components/NetworkStatsMonitor.tsx(254,13): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTCard.tsx(28,7): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTCard.tsx(34,13): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTCard.tsx(43,11): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTCard.tsx(51,13): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTCard.tsx(62,13): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTCollection.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGallery.tsx(52,7): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGallery.tsx(68,7): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGallery.tsx(76,7): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGallery.tsx(88,11): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGallery.tsx(124,13): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGallery.tsx(147,15): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGallery.tsx(154,21): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGallery.tsx(169,13): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGrid.tsx(20,5): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGrid.tsx(81,7): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGrid.tsx(89,7): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGrid.tsx(96,7): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTGrid.tsx(106,9): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTMinter.tsx(70,5): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTMinter.tsx(82,9): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTMinter.tsx(87,17): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTMinter.tsx(100,19): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTMinter.tsx(122,13): error TS2657: JSX expressions must have one parent element.
src/components/nft/NFTMinter.tsx(133,9): error TS2657: JSX expressions must have one parent element.
src/components/nft/OptimizedNFTGrid.tsx(35,7): error TS2657: JSX expressions must have one parent element.
src/components/nft/OptimizedNFTGrid.tsx(83,9): error TS2657: JSX expressions must have one parent element.
src/components/nft/OptimizedNFTGrid.tsx(93,5): error TS2657: JSX expressions must have one parent element.
src/components/NFTGallery.tsx(54,5): error TS2657: JSX expressions must have one parent element.
src/components/NFTGallery.tsx(63,13): error TS2657: JSX expressions must have one parent element.
src/components/NFTGallery.tsx(74,17): error TS2657: JSX expressions must have one parent element.
src/components/NFTGallery.tsx(85,19): error TS2657: JSX expressions must have one parent element.
src/components/NFTGallery.tsx(96,19): error TS2657: JSX expressions must have one parent element.
src/components/NFTGallery.tsx(101,21): error TS2657: JSX expressions must have one parent element.
src/components/NFTGallery.tsx(112,11): error TS2657: JSX expressions must have one parent element.
src/components/NFTGallery.tsx(118,11): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationBadge.tsx(38,5): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(150,5): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(161,13): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(165,11): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(171,7): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(176,37): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(196,9): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(203,9): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(210,9): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(223,11): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(327,17): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(340,21): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(344,23): error TS2657: JSX expressions must have one parent element.
src/components/Notification/NotificationCenter.tsx(391,15): error TS2657: JSX expressions must have one parent element.
src/components/NotificationList.tsx(28,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestForm.tsx(67,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestForm.tsx(98,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestForm.tsx(108,19): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestForm.tsx(114,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestForm.tsx(123,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestForm.tsx(145,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestingDashboard.tsx(83,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestingDashboard.tsx(92,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestingDashboard.tsx(108,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestingDashboard.tsx(133,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestResults.tsx(44,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestResults.tsx(54,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestResults.tsx(64,19): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestResults.tsx(71,21): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestResults.tsx(79,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestResults.tsx(83,19): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestResults.tsx(90,21): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ABTestResults.tsx(102,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ConnectionStatus.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/ConnectionStatus.tsx(35,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(106,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(114,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(121,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(137,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(154,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(168,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(192,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(197,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(206,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(215,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(230,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(235,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(254,21): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(265,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(270,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(306,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationAnalytics.tsx(311,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationBadge.tsx(34,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationCenter.tsx(10,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(74,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(82,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(89,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(93,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(100,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(110,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(126,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(142,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(170,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(175,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(196,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDashboard.tsx(203,19): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDropdown.tsx(26,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDropdown.tsx(46,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationDropdown.tsx(69,19): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(157,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(174,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(188,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(201,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(236,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(248,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(258,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(262,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(267,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationHistory.tsx(276,19): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationList.tsx(57,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationList.tsx(76,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationList.tsx(81,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationList.tsx(102,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationManager.tsx(91,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationManager.tsx(98,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationManager.tsx(119,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(204,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(211,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(216,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(220,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(260,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(269,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(273,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(282,19): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(305,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(322,19): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(354,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(365,19): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(372,27): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(390,21): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(402,25): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(407,21): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationPreferences.tsx(430,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(155,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(169,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(191,21): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(198,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(203,17): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(240,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(247,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(269,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(283,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(299,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(325,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationScheduler.tsx(339,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationSettings.tsx(34,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationSettings.tsx(47,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationSettings.tsx(109,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationSystem.tsx(56,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(125,5): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(139,11): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(150,21): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(183,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(199,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(207,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(221,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(229,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(240,13): error TS2657: JSX expressions must have one parent element.
src/components/notifications/NotificationTemplates.tsx(268,9): error TS2657: JSX expressions must have one parent element.
src/components/notifications/RealTimeNotifications.tsx(118,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/RealTimeNotifications.tsx(128,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/RealTimeNotifications.tsx(140,15): error TS2657: JSX expressions must have one parent element.
src/components/notifications/RealTimeNotifications.tsx(159,21): error TS2657: JSX expressions must have one parent element.
src/components/notifications/RealTimeNotifications.tsx(171,7): error TS2657: JSX expressions must have one parent element.
src/components/notifications/RealTimeNotifications.tsx(185,13): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineExample.tsx(104,5): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineExample.tsx(118,9): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineExample.tsx(124,7): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineExample.tsx(141,9): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineExample.tsx(158,13): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineExample.tsx(166,7): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineExample.tsx(173,15): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineExample.tsx(178,17): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineManager.tsx(38,5): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineManager.tsx(45,9): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineManager.tsx(52,11): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineManager.tsx(60,9): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineManager.tsx(73,11): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineManager.tsx(85,11): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineManager.tsx(97,11): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineManager.tsx(107,9): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineManager.tsx(217,7): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineProfileEditor.tsx(137,7): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineProfileEditor.tsx(145,5): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineProfileEditor.tsx(150,7): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineProfileEditor.tsx(166,9): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineProfileEditor.tsx(181,9): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineProfileEditor.tsx(194,9): error TS2657: JSX expressions must have one parent element.
src/components/offline/OfflineProfileEditor.tsx(208,9): error TS2657: JSX expressions must have one parent element.
src/components/optimizations/MediaResource.tsx(71,9): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(18,5): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(29,11): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(36,15): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(46,15): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(55,17): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(62,23): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(70,17): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(77,23): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(83,17): error TS2657: JSX expressions must have one parent element.
src/components/organisms/BookingModal.tsx(94,11): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CalendarComponent.tsx(142,7): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(60,5): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(66,9): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(79,13): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(97,17): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(117,19): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(125,21): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(134,19): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(150,19): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(160,21): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(170,11): error TS2657: JSX expressions must have one parent element.
src/components/organisms/CommunitySection.tsx(175,11): error TS2657: JSX expressions must have one parent element.
src/components/organisms/DashboardHeader/DashboardHeader.tsx(77,5): error TS2657: JSX expressions must have one parent element.
src/components/organisms/DashboardHeader/DashboardHeader.tsx(83,7): error TS2657: JSX expressions must have one parent element.
src/components/organisms/DashboardHeader/DashboardHeader.tsx(93,9): error TS2657: JSX expressions must have one parent element.
src/components/organisms/FilterBar/FilterBar.tsx(61,5): error TS2657: JSX expressions must have one parent element.
src/components/organisms/Form/Form.tsx(49,5): error TS2657: JSX expressions must have one parent element.
src/components/organisms/Navigation/Navigation.tsx(37,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/AnalyticsChart.tsx(45,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/BookingCompletionChart.tsx(39,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/CustomerDemographicsChart.tsx(52,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/CustomerSatisfactionChart.tsx(51,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/MetricsCard.tsx(17,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/MetricsCard.tsx(22,11): error TS2657: JSX expressions must have one parent element.
src/components/partner/PartnerAnalytics.tsx(53,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/PartnerAnalytics.tsx(68,7): error TS2657: JSX expressions must have one parent element.
src/components/partner/PartnerAnalytics.tsx(74,7): error TS2657: JSX expressions must have one parent element.
src/components/partner/PartnerAnalytics.tsx(85,7): error TS2657: JSX expressions must have one parent element.
src/components/partner/PartnerHeader.tsx(15,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/PartnerHeader.tsx(21,9): error TS2657: JSX expressions must have one parent element.
src/components/partner/PartnerHeader.tsx(30,7): error TS2657: JSX expressions must have one parent element.
src/components/partner/PartnerHeader.tsx(34,9): error TS2657: JSX expressions must have one parent element.
src/components/partner/RevenueAnalytics.tsx(68,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/RevenueAnalytics.tsx(75,7): error TS2657: JSX expressions must have one parent element.
src/components/partner/RevenueAnalytics.tsx(81,9): error TS2657: JSX expressions must have one parent element.
src/components/partner/RevenueAnalytics.tsx(86,9): error TS2657: JSX expressions must have one parent element.
src/components/partner/StatCard.tsx(12,5): error TS2657: JSX expressions must have one parent element.
src/components/partner/StatCard.tsx(17,9): error TS2657: JSX expressions must have one parent element.
src/components/partner/UpgradeBanner.tsx(14,5): error TS2657: JSX expressions must have one parent element.
src/components/PartnerRegistrationManager.tsx(21,5): error TS2657: JSX expressions must have one parent element.
src/components/Partners.tsx(29,5): error TS2657: JSX expressions must have one parent element.
src/components/Partners.tsx(40,13): error TS2657: JSX expressions must have one parent element.
src/components/Partners.tsx(53,9): error TS2657: JSX expressions must have one parent element.
src/components/payment/SecurePaymentForm.tsx(66,5): error TS2657: JSX expressions must have one parent element.
src/components/payment/SecurePaymentForm.tsx(86,11): error TS2657: JSX expressions must have one parent element.
src/components/payment/SecurePaymentForm.tsx(102,15): error TS2657: JSX expressions must have one parent element.
src/components/payment/SecurePaymentForm.tsx(121,15): error TS2657: JSX expressions must have one parent element.
src/components/payment/SecurePaymentForm.tsx(139,15): error TS2657: JSX expressions must have one parent element.
src/components/payment/SecurePaymentForm.tsx(158,15): error TS2657: JSX expressions must have one parent element.
src/components/payment/SecurePaymentForm.tsx(166,7): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(158,16): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(177,11): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(183,13): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(195,13): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(210,13): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(216,17): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(223,15): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(235,25): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(248,27): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(261,19): error TS2657: JSX expressions must have one parent element.
src/components/performance/PerformanceMonitor.tsx(273,25): error TS2657: JSX expressions must have one parent element.
src/components/PrivateRoute/PrivateRoute.tsx(17,7): error TS2657: JSX expressions must have one parent element.
src/components/professional/AnalyticsDashboard.tsx(35,5): error TS2657: JSX expressions must have one parent element.
src/components/professional/AnalyticsDashboard.tsx(50,9): error TS2657: JSX expressions must have one parent element.
src/components/professional/AnalyticsDashboard.tsx(62,9): error TS2657: JSX expressions must have one parent element.
src/components/professional/AnalyticsDashboard.tsx(68,15): error TS2657: JSX expressions must have one parent element.
src/components/professional/AnalyticsDashboard.tsx(84,9): error TS2657: JSX expressions must have one parent element.
src/components/professional/AnalyticsDashboard.tsx(96,9): error TS2657: JSX expressions must have one parent element.
src/components/professional/BookingManagement.tsx(45,5): error TS2657: JSX expressions must have one parent element.
src/components/professional/BookingManagement.tsx(50,9): error TS2657: JSX expressions must have one parent element.
src/components/professional/BookingManagement.tsx(64,17): error TS2657: JSX expressions must have one parent element.
src/components/professional/BookingManagement.tsx(77,19): error TS2657: JSX expressions must have one parent element.
src/components/professional/BookingManagement.tsx(84,21): error TS2657: JSX expressions must have one parent element.
src/components/professional/PerformanceMetrics.tsx(21,5): error TS2657: JSX expressions must have one parent element.
src/components/professional/PerformanceMetrics.tsx(27,11): error TS2657: JSX expressions must have one parent element.
src/components/professional/PerformanceMetrics.tsx(36,7): error TS2657: JSX expressions must have one parent element.
src/components/professional/PerformanceMetrics.tsx(41,11): error TS2657: JSX expressions must have one parent element.
src/components/professional/PerformanceMetrics.tsx(46,13): error TS2657: JSX expressions must have one parent element.
src/components/professional/PerformanceMetrics.tsx(54,7): error TS2657: JSX expressions must have one parent element.
src/components/professional/PerformanceMetrics.tsx(59,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(63,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(73,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(85,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(126,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(153,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(199,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(207,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(218,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(240,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(247,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(257,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(264,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(274,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/AchievementsDisplay.tsx(287,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActiveSessions.tsx(144,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActiveSessions.tsx(156,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActiveSessions.tsx(172,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActiveSessions.tsx(189,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActiveSessions.tsx(199,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(100,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(108,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(135,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(140,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(146,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(152,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(183,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(191,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(200,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ActivityFeed.tsx(207,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/AppPermissions.tsx(200,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/AppPermissions.tsx(218,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/AppPermissions.tsx(237,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/AppPermissions.tsx(249,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/AppPermissions.tsx(267,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/AppPermissions.tsx(274,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/AppPermissions.tsx(300,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/AppPermissions.tsx(306,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(163,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(181,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(201,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(215,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(223,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(230,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(257,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(284,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(289,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(300,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(308,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(314,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(325,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(339,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectedApps.tsx(355,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(75,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(92,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(131,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(171,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(194,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(198,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(265,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(273,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(281,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(301,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ConnectionsGrid.tsx(311,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(135,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(141,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(171,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(182,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(196,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(207,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(218,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(234,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(245,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(254,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(270,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(308,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/EditProfileDialog.tsx(344,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/LoginHistory.tsx(82,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/LoginHistory.tsx(87,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/LoginHistory.tsx(103,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/LoginHistory.tsx(111,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/LoginHistory.tsx(118,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/LoginHistory.tsx(126,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/LoginHistory.tsx(131,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/NotificationCenter.tsx(132,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/NotificationCenter.tsx(135,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/NotificationCenter.tsx(144,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/NotificationCenter.tsx(158,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/NotificationCenter.tsx(228,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/NotificationCenter.tsx(236,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/NotificationCenter.tsx(246,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/NotificationCenter.tsx(289,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/NotificationCenter.tsx(295,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(188,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(199,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(224,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(230,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(233,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(250,25): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(265,35): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(296,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(307,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(322,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/PrivacyPreferences.tsx(333,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(72,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(85,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(91,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(117,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(128,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(164,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(170,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(200,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(205,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileAnalytics.tsx(220,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileCard.tsx(50,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileCard.tsx(75,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileCard.tsx(86,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileCard.tsx(118,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileCard.tsx(133,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileDashboard.tsx(92,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileDashboard.tsx(133,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileDashboard.tsx(141,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileDashboard.tsx(151,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileDashboard.tsx(207,22): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileDashboard.tsx(226,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileDashboard.tsx(243,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(179,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(186,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(193,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(197,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(218,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(225,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(245,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(252,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(273,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(280,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(287,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(299,23): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(333,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(336,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(349,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(368,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(375,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(391,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(408,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(412,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(429,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/ProfileSettings.tsx(437,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/RetreatHistory.tsx(29,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/RetreatHistory.tsx(35,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/RetreatHistory.tsx(40,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/RetreatHistory.tsx(51,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecureProfileForm.tsx(59,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecureProfileForm.tsx(74,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecureProfileForm.tsx(79,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecureProfileForm.tsx(93,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecureProfileForm.tsx(106,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecureProfileForm.tsx(122,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecureProfileForm.tsx(135,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecureProfileForm.tsx(143,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityActivityHistory.tsx(222,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityActivityHistory.tsx(229,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityActivityHistory.tsx(238,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityActivityHistory.tsx(254,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityActivityHistory.tsx(273,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityActivityHistory.tsx(284,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityActivityHistory.tsx(305,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityActivityHistory.tsx(328,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(201,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(218,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(261,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(272,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(279,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(286,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(293,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(318,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(341,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(347,23): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(359,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAlerts.tsx(371,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(179,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(195,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(207,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(216,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(238,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(243,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(259,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(272,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(285,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(306,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(325,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(348,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(364,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(378,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityAudit.tsx(392,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(175,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(198,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(222,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(229,23): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(235,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(251,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(259,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(265,19): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(285,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(307,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(327,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(343,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(359,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityKeys.tsx(365,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNavigation.tsx(158,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNavigation.tsx(164,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNavigation.tsx(182,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(92,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(103,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(107,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(112,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(117,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(121,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(132,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(139,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(158,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecurityNotifications.tsx(177,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecuritySettings.tsx(67,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecuritySettings.tsx(78,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecuritySettings.tsx(86,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecuritySettings.tsx(100,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecuritySettings.tsx(107,13): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecuritySettings.tsx(136,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/SecuritySettings.tsx(163,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/TrustedDevices.tsx(178,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/TrustedDevices.tsx(192,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/TrustedDevices.tsx(205,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/TrustedDevices.tsx(215,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/TrustedDevices.tsx(243,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/TrustedDevices.tsx(271,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(101,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(108,17): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(127,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(133,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(153,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(167,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(179,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(197,11): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(212,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(216,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(233,21): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(250,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(254,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(274,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/UserProfile.tsx(278,15): error TS2657: JSX expressions must have one parent element.
src/components/profile/WellnessPreferences.tsx(68,5): error TS2657: JSX expressions must have one parent element.
src/components/profile/WellnessPreferences.tsx(72,7): error TS2657: JSX expressions must have one parent element.
src/components/profile/WellnessPreferences.tsx(91,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/WellnessPreferences.tsx(109,9): error TS2657: JSX expressions must have one parent element.
src/components/profile/WellnessPreferences.tsx(131,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/InstallPrompt.tsx(70,5): error TS2657: JSX expressions must have one parent element.
src/components/pwa/InstallPrompt.tsx(85,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/InstallPrompt.tsx(93,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/InstallPrompt.tsx(103,11): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NetworkStatus.tsx(167,5): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NetworkStatus.tsx(197,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NetworkStatus.tsx(202,13): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NetworkStatus.tsx(207,13): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NetworkStatus.tsx(212,13): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NetworkStatus.tsx(216,11): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NotificationManager.tsx(73,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NotificationManager.tsx(78,11): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NotificationManager.tsx(100,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NotificationManager.tsx(126,13): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NotificationManager.tsx(132,13): error TS2657: JSX expressions must have one parent element.
src/components/pwa/NotificationManager.tsx(138,13): error TS2657: JSX expressions must have one parent element.
src/components/pwa/PWAController.tsx(63,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/PWAController.tsx(68,11): error TS2657: JSX expressions must have one parent element.
src/components/pwa/PWAController.tsx(90,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/PWAController.tsx(108,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/PWAController.tsx(116,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/UpdatePrompt.tsx(38,5): error TS2657: JSX expressions must have one parent element.
src/components/pwa/UpdatePrompt.tsx(53,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/UpdatePrompt.tsx(61,9): error TS2657: JSX expressions must have one parent element.
src/components/pwa/UpdatePrompt.tsx(71,11): error TS2657: JSX expressions must have one parent element.
src/components/RealTimeMetrics.tsx(86,5): error TS2657: JSX expressions must have one parent element.
src/components/RealTimeMetrics.tsx(111,5): error TS2657: JSX expressions must have one parent element.
src/components/RealTimeMetrics.tsx(115,11): error TS2657: JSX expressions must have one parent element.
src/components/RealTimeMetrics.tsx(135,5): error TS2657: JSX expressions must have one parent element.
src/components/RealTimeMetrics.tsx(142,7): error TS2657: JSX expressions must have one parent element.
src/components/referral/ReferralProgram.tsx(45,7): error TS2657: JSX expressions must have one parent element.
src/components/referral/ReferralProgram.tsx(52,11): error TS2657: JSX expressions must have one parent element.
src/components/referral/ReferralProgram.tsx(56,11): error TS2657: JSX expressions must have one parent element.
src/components/referral/ReferralProgram.tsx(65,11): error TS2657: JSX expressions must have one parent element.
src/components/referral/ReferralProgram.tsx(92,11): error TS2657: JSX expressions must have one parent element.
src/components/referral/ReferralProgram.tsx(97,15): error TS2657: JSX expressions must have one parent element.
src/components/referral/ReferralProgram.tsx(110,11): error TS2657: JSX expressions must have one parent element.
src/components/referral/ReferralProgram.tsx(113,13): error TS2657: JSX expressions must have one parent element.
src/components/referral/ReferralProgram.tsx(120,15): error TS2657: JSX expressions must have one parent element.
src/components/RetreatBooking/RetreatBookingForm.tsx(39,5): error TS2657: JSX expressions must have one parent element.
src/components/RetreatBooking/RetreatBookingForm.tsx(56,9): error TS2657: JSX expressions must have one parent element.
src/components/RetreatBooking/RetreatBookingForm.tsx(72,7): error TS2657: JSX expressions must have one parent element.
src/components/RetreatBooking/RetreatBookingForm.tsx(88,7): error TS2657: JSX expressions must have one parent element.
src/components/RetreatCategories.tsx(33,5): error TS2657: JSX expressions must have one parent element.
src/components/RetreatCategories.tsx(44,13): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(11,5): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(17,9): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(25,11): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(29,13): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(41,15): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(43,15): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(53,15): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(55,15): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(65,15): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(67,15): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(78,15): error TS2657: JSX expressions must have one parent element.
src/components/RetreatFilters.tsx(80,15): error TS2657: JSX expressions must have one parent element.
src/components/RetreatHighlights.tsx(25,5): error TS2657: JSX expressions must have one parent element.
src/components/RetreatHighlights.tsx(37,13): error TS2657: JSX expressions must have one parent element.
src/components/RetreatListHeader.tsx(17,5): error TS2657: JSX expressions must have one parent element.
src/components/RetreatListHeader.tsx(24,7): error TS2657: JSX expressions must have one parent element.
src/components/RetreatMap.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/components/RetreatMap.tsx(16,9): error TS2657: JSX expressions must have one parent element.
src/components/RetreatProMatcherManager.tsx(31,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/BookingModal.tsx(137,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/BookingModal.tsx(177,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/BookingModal.tsx(201,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/BookingModal.tsx(237,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/BookingModal.tsx(250,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/BookingModal.tsx(259,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/PaymentDetails.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/PaymentDetails.tsx(60,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/PaymentDetails.tsx(65,9): error TS2657: JSX expressions must have one parent element.
src/components/retreats/PaymentDetails.tsx(74,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/PaymentDetails.tsx(80,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/PaymentDetails.tsx(86,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/PaymentDetails.tsx(110,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatCard.tsx(50,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/components/retreats/RetreatCard.tsx(76,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatCard.tsx(112,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatCard.tsx(120,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatCard.tsx(127,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatCard.tsx(146,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatCard.tsx(155,9): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(54,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(74,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(94,15): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(102,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(111,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(127,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(138,17): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(147,17): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(162,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(170,9): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(176,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetail.tsx(185,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(30,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(34,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(116,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(124,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(135,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(157,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(163,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(168,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(175,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(180,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(207,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatDetails.tsx(227,13): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatFilters.tsx(63,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatFilters.tsx(68,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatFilters.tsx(83,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatFilters.tsx(99,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatGrid.tsx(36,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatGrid.tsx(47,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatGrid.tsx(60,9): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatList.tsx(94,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatList.tsx(141,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatList.tsx(158,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatList.tsx(166,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatList.tsx(174,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatList.tsx(179,9): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatList.tsx(189,15): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatList.tsx(216,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/RetreatList.tsx(233,9): error TS2657: JSX expressions must have one parent element.
src/components/retreats/ReviewSection.tsx(84,5): error TS2657: JSX expressions must have one parent element.
src/components/retreats/ReviewSection.tsx(96,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/ReviewSection.tsx(104,17): error TS2657: JSX expressions must have one parent element.
src/components/retreats/ReviewSection.tsx(108,19): error TS2657: JSX expressions must have one parent element.
src/components/retreats/ReviewSection.tsx(136,11): error TS2657: JSX expressions must have one parent element.
src/components/retreats/ReviewSection.tsx(143,15): error TS2657: JSX expressions must have one parent element.
src/components/retreats/ReviewSection.tsx(153,23): error TS2657: JSX expressions must have one parent element.
src/components/retreats/ReviewSection.tsx(169,7): error TS2657: JSX expressions must have one parent element.
src/components/retreats/ReviewSection.tsx(196,9): error TS2657: JSX expressions must have one parent element.
src/components/RetreatSearch.tsx(15,5): error TS2657: JSX expressions must have one parent element.
src/components/RetreatSearch.tsx(35,13): error TS2657: JSX expressions must have one parent element.
src/components/RetreatSearch.tsx(50,13): error TS2657: JSX expressions must have one parent element.
src/components/RetreatSearch.tsx(72,13): error TS2657: JSX expressions must have one parent element.
src/components/RetreatSort.tsx(10,5): error TS2657: JSX expressions must have one parent element.
src/components/RetreatStats.tsx(25,5): error TS2657: JSX expressions must have one parent element.
src/components/RetreatStats.tsx(29,13): error TS2657: JSX expressions must have one parent element.
src/components/RetreatStreamManager.tsx(37,5): error TS2657: JSX expressions must have one parent element.
src/components/reviews/ReviewSystem.tsx(51,5): error TS2657: JSX expressions must have one parent element.
src/components/reviews/ReviewSystem.tsx(57,9): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(77,5): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(83,13): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(103,7): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(117,7): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(122,9): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(139,7): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(154,7): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(169,7): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(179,9): error TS2657: JSX expressions must have one parent element.
src/components/reviews/SecureReviewForm.tsx(189,7): error TS2657: JSX expressions must have one parent element.
src/components/rewards/RewardsCatalog.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/rewards/RewardsCatalog.tsx(47,11): error TS2657: JSX expressions must have one parent element.
src/components/rewards/RewardsCatalog.tsx(56,13): error TS2657: JSX expressions must have one parent element.
src/components/RouteErrorBoundary.tsx(29,9): error TS2657: JSX expressions must have one parent element.
src/components/RouteErrorBoundary.tsx(40,13): error TS2657: JSX expressions must have one parent element.
src/components/routes/PrivateRoutes.tsx(16,7): error TS2657: JSX expressions must have one parent element.
src/components/routes/PrivateRoutes.tsx(25,7): error TS2657: JSX expressions must have one parent element.
src/components/routing/AuthRoute.tsx(20,7): error TS2657: JSX expressions must have one parent element.
src/components/routing/LazyRoute.tsx(10,3): error TS2657: JSX expressions must have one parent element.
src/components/routing/LazyRoute.tsx(24,9): error TS2657: JSX expressions must have one parent element.
src/components/routing/RoleBasedRoute.tsx(36,7): error TS2657: JSX expressions must have one parent element.
src/components/search/GlobalSearch.tsx(98,9): error TS2657: JSX expressions must have one parent element.
src/components/search/GlobalSearch.tsx(118,9): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchBar.tsx(97,5): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchBar.tsx(120,11): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchBar.tsx(126,13): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchBar.tsx(140,7): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchBar.tsx(147,13): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchBar.tsx(168,11): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchBar.tsx(184,11): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchBar.tsx(188,13): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchBar.tsx(202,11): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchResults.tsx(43,3): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchResults.tsx(66,5): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchResults.tsx(88,9): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchResults.tsx(177,11): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchResults.tsx(187,7): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchResults.tsx(197,5): error TS2657: JSX expressions must have one parent element.
src/components/search/SearchResults.tsx(201,11): error TS2657: JSX expressions must have one parent element.
src/components/SearchTransportManager.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/components/SearchTransportManager.tsx(51,7): error TS2657: JSX expressions must have one parent element.
src/components/SecureFileUpload.tsx(45,5): error TS2657: JSX expressions must have one parent element.
src/components/security/AuditLog.tsx(22,5): error TS2657: JSX expressions must have one parent element.
src/components/security/AuditLog.tsx(40,11): error TS2657: JSX expressions must have one parent element.
src/components/security/AuditLog.tsx(49,19): error TS2657: JSX expressions must have one parent element.
src/components/security/DashboardDAST.tsx(27,5): error TS2657: JSX expressions must have one parent element.
src/components/security/DashboardDAST.tsx(44,13): error TS2657: JSX expressions must have one parent element.
src/components/security/DeviceManager.tsx(45,5): error TS2657: JSX expressions must have one parent element.
src/components/security/DeviceManager.tsx(53,13): error TS2657: JSX expressions must have one parent element.
src/components/security/DeviceManager.tsx(59,17): error TS2657: JSX expressions must have one parent element.
src/components/security/DeviceManager.tsx(83,17): error TS2657: JSX expressions must have one parent element.
src/components/security/DeviceManager.tsx(98,9): error TS2657: JSX expressions must have one parent element.
src/components/security/InactivityMonitor.tsx(45,5): error TS2657: JSX expressions must have one parent element.
src/components/security/InactivityMonitor.tsx(53,9): error TS2657: JSX expressions must have one parent element.
src/components/security/PasswordManager.tsx(34,5): error TS2657: JSX expressions must have one parent element.
src/components/security/PasswordManager.tsx(40,11): error TS2657: JSX expressions must have one parent element.
src/components/security/PasswordManager.tsx(44,9): error TS2657: JSX expressions must have one parent element.
src/components/security/PasswordManager.tsx(69,11): error TS2657: JSX expressions must have one parent element.
src/components/security/PasswordManager.tsx(96,17): error TS2657: JSX expressions must have one parent element.
src/components/security/PasswordManager.tsx(115,15): error TS2657: JSX expressions must have one parent element.
src/components/security/RecentActivityList.tsx(95,7): error TS2657: JSX expressions must have one parent element.
src/components/security/RecentActivityList.tsx(107,11): error TS2657: JSX expressions must have one parent element.
src/components/security/RecentActivityList.tsx(120,17): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityAlertsList.tsx(106,7): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityAlertsList.tsx(120,11): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityAlertsList.tsx(147,21): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityAlertsList.tsx(159,17): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityAlertsList.tsx(171,13): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityChart.tsx(28,5): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityDashboard.tsx(31,5): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityDashboard.tsx(38,7): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityDashboard.tsx(46,7): error TS2657: JSX expressions must have one parent element.
src/components/security/SecurityDashboard.tsx(61,15): error TS2657: JSX expressions must have one parent element.
src/components/security/SecuritySettings.tsx(44,5): error TS2657: JSX expressions must have one parent element.
src/components/security/SecuritySettings.tsx(63,15): error TS2657: JSX expressions must have one parent element.
src/components/security/SecuritySettings.tsx(69,17): error TS2657: JSX expressions must have one parent element.
src/components/security/SecuritySettings.tsx(101,15): error TS2657: JSX expressions must have one parent element.
src/components/security/SecuritySettings.tsx(110,23): error TS2657: JSX expressions must have one parent element.
src/components/security/SecuritySettings.tsx(122,21): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorSetup.tsx(69,11): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorSetup.tsx(85,11): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorSetup.tsx(95,15): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorSetup.tsx(125,11): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorSetup.tsx(132,13): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorSetup.tsx(173,7): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorSetup.tsx(179,11): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorSetup.tsx(182,11): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorSetup.tsx(187,9): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorVerify.tsx(44,5): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorVerify.tsx(63,15): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorVerify.tsx(89,15): error TS2657: JSX expressions must have one parent element.
src/components/security/TwoFactorVerify.tsx(109,7): error TS2657: JSX expressions must have one parent element.
src/components/security/VulnerabilityTable.tsx(62,5): error TS2657: JSX expressions must have one parent element.
src/components/security/VulnerabilityTable.tsx(66,9): error TS2657: JSX expressions must have one parent element.
src/components/security/VulnerabilityTable.tsx(71,11): error TS2657: JSX expressions must have one parent element.
src/components/security/VulnerabilityTable.tsx(132,19): error TS2657: JSX expressions must have one parent element.
src/components/security/VulnerabilityTable.tsx(149,9): error TS2657: JSX expressions must have one parent element.
src/components/security/VulnerabilityTable.tsx(167,15): error TS2657: JSX expressions must have one parent element.
src/components/security/VulnerabilityTable.tsx(184,15): error TS2657: JSX expressions must have one parent element.
src/components/security/VulnerabilityTable.tsx(189,17): error TS2657: JSX expressions must have one parent element.
src/components/security/VulnerabilityTable.tsx(195,13): error TS2657: JSX expressions must have one parent element.
src/components/security/Web3Route.tsx(15,7): error TS2657: JSX expressions must have one parent element.
src/components/SecurityDashboard.tsx(14,5): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Breadcrumbs.tsx(49,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(111,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(133,5): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(139,11): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(163,11): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(169,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(179,11): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(185,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(194,11): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(204,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(212,11): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(218,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(230,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(235,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(252,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Dashboard/SEODashboard.tsx(259,21): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(89,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(104,5): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(119,9): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(125,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(132,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(137,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(142,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(154,13): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(159,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(170,23): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(188,13): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(193,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(204,23): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(222,13): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(227,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/GoogleSearchConsole.tsx(237,23): error TS2657: JSX expressions must have one parent element.
src/components/SEO/MetaTags.tsx(79,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/MetaTags.tsx(84,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/MetaTags.tsx(93,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/MetaTags.tsx(99,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/PageMetadata.tsx(34,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/PageMetadata.tsx(41,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/PageMetadata.tsx(51,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/PagePerformance.tsx(102,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/PagePerformance.tsx(113,5): error TS2657: JSX expressions must have one parent element.
src/components/SEO/PagePerformance.tsx(121,9): error TS2657: JSX expressions must have one parent element.
src/components/SEO/PagePerformance.tsx(129,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/PagePerformance.tsx(135,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEO.tsx(30,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEO.tsx(35,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEO.tsx(42,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(80,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(99,5): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(109,11): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(120,9): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(128,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(144,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(160,13): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(167,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(171,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(176,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(180,17): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(192,13): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(199,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(212,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/SEODashboard.tsx(227,13): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOFilters.tsx(35,5): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOFilters.tsx(47,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOFilters.tsx(57,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOStats.tsx(50,9): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(139,5): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(150,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(174,11): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(186,19): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(219,21): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(228,21): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(236,19): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(250,7): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(266,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(273,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(280,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(287,15): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(297,9): error TS2657: JSX expressions must have one parent element.
src/components/SEO/Suggestions/SEOSuggestions.tsx(305,7): error TS2657: JSX expressions must have one parent element.
src/components/settings/NotificationsSettings.tsx(28,5): error TS2657: JSX expressions must have one parent element.
src/components/settings/NotificationsSettings.tsx(32,7): error TS2657: JSX expressions must have one parent element.
src/components/settings/NotificationsSettings.tsx(48,9): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(68,5): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(80,11): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(93,17): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(104,13): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(116,17): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(127,13): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(135,13): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(143,9): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(151,11): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecureSettingsForm.tsx(165,7): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecuritySettings.tsx(33,5): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecuritySettings.tsx(37,7): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecuritySettings.tsx(50,7): error TS2657: JSX expressions must have one parent element.
src/components/settings/SecuritySettings.tsx(87,9): error TS2657: JSX expressions must have one parent element.
src/components/settings/TwoFactorSettings.tsx(82,5): error TS2657: JSX expressions must have one parent element.
src/components/settings/TwoFactorSettings.tsx(117,11): error TS2657: JSX expressions must have one parent element.
src/components/settings/TwoFactorSettings.tsx(123,17): error TS2657: JSX expressions must have one parent element.
src/components/settings/TwoFactorSettings.tsx(129,15): error TS2657: JSX expressions must have one parent element.
src/components/settings/TwoFactorSettings.tsx(144,11): error TS2657: JSX expressions must have one parent element.
src/components/settings/TwoFactorSettings.tsx(161,11): error TS2657: JSX expressions must have one parent element.
src/components/settings/TwoFactorSettings.tsx(182,11): error TS2657: JSX expressions must have one parent element.
src/components/shared/Map.tsx(98,5): error TS2657: JSX expressions must have one parent element.
src/components/shared/Map.tsx(108,13): error TS2657: JSX expressions must have one parent element.
src/components/shared/Map.tsx(117,11): error TS2657: JSX expressions must have one parent element.
src/components/shared/SharedHeader.tsx(106,9): error TS2657: JSX expressions must have one parent element.
src/components/shared/SharedHeader.tsx(115,13): error TS2657: JSX expressions must have one parent element.
src/components/shared/SharedHeader.tsx(127,11): error TS2657: JSX expressions must have one parent element.
src/components/shared/SharedHeader.tsx(132,11): error TS2657: JSX expressions must have one parent element.
src/components/skeletons.tsx(11,3): error TS2657: JSX expressions must have one parent element.
src/components/skeletons.tsx(19,7): error TS2657: JSX expressions must have one parent element.
src/components/skeletons.tsx(27,3): error TS2657: JSX expressions must have one parent element.
src/components/skeletons.tsx(34,5): error TS2657: JSX expressions must have one parent element.
src/components/skeletons.tsx(38,7): error TS2657: JSX expressions must have one parent element.
src/components/skeletons.tsx(46,3): error TS2657: JSX expressions must have one parent element.
src/components/skeletons.tsx(51,7): error TS2657: JSX expressions must have one parent element.
src/components/skeletons.tsx(66,7): error TS2657: JSX expressions must have one parent element.
src/components/skeletons.tsx(79,7): error TS2657: JSX expressions must have one parent element.
src/components/social/CreatePost.tsx(40,5): error TS2657: JSX expressions must have one parent element.
src/components/social/CreatePost.tsx(49,9): error TS2657: JSX expressions must have one parent element.
src/components/social/CreatePost.tsx(76,17): error TS2657: JSX expressions must have one parent element.
src/components/social/CreatePost.tsx(97,11): error TS2657: JSX expressions must have one parent element.
src/components/social/CreatePost.tsx(112,15): error TS2657: JSX expressions must have one parent element.
src/components/social/CreatePost.tsx(149,11): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/CreatePost.tsx(118,5): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/CreatePost.tsx(126,11): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/CreatePost.tsx(144,15): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/CreatePost.tsx(148,21): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/CreatePost.tsx(173,13): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/CreatePost.tsx(196,17): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/CreatePost.tsx(204,17): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/CreatePost.tsx(207,17): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/CreatePost.tsx(217,21): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedFilters.tsx(113,5): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedFilters.tsx(124,15): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedFilters.tsx(137,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedFilters.tsx(189,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedFilters.tsx(216,11): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedItem.tsx(93,5): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedItem.tsx(127,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedItem.tsx(159,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedItem.tsx(165,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedItem.tsx(168,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedItem.tsx(173,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedItem.tsx(178,15): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedItem.tsx(189,19): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedItem.tsx(202,11): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedList.tsx(28,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedList.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedList.tsx(45,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedList.tsx(51,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/FeedList.tsx(59,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(91,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(92,32): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(97,13): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(118,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(136,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(151,5): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(155,11): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(162,11): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(176,13): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(216,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(224,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(230,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/RecommendedPosts.tsx(236,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/SocialFeedContainer.tsx(144,5): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/SocialFeedContainer.tsx(151,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/SocialFeedContainer.tsx(183,13): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/SocialFeedContainer.tsx(189,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/SocialFeedContainer.tsx(194,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/TrendingPosts.tsx(110,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/TrendingPosts.tsx(111,32): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/TrendingPosts.tsx(116,13): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/TrendingPosts.tsx(136,5): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/TrendingPosts.tsx(141,11): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/TrendingPosts.tsx(171,11): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/TrendingPosts.tsx(181,13): error TS2657: JSX expressions must have one parent element.
src/components/social/Feed/TrendingPosts.tsx(201,17): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(205,7): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(215,7): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(218,11): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(234,15): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(241,13): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(247,13): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(257,13): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(275,15): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(301,11): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(308,17): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(323,15): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(347,15): error TS2657: JSX expressions must have one parent element.
src/components/social/LiveStream.tsx(365,15): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/CreatePollDialog.tsx(123,5): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/CreatePollDialog.tsx(144,11): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/CreatePollDialog.tsx(155,15): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/CreatePollDialog.tsx(169,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/CreatePollDialog.tsx(183,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/CreatePollDialog.tsx(201,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/CreatePollDialog.tsx(227,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/PollComponent.tsx(104,7): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/PollComponent.tsx(140,15): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/PollComponent.tsx(157,5): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/PollComponent.tsx(166,15): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/PollComponent.tsx(179,13): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/PollComponent.tsx(185,9): error TS2657: JSX expressions must have one parent element.
src/components/social/Poll/PollComponent.tsx(189,9): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(38,11): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(45,15): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(53,9): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(60,9): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(72,7): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(93,9): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(100,13): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(109,13): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(113,11): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(120,11): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(123,17): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(133,19): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(145,21): error TS2657: JSX expressions must have one parent element.
src/components/social/PostCard.tsx(155,13): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(166,7): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(175,9): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(181,7): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(207,9): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(218,17): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(246,13): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(248,30): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(270,15): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(273,19): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(285,23): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialFeed.tsx(303,7): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialSidebar.tsx(31,7): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialSidebar.tsx(40,13): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialSidebar.tsx(46,15): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialSidebar.tsx(69,11): error TS2657: JSX expressions must have one parent element.
src/components/social/SocialSidebar.tsx(72,13): error TS2657: JSX expressions must have one parent element.
src/components/SocialManager.tsx(38,5): error TS2657: JSX expressions must have one parent element.
src/components/storage/StorageManager.tsx(4,3): error TS2657: JSX expressions must have one parent element.
src/components/SyncStatusIndicator.tsx(43,5): error TS2657: JSX expressions must have one parent element.
src/components/TaskForm.tsx(48,5): error TS2657: JSX expressions must have one parent element.
src/components/TaskForm.tsx(61,9): error TS2657: JSX expressions must have one parent element.
src/components/TaskForm.tsx(75,9): error TS2657: JSX expressions must have one parent element.
src/components/TaskForm.tsx(89,9): error TS2657: JSX expressions must have one parent element.
src/components/templates/DashboardLayout/DashboardLayout.tsx(72,5): error TS2657: JSX expressions must have one parent element.
src/components/templates/DashboardLayout/DashboardLayout.tsx(98,9): error TS2657: JSX expressions must have one parent element.
src/components/templates/Hero.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/components/templates/Hero.tsx(13,7): error TS2657: JSX expressions must have one parent element.
src/components/templates/Hero.tsx(18,9): error TS2657: JSX expressions must have one parent element.
src/components/templates/Hero.tsx(22,9): error TS2657: JSX expressions must have one parent element.
src/components/templates/Hero.tsx(29,11): error TS2657: JSX expressions must have one parent element.
src/components/templates/LiveRetreats.tsx(76,5): error TS2657: JSX expressions must have one parent element.
src/components/templates/LiveRetreats.tsx(97,13): error TS2657: JSX expressions must have one parent element.
src/components/templates/LiveRetreats.tsx(113,19): error TS2657: JSX expressions must have one parent element.
src/components/templates/LiveRetreats.tsx(123,17): error TS2657: JSX expressions must have one parent element.
src/components/templates/LiveRetreats.tsx(130,21): error TS2657: JSX expressions must have one parent element.
src/components/templates/LiveRetreats.tsx(137,19): error TS2657: JSX expressions must have one parent element.
src/components/templates/LiveRetreats.tsx(153,13): error TS2657: JSX expressions must have one parent element.
src/components/templates/LiveRetreats.tsx(156,15): error TS2657: JSX expressions must have one parent element.
src/components/templates/LiveRetreats.tsx(163,17): error TS2657: JSX expressions must have one parent element.
src/components/Testimonials.tsx(33,5): error TS2657: JSX expressions must have one parent element.
src/components/Testimonials.tsx(44,13): error TS2657: JSX expressions must have one parent element.
src/components/Testimonials.tsx(55,15): error TS2657: JSX expressions must have one parent element.
src/components/theme/ThemeSelector.tsx(32,9): error TS2657: JSX expressions must have one parent element.
src/components/TokenDistribution.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/components/TokenDistribution.tsx(33,5): error TS2657: JSX expressions must have one parent element.
src/components/TokenDistribution.tsx(38,7): error TS2657: JSX expressions must have one parent element.
src/components/TokenDistribution.tsx(83,13): error TS2657: JSX expressions must have one parent element.
src/components/TokenEcosystem.tsx(30,5): error TS2657: JSX expressions must have one parent element.
src/components/TokenEcosystem.tsx(38,11): error TS2657: JSX expressions must have one parent element.
src/components/TokenMetrics.tsx(42,5): error TS2657: JSX expressions must have one parent element.
src/components/TokenMetrics.tsx(50,11): error TS2657: JSX expressions must have one parent element.
src/components/TokenMetrics.tsx(72,15): error TS2657: JSX expressions must have one parent element.
src/components/transitions/PageTransition.tsx(49,5): error TS2657: JSX expressions must have one parent element.
src/components/TransportBookingManager.tsx(29,5): error TS2657: JSX expressions must have one parent element.
src/components/ui/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/components/ui/LoadingSpinner.tsx(10,5): error TS2657: JSX expressions must have one parent element.
src/components/ui/OptimizedImage.tsx(65,9): error TS2657: JSX expressions must have one parent element.
src/components/ui/Sidebar.tsx(24,5): error TS2657: JSX expressions must have one parent element.
src/components/ui/Skeletons.tsx(5,3): error TS2657: JSX expressions must have one parent element.
src/components/ui/Skeletons.tsx(17,7): error TS2657: JSX expressions must have one parent element.
src/components/ui/Skeletons.tsx(27,3): error TS2657: JSX expressions must have one parent element.
src/components/ui/Skeletons.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/components/ui/Skeletons.tsx(43,7): error TS2657: JSX expressions must have one parent element.
src/components/ui/Skeletons.tsx(51,3): error TS2657: JSX expressions must have one parent element.
src/components/ui/Skeletons.tsx(58,7): error TS2657: JSX expressions must have one parent element.
src/components/ui/Skeletons.tsx(75,7): error TS2657: JSX expressions must have one parent element.
src/components/ui/Skeletons.tsx(94,7): error TS2657: JSX expressions must have one parent element.
src/components/ui/Transitions.tsx(79,3): error TS2657: JSX expressions must have one parent element.
src/components/video/GroupVideoCall.tsx(14,5): error TS2657: JSX expressions must have one parent element.
src/components/video/GroupVideoCall.tsx(20,9): error TS2657: JSX expressions must have one parent element.
src/components/video/GroupVideoCall.tsx(34,13): error TS2657: JSX expressions must have one parent element.
src/components/video/VideoCall.tsx(96,5): error TS2657: JSX expressions must have one parent element.
src/components/video/VideoCall.tsx(104,11): error TS2657: JSX expressions must have one parent element.
src/components/video/VideoCall.tsx(114,13): error TS2657: JSX expressions must have one parent element.
src/components/video/VideoCall.tsx(128,11): error TS2657: JSX expressions must have one parent element.
src/components/video/VideoCall.tsx(159,11): error TS2657: JSX expressions must have one parent element.
src/components/video/VideoCall.tsx(164,13): error TS2657: JSX expressions must have one parent element.
src/components/VideoManager.tsx(106,5): error TS2657: JSX expressions must have one parent element.
src/components/VideoManager.tsx(109,7): error TS2657: JSX expressions must have one parent element.
src/components/VideoManager.tsx(120,7): error TS2657: JSX expressions must have one parent element.
src/components/VideoManager.tsx(123,11): error TS2657: JSX expressions must have one parent element.
src/components/VideoManager.tsx(147,9): error TS2657: JSX expressions must have one parent element.
src/components/VideoManager.tsx(165,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityCard.tsx(89,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityCard.tsx(94,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityCard.tsx(100,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityCard.tsx(108,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityCard.tsx(114,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityCard.tsx(121,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityCard.tsx(131,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityFilters.tsx(69,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityFilters.tsx(75,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityFilters.tsx(90,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityFilters.tsx(101,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityFilters.tsx(118,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityFilters.tsx(129,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityManager.tsx(117,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityManager.tsx(133,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityManager.tsx(146,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityManager.tsx(150,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityManager.tsx(154,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ActivityManager.tsx(159,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/BreakoutRoomActivity.tsx(112,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/BreakoutRoomActivity.tsx(125,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/BreakoutRoomActivity.tsx(136,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/BreakoutRoomActivity.tsx(144,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/BreakoutRoomActivity.tsx(204,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/BreakoutRoomActivity.tsx(225,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/BreakoutRoomActivity.tsx(241,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/BreakoutRoomActivity.tsx(252,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(107,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(124,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(137,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(159,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(176,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(185,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(195,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(205,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(215,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(224,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(241,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(255,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(268,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(281,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(294,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(307,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(320,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(333,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/CreateActivityDialog.tsx(351,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ExerciseActivity.tsx(243,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ExerciseActivity.tsx(299,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ExerciseActivity.tsx(312,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ExerciseActivity.tsx(336,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/MeditationActivity.tsx(131,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/MeditationActivity.tsx(145,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/MeditationActivity.tsx(182,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/MeditationActivity.tsx(194,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/MeditationActivity.tsx(220,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileActivityBase.tsx(52,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileActivityBase.tsx(61,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(133,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(145,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(155,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(159,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(177,25): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(182,23): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(221,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(232,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(264,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(278,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileBreakoutRoom.tsx(286,21): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(181,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(201,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(209,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(217,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(220,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(229,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(261,27): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(297,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(314,21): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(359,21): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(369,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(385,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(402,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(433,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(450,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(453,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(457,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileChat.tsx(473,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(137,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(146,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(154,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(170,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(185,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(191,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(195,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(205,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(214,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileEventViewer.tsx(261,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileNetworking.tsx(112,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileNetworking.tsx(154,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileNetworking.tsx(168,23): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileNetworking.tsx(192,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileNetworking.tsx(201,21): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileNetworking.tsx(211,21): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileNetworking.tsx(221,21): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(139,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(153,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(162,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(200,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(216,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(229,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(247,19): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(271,23): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(281,25): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(295,25): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileVirtualExpo.tsx(303,25): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWhiteboard.tsx(270,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWhiteboard.tsx(290,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWhiteboard.tsx(342,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWhiteboard.tsx(352,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWhiteboard.tsx(355,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWhiteboard.tsx(358,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWhiteboard.tsx(361,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWorkshop.tsx(83,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWorkshop.tsx(122,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWorkshop.tsx(134,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWorkshop.tsx(149,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWorkshop.tsx(172,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWorkshop.tsx(185,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/Mobile/MobileWorkshop.tsx(198,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/PollActivity.tsx(82,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/PollActivity.tsx(102,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/PollActivity.tsx(121,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/PollActivity.tsx(135,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/PollActivity.tsx(146,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/PollActivity.tsx(153,21): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/PollActivity.tsx(175,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/PollActivity.tsx(200,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/QuizActivity.tsx(77,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/QuizActivity.tsx(90,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/QuizActivity.tsx(109,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/QuizActivity.tsx(130,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/QuizActivity.tsx(184,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/QuizActivity.tsx(207,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ThreeDActivity.tsx(264,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ThreeDActivity.tsx(285,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ThreeDActivity.tsx(296,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ThreeDActivity.tsx(306,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ThreeDActivity.tsx(320,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ThreeDActivity.tsx(325,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/ThreeDActivity.tsx(333,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(248,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(279,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(291,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(296,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(302,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(307,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(314,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(320,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(334,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(353,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(358,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(365,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(373,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(379,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/Activities/WhiteboardActivity.tsx(416,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(94,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(143,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(171,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(176,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(182,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(193,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(308,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(325,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(337,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(344,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(360,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(395,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(401,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(406,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/EventChat.tsx(429,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/InteractiveWorkshop.tsx(88,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/InteractiveWorkshop.tsx(109,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/InteractiveWorkshop.tsx(186,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/InteractiveWorkshop.tsx(190,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/InteractiveWorkshop.tsx(208,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/InteractiveWorkshop.tsx(256,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/LivestreamComponent.tsx(140,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/LivestreamComponent.tsx(151,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/LivestreamComponent.tsx(165,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/LivestreamComponent.tsx(176,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/LivestreamComponent.tsx(190,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/LivestreamComponent.tsx(218,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/LivestreamComponent.tsx(221,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/LivestreamComponent.tsx(236,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/LivestreamComponent.tsx(250,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(81,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(88,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(111,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(132,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(140,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(148,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(157,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(169,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(177,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(184,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(191,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(294,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(314,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(331,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ParticipantsList.tsx(340,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(118,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(124,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(131,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(141,9): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(146,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(169,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(173,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(176,17): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(186,21): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(192,21): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(263,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(280,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(292,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(305,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/AIRecommendations.tsx(338,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(166,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(181,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(192,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(213,23): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(219,27): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(228,27): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(242,27): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(249,29): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(255,29): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(277,7): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(286,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(301,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(313,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(327,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/ScheduleBuilder/ScheduleBuilder.tsx(338,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(92,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(120,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(227,5): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(245,11): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(253,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(262,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(270,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(281,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(290,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(321,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(332,15): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(339,13): error TS2657: JSX expressions must have one parent element.
src/components/VirtualEvents/VirtualEventRoom.tsx(350,13): error TS2657: JSX expressions must have one parent element.
src/components/VRManager.tsx(29,5): error TS2657: JSX expressions must have one parent element.
src/components/WalletConnect.tsx(35,7): error TS2657: JSX expressions must have one parent element.
src/components/WalletConnect.tsx(49,9): error TS2657: JSX expressions must have one parent element.
src/components/WalletConnect.tsx(68,15): error TS2657: JSX expressions must have one parent element.
src/components/WalletConnect.tsx(74,19): error TS2657: JSX expressions must have one parent element.
src/components/WalletConnect.tsx(84,21): error TS2657: JSX expressions must have one parent element.
src/components/WalletConnect.tsx(93,21): error TS2657: JSX expressions must have one parent element.
src/components/Web3NFTManager.tsx(41,5): error TS2657: JSX expressions must have one parent element.
src/components/Web3NFTManager.tsx(75,7): error TS2657: JSX expressions must have one parent element.
src/components/WebsiteCreatorManager.tsx(39,5): error TS2657: JSX expressions must have one parent element.
src/components/wellness/WellnessTracker.tsx(64,11): error TS2657: JSX expressions must have one parent element.
src/components/wellness/WellnessTracker.tsx(70,15): error TS2657: JSX expressions must have one parent element.
src/containers/Search-Transport-Service/pages/TransportSearch.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/containers/Social/pages/BlogPage.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/containers/Social/pages/BlogPostDetail.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/contexts/ThemeContext.tsx(65,5): error TS2657: JSX expressions must have one parent element.
src/contexts/UIContext.tsx(67,7): error TS2657: JSX expressions must have one parent element.
src/demo-app.tsx(24,3): error TS2657: JSX expressions must have one parent element.
src/features/analyses/components/AnalysisDetails.tsx(73,15): error TS2657: JSX expressions must have one parent element.
src/features/analyses/components/AnalysisDetails.tsx(103,19): error TS2657: JSX expressions must have one parent element.
src/features/analyses/components/AnalysisDetails.tsx(128,19): error TS2657: JSX expressions must have one parent element.
src/features/analyses/components/AnalysisDetails.tsx(135,25): error TS2657: JSX expressions must have one parent element.
src/features/analyses/components/AnalysisDetails.tsx(170,7): error TS2657: JSX expressions must have one parent element.
src/features/analyses/components/AnalysisDetails.tsx(179,7): error TS2657: JSX expressions must have one parent element.
src/features/analyses/components/AnalysisDetails.tsx(197,7): error TS2657: JSX expressions must have one parent element.
src/features/analyses/components/AnalysisTable.tsx(96,9): error TS2657: JSX expressions must have one parent element.
src/features/analyses/components/AnalysisTable.tsx(145,11): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(83,5): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(102,7): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(111,15): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(134,11): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(140,15): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(163,11): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(169,15): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(194,11): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(212,11): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysesPage.tsx(229,7): error TS2657: JSX expressions must have one parent element.
src/features/analyses/pages/AnalysisDetailsPage.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Login.tsx(56,5): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Login.tsx(84,11): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Login.tsx(124,13): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Register.tsx(113,13): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Register.tsx(164,11): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Register.tsx(179,11): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Register.tsx(194,5): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Register.tsx(213,15): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Register.tsx(230,13): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/Register.tsx(253,11): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/ResetPassword.tsx(53,7): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/ResetPassword.tsx(68,11): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/ResetPassword.tsx(82,5): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/ResetPassword.tsx(94,11): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/ResetPassword.tsx(109,9): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/ResetPassword.tsx(134,11): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/ResetPassword.tsx(145,19): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/ResetPassword.tsx(174,11): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/VerifyEmail.tsx(51,7): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/VerifyEmail.tsx(62,7): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/VerifyEmail.tsx(72,13): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/VerifyEmail.tsx(83,11): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/VerifyEmail.tsx(99,7): error TS2657: JSX expressions must have one parent element.
src/features/auth/components/VerifyEmail.tsx(111,13): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/CategoryFilter.tsx(22,5): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/CategoryFilter.tsx(26,11): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/Marketplace.tsx(92,7): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/Marketplace.tsx(99,5): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/Marketplace.tsx(110,7): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/Marketplace.tsx(120,11): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/Marketplace.tsx(140,9): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/ProductCard.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/ProductCard.tsx(29,7): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/ProductCard.tsx(33,7): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/ProductCard.tsx(39,7): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/SearchBar.tsx(11,5): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/ShoppingCart.tsx(46,7): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/ShoppingCart.tsx(54,7): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/ShoppingCart.tsx(70,19): error TS2657: JSX expressions must have one parent element.
src/features/marketplace/components/ShoppingCart.tsx(95,15): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(127,5): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(140,13): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(165,17): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(170,15): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(177,13): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(193,17): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(208,23): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(230,19): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(255,19): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(280,19): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/MessagesPage.tsx(301,11): error TS2657: JSX expressions must have one parent element.
src/features/messaging/components/VoiceMessageRecorder.tsx(58,5): error TS2657: JSX expressions must have one parent element.
src/hooks/useAnimations.ts(29,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useCommunity.ts(58,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useFormOptimizer.ts(36,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useIntersectionObserver.ts(19,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useMutation.ts(22,90): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useNotificationSound.ts(12,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/usePrefetch.ts(12,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useProtectedRoute.ts(14,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useQueryState.ts(68,74): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useRenderOptimizer.ts(33,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useRetreatService.ts(39,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useRoutePrefetch.ts(24,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/hooks/useVirtualList.tsx(120,5): error TS2657: JSX expressions must have one parent element.
src/i18n/scripts/translationSync.ts(28,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/layouts/DashboardLayout.tsx(22,7): error TS2657: JSX expressions must have one parent element.
src/layouts/FinancialLayout.tsx(9,5): error TS2657: JSX expressions must have one parent element.
src/layouts/PrivateLayout.tsx(7,7): error TS2657: JSX expressions must have one parent element.
src/layouts/PublicLayout.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/layouts/SettingsLayout.tsx(9,5): error TS2657: JSX expressions must have one parent element.
src/middleware/AuthMiddleware.tsx(39,6): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/middleware/loggingMiddleware.ts(15,59): error TS2809: Declaration or statement expected. This '=' follows a block of statements, so if you intended to write a destructuring assignment, you might need to wrap the the whole assignment in parentheses.
src/monitoring/AdminDashboard.tsx(20,5): error TS2657: JSX expressions must have one parent element.
src/monitoring/AdminDashboard.tsx(25,11): error TS2657: JSX expressions must have one parent element.
src/monitoring/components/AdminDashboard.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/monitoring/components/AdminDashboard.tsx(10,7): error TS2657: JSX expressions must have one parent element.
src/monitoring/components/OfflineStatus.tsx(105,11): error TS2657: JSX expressions must have one parent element.
src/monitoring/components/OfflineStatus.tsx(115,11): error TS2657: JSX expressions must have one parent element.
src/monitoring/components/OfflineStatus.tsx(125,11): error TS2657: JSX expressions must have one parent element.
src/monitoring/ConfigurationPanel.tsx(15,5): error TS2657: JSX expressions must have one parent element.
src/monitoring/dashboard/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/monitoring/ErrorBoundary.tsx(45,11): error TS2657: JSX expressions must have one parent element.
src/monitoring/MetricsExplorer.tsx(130,5): error TS2657: JSX expressions must have one parent element.
src/monitoring/MetricsExplorer.tsx(133,7): error TS2657: JSX expressions must have one parent element.
src/monitoring/MetricsExplorer.tsx(146,11): error TS2657: JSX expressions must have one parent element.
src/monitoring/MetricsExplorer.tsx(161,11): error TS2657: JSX expressions must have one parent element.
src/monitoring/MetricsExplorer.tsx(163,13): error TS2657: JSX expressions must have one parent element.
src/monitoring/MetricsExplorer.tsx(189,9): error TS2657: JSX expressions must have one parent element.
src/monitoring/MonitoringAdminDashboard.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/monitoring/MonitoringAdminDashboard.tsx(26,7): error TS2657: JSX expressions must have one parent element.
src/monitoring/MonitoringDashboard.tsx(12,3): error TS2657: JSX expressions must have one parent element.
src/monitoring/MonitoringDashboard.tsx(15,5): error TS2657: JSX expressions must have one parent element.
src/monitoring/PerformanceDashboard.tsx(49,5): error TS2657: JSX expressions must have one parent element.
src/monitoring/PerformanceDashboard.tsx(52,7): error TS2657: JSX expressions must have one parent element.
src/monitoring/PerformanceDashboard.tsx(70,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(94,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(104,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(113,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(124,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(143,13): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(163,13): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(185,13): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(214,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(233,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(236,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(270,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(273,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(276,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/AdminDashboard.tsx(279,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Analytics.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Content.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Dashboard.tsx(47,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Dashboard.tsx(61,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Dashboard.tsx(67,9): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Dashboard.tsx(72,9): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Dashboard.tsx(77,9): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Dashboard.tsx(95,9): error TS2657: JSX expressions must have one parent element.
src/pages/admin/MonitoringDashboard.tsx(68,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/MonitoringDashboard.tsx(76,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/MonitoringDashboard.tsx(85,5): error TS2657: JSX expressions must have one parent element.
src/pages/admin/MonitoringDashboard.tsx(95,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/MonitoringDashboard.tsx(104,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/MonitoringDashboard.tsx(113,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/MonitoringDashboard.tsx(122,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/MonitoringDashboard.tsx(135,13): error TS2657: JSX expressions must have one parent element.
src/pages/admin/MonitoringDashboard.tsx(150,13): error TS2657: JSX expressions must have one parent element.
src/pages/admin/PartnerManagement.tsx(36,5): error TS2657: JSX expressions must have one parent element.
src/pages/admin/PartnerManagement.tsx(44,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/PartnerManagement.tsx(55,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/PartnerManagement.tsx(61,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/PartnerManagement.tsx(90,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/PartnerManagement.tsx(96,17): error TS2657: JSX expressions must have one parent element.
src/pages/admin/PartnerManagement.tsx(100,17): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ReportsPage.tsx(40,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ReportsPage.tsx(56,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ReportsPage.tsx(64,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ReportsPage.tsx(68,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ReportsPage.tsx(72,11): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ResourcesPage.tsx(66,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ResourcesPage.tsx(77,9): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ResourcesPage.tsx(90,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ResourcesPage.tsx(126,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ResourcesPage.tsx(162,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/ResourcesPage.tsx(198,9): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Roles.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Settings.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/admin/UserManagement.tsx(33,5): error TS2657: JSX expressions must have one parent element.
src/pages/admin/UserManagement.tsx(41,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/UserManagement.tsx(53,7): error TS2657: JSX expressions must have one parent element.
src/pages/admin/UserManagement.tsx(79,15): error TS2657: JSX expressions must have one parent element.
src/pages/admin/UserManagement.tsx(85,17): error TS2657: JSX expressions must have one parent element.
src/pages/admin/UserManagement.tsx(89,17): error TS2657: JSX expressions must have one parent element.
src/pages/admin/UserManagement.tsx(93,17): error TS2657: JSX expressions must have one parent element.
src/pages/admin/Users.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(148,35): error TS2457: Type alias name cannot be 'type'.
src/pages/analysis/AnalysisListPage.tsx(191,71): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(328,9): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(335,15): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(355,13): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(365,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(370,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(381,15): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(407,17): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(430,17): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(476,15): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(504,25): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(517,25): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(525,25): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(533,25): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(546,31): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(557,31): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(573,31): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(589,31): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/AnalysisListPage.tsx(627,13): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(404,7): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(417,9): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(440,9): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(482,9): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(514,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(525,19): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(539,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(549,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(568,11): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(598,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(609,19): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(623,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(633,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(666,17): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(711,15): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(731,25): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(739,27): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(752,27): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(788,9): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(804,13): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(819,15): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(826,15): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CompareAnalysisPage.tsx(835,15): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(197,5): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(200,9): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(239,9): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(244,13): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(252,19): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(276,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(299,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(330,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(368,19): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(387,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(403,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(427,15): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(432,19): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(469,21): error TS2657: JSX expressions must have one parent element.
src/pages/analysis/CreateAnalysisPage.tsx(477,27): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ForgotPasswordPage.tsx(53,7): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ForgotPasswordPage.tsx(65,11): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ForgotPasswordPage.tsx(77,5): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ForgotPasswordPage.tsx(90,9): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ForgotPasswordPage.tsx(110,13): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ForgotPasswordPage.tsx(127,17): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ForgotPasswordPage.tsx(135,11): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ForgotPasswordPage.tsx(150,11): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Login.tsx(26,5): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Login.tsx(33,9): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Login.tsx(53,13): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Login.tsx(72,11): error TS2657: JSX expressions must have one parent element.
src/pages/auth/LoginPage.tsx(51,7): error TS2657: JSX expressions must have one parent element.
src/pages/auth/LoginPage.tsx(63,11): error TS2657: JSX expressions must have one parent element.
src/pages/auth/LoginPage.tsx(106,13): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Register.tsx(27,5): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Register.tsx(34,9): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Register.tsx(54,13): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Register.tsx(71,13): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Register.tsx(88,13): error TS2657: JSX expressions must have one parent element.
src/pages/auth/Register.tsx(107,11): error TS2657: JSX expressions must have one parent element.
src/pages/auth/RegisterPage.tsx(21,5): error TS2657: JSX expressions must have one parent element.
src/pages/auth/RegisterPage.tsx(27,9): error TS2657: JSX expressions must have one parent element.
src/pages/auth/RegisterPage.tsx(58,11): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPassword.tsx(26,5): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPassword.tsx(34,9): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPassword.tsx(54,13): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPassword.tsx(73,11): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPasswordPage.tsx(54,7): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPasswordPage.tsx(64,11): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPasswordPage.tsx(77,5): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPasswordPage.tsx(85,9): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPasswordPage.tsx(105,13): error TS2657: JSX expressions must have one parent element.
src/pages/auth/ResetPasswordPage.tsx(124,11): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/BookingPage.tsx(76,7): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/BookingPage.tsx(98,13): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingCalendar.tsx(71,7): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingForm.tsx(53,7): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingForm.tsx(58,9): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingForm.tsx(145,9): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(37,5): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(44,9): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(52,13): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(57,13): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(63,13): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(68,13): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(78,11): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(86,9): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(97,13): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(102,13): error TS2657: JSX expressions must have one parent element.
src/pages/Booking/components/BookingSummary.tsx(112,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(144,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(153,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(178,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(196,17): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(213,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(231,17): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(248,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(285,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(290,21): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(295,23): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(320,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(338,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(365,25): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(384,27): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(415,19): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(424,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard.tsx(438,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/AdminDashboard.tsx(33,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/AdminDashboard.tsx(39,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/AdminDashboard.tsx(46,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/Analytics.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/BookingCard.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/BookingCard.tsx(14,9): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/BookingCard.tsx(35,7): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/BookingCard.tsx(40,9): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/RecentBookings.tsx(40,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/RecentBookings.tsx(60,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/RecentBookings.tsx(76,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/RecentBookings.tsx(93,7): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/Stats.tsx(12,3): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/UpcomingEvents.tsx(46,9): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/UpcomingEvents.tsx(68,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/UpcomingEvents.tsx(76,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/components/UpcomingEvents.tsx(92,7): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/Dashboard.tsx(22,7): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/Dashboard.tsx(43,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/Dashboard.tsx(61,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(64,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(67,9): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(72,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(82,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(90,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(98,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(106,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(120,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(129,19): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(142,27): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(168,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(173,17): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(192,19): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/GroupDashboard.tsx(197,21): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/Home.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(119,7): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(128,7): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(158,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(184,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(192,19): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(202,21): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(216,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(232,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(238,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(249,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/MainDashboard.tsx(255,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(50,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(53,9): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(73,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(88,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(99,19): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(112,21): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(133,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(140,17): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(148,19): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/PartnerDashboard.tsx(159,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/RetreatAnalyticsPage.tsx(43,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/RetreatAnalyticsPage.tsx(59,7): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/RetreatAnalyticsPage.tsx(66,9): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/RetreatAnalyticsPage.tsx(72,9): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/RetreatAnalyticsPage.tsx(79,7): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/RetreatAnalyticsPage.tsx(97,9): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(39,5): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(42,9): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(47,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(51,13): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(59,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(64,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(71,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(76,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(83,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(88,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(96,11): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(101,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(116,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(127,19): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(165,15): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(174,17): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(181,17): error TS2657: JSX expressions must have one parent element.
src/pages/Dashboard/UserDashboard.tsx(188,17): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/DashboardPage.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/NotificationHistory.tsx(66,7): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/NotificationHistory.tsx(77,7): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/NotificationHistory.tsx(85,5): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/NotificationHistory.tsx(91,9): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/NotificationHistory.tsx(94,13): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/ProfilePage.tsx(9,5): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/ProfilePage.tsx(15,7): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/ProfilePage.tsx(36,9): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(50,5): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(60,7): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(82,9): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(101,13): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(107,13): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(115,17): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(131,13): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(139,17): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(147,19): error TS2657: JSX expressions must have one parent element.
src/pages/dashboards/SettingsPage.tsx(154,19): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(114,5): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(125,9): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(137,9): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(156,13): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(170,19): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(187,19): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(199,13): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(217,15): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(233,15): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(248,15): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(269,13): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(290,15): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(309,11): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(325,13): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(332,15): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(343,17): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(352,17): error TS2657: JSX expressions must have one parent element.
src/pages/demo/AIAndInteractionsDemo.tsx(368,13): error TS2657: JSX expressions must have one parent element.
src/pages/error/Error404Page.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/error/Error500Page.tsx(15,5): error TS2657: JSX expressions must have one parent element.
src/pages/error/Error500Page.tsx(42,11): error TS2657: JSX expressions must have one parent element.
src/pages/error/ErrorPage.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/error/NotFoundPage.tsx(10,5): error TS2657: JSX expressions must have one parent element.
src/pages/error/UnauthorizedPage.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/error/UnauthorizedPage.tsx(18,9): error TS2657: JSX expressions must have one parent element.
src/pages/error/UnderConstructionPage.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/error/UpgradePage.tsx(90,5): error TS2657: JSX expressions must have one parent element.
src/pages/error/UpgradePage.tsx(106,13): error TS2657: JSX expressions must have one parent element.
src/pages/error/UpgradePage.tsx(135,21): error TS2657: JSX expressions must have one parent element.
src/pages/Error500Page.tsx(9,5): error TS2657: JSX expressions must have one parent element.
src/pages/Files/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/pages/financial/FinancialReports.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InsurancePage.tsx(75,5): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InsurancePage.tsx(101,11): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InsurancePage.tsx(114,19): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InsurancePage.tsx(121,19): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InsurancePage.tsx(131,15): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InsurancePage.tsx(135,21): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InsurancePage.tsx(145,13): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InvoiceList.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InvoicePage.tsx(18,5): error TS2657: JSX expressions must have one parent element.
src/pages/financial/InvoicePage.tsx(32,11): error TS2657: JSX expressions must have one parent element.
src/pages/financial/PaymentManager.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/FormDemo.tsx(56,5): error TS2657: JSX expressions must have one parent element.
src/pages/FormDemo.tsx(64,7): error TS2657: JSX expressions must have one parent element.
src/pages/FormDemo.tsx(126,7): error TS2657: JSX expressions must have one parent element.
src/pages/InvoicePage.tsx(14,5): error TS2657: JSX expressions must have one parent element.
src/pages/Login/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/pages/Login/LoginPage.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/MarketplacePage/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Analytics.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Analytics.tsx(13,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Analytics.tsx(19,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Analytics.tsx(24,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/AnalyzerDashboard.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/AnalyzerDashboard.tsx(13,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/AnalyzerDashboard.tsx(18,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/AnalyzerDashboard.tsx(24,7): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/AnalyzerLayout.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/AnalyzerReports.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/AnalyzerReports.tsx(17,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/AnalyzerReports.tsx(26,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/BrowseFiles.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/BrowseFiles.tsx(17,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/BrowseFiles.tsx(25,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(71,7): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(79,7): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(86,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(89,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(100,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(112,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(117,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(122,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(127,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(136,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(153,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(162,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(170,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(186,13): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(204,13): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(216,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarDetailsPage.tsx(221,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CarRentalPage.tsx(41,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CompareInsurance.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CompareInsurance.tsx(12,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CompareInsurance.tsx(22,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CompareInsurance.tsx(33,13): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CompareInsurance.tsx(43,13): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CompareInsurance.tsx(56,7): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CompareInsurance.tsx(76,13): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CompareInsurance.tsx(83,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/CompareInsuranceLayout.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Domains.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Domains.tsx(13,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Domains.tsx(23,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Domains.tsx(29,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Editor.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Editor.tsx(14,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Editor.tsx(21,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/EducationPage.tsx(32,7): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/EducationPage.tsx(43,13): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/EducationPage.tsx(58,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/EducationPage.tsx(63,17): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/EducationPage.tsx(80,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/EducationPage.tsx(83,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/EducationPage.tsx(101,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/EducationPage.tsx(105,17): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagement.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagement.tsx(15,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagement.tsx(21,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagement.tsx(27,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagement.tsx(34,7): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagement.tsx(54,13): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagement.tsx(66,7): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagement.tsx(82,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagement.tsx(90,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/FinancialManagementLayout.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Messaging.tsx(44,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Messaging.tsx(53,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Messaging.tsx(60,15): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/NFT.tsx(57,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/NFT.tsx(129,19): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/RetreatMatcher.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/RetreatMatcherLayout.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/RetreatStream.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/SecurityService.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/SecurityService.tsx(16,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/SecurityService.tsx(32,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/SecurityService.tsx(46,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Social.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Social.tsx(16,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Social.tsx(32,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Social.tsx(46,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/SocialVideo.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/SocialVideo.tsx(16,9): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/SocialVideo.tsx(32,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/SocialVideo.tsx(46,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/StorageLayout.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/TransportBooking.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/Upload.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/VR.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/VR.tsx(18,11): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/VRLayout.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/microservices/WebsiteCreatorLayout.tsx(7,5): error TS2657: JSX expressions must have one parent element.
src/pages/nft/NFTGalleryPage.tsx(8,7): error TS2657: JSX expressions must have one parent element.
src/pages/nft/TokenPage.tsx(8,7): error TS2657: JSX expressions must have one parent element.
src/pages/NftGallery/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/pages/NftGallery/nft/NFTGalleryPage.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/pages/NftGallery/nft/NFTGalleryPage.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/pages/NftGallery/nft/NFTGalleryPage.tsx(28,11): error TS2657: JSX expressions must have one parent element.
src/pages/NftGallery/nft/NFTGalleryPage.tsx(48,13): error TS2657: JSX expressions must have one parent element.
src/pages/NftGallery/nft/NFTGalleryPage.tsx(58,11): error TS2657: JSX expressions must have one parent element.
src/pages/NftGallery/nft/TokenPage.tsx(13,5): error TS2657: JSX expressions must have one parent element.
src/pages/NftGallery/nft/TokenPage.tsx(18,7): error TS2657: JSX expressions must have one parent element.
src/pages/NotFound.tsx(13,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AdminDashboardPage.tsx(131,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AdminDashboardPage.tsx(134,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AdminDashboardPage.tsx(139,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AdminDashboardPage.tsx(144,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AdminDashboardPage.tsx(154,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(44,7): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(54,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(62,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(69,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(76,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(86,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(93,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(100,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(110,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(118,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(124,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/AffiliatePortal.tsx(133,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/BaseDashboard.tsx(25,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/BaseDashboard.tsx(40,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/BookingsPage.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/BookingsPage.tsx(17,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CertifiedDashboard.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CertifiedDashboard.tsx(26,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CertifiedDashboard.tsx(33,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CertifiedDashboard.tsx(37,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CertifiedDashboard.tsx(41,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CertifiedDashboard.tsx(48,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(20,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(28,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(35,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(39,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(46,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(53,15): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(58,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(62,15): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(71,7): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CommunityPage.tsx(82,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CreateRetreatAIPage.tsx(110,7): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CreateRetreatAIPage.tsx(121,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CreateRetreatAIPage.tsx(126,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CreateRetreatAIPage.tsx(129,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CreateRetreatAIPage.tsx(141,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CreateRetreatAIPage.tsx(154,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CreateRetreatAIPage.tsx(175,15): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CreateRetreatAIPage.tsx(183,19): error TS2657: JSX expressions must have one parent element.
src/pages/partner/CreateRetreatAIPage.tsx(207,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/Dashboard.tsx(9,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/Dashboard.tsx(24,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/Dashboard.tsx(31,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/Dashboard.tsx(35,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/Dashboard.tsx(42,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/GetStartedPage.tsx(92,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(79,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(92,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(104,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(116,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(133,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(149,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(161,13): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(165,15): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(184,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(189,19): error TS2657: JSX expressions must have one parent element.
src/pages/partner/OnboardingPage.tsx(225,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PartnerCommunityPage.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PartnerCommunityPage.tsx(20,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PartnerCommunityPage.tsx(34,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PartnerCommunityPage.tsx(43,11): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PremiumAnalyticsPage.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PremiumAnalyticsPage.tsx(16,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PremiumAnalyticsPage.tsx(23,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PremiumAnalyticsPage.tsx(31,7): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PremiumAnalyticsPage.tsx(39,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PremiumDashboard.tsx(25,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/PremiumDashboard.tsx(41,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/RetreatAnalyticsPage.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/partner/RetreatAnalyticsPage.tsx(16,9): error TS2657: JSX expressions must have one parent element.
src/pages/partner/RetreatAnalyticsPage.tsx(24,7): error TS2657: JSX expressions must have one parent element.
src/pages/partner/ToolsPage.tsx(46,5): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/Profile.tsx(10,7): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/Profile.tsx(36,13): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/Profile.tsx(41,15): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/Profile.tsx(49,17): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/Profile.tsx(55,17): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/Profile.tsx(63,13): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(47,7): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(59,9): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(63,11): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(68,13): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(88,13): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(116,9): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(120,11): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(132,13): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(142,13): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/RewardsPage.tsx(155,9): error TS2657: JSX expressions must have one parent element.
src/pages/Private/user/UserDashboardPage.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/professional/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/pages/professional/OrganizersPage.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/pages/professional/OrganizersPage.tsx(41,9): error TS2657: JSX expressions must have one parent element.
src/pages/professional/OrganizersPage.tsx(45,15): error TS2657: JSX expressions must have one parent element.
src/pages/professional/OrganizersPage.tsx(63,9): error TS2657: JSX expressions must have one parent element.
src/pages/professional/OrganizersPage.tsx(74,15): error TS2657: JSX expressions must have one parent element.
src/pages/professional/OrganizersPage.tsx(83,15): error TS2657: JSX expressions must have one parent element.
src/pages/professional/OrganizersPage.tsx(88,19): error TS2657: JSX expressions must have one parent element.
src/pages/professional/OrganizersPage.tsx(93,15): error TS2657: JSX expressions must have one parent element.
src/pages/professional/PartnersPage.tsx(39,7): error TS2657: JSX expressions must have one parent element.
src/pages/professional/PartnersPage.tsx(58,13): error TS2657: JSX expressions must have one parent element.
src/pages/professional/PartnersPage.tsx(85,9): error TS2657: JSX expressions must have one parent element.
src/pages/professional/ProDashboard.tsx(28,7): error TS2657: JSX expressions must have one parent element.
src/pages/professional/ProDashboard.tsx(35,5): error TS2657: JSX expressions must have one parent element.
src/pages/professional/ProDashboard.tsx(43,11): error TS2657: JSX expressions must have one parent element.
src/pages/professional/ProDashboard.tsx(58,9): error TS2657: JSX expressions must have one parent element.
src/pages/professional/ProDashboard.tsx(62,11): error TS2657: JSX expressions must have one parent element.
src/pages/professional/ProDashboard.tsx(77,9): error TS2657: JSX expressions must have one parent element.
src/pages/professional/ProDashboard.tsx(81,11): error TS2657: JSX expressions must have one parent element.
src/pages/Profile/components/ChangePasswordDialog.tsx(112,5): error TS2657: JSX expressions must have one parent element.
src/pages/Profile/components/ChangePasswordDialog.tsx(164,9): error TS2657: JSX expressions must have one parent element.
src/pages/Profile/Profile.tsx(10,7): error TS2657: JSX expressions must have one parent element.
src/pages/Profile/Profile.tsx(36,13): error TS2657: JSX expressions must have one parent element.
src/pages/Profile/Profile.tsx(41,15): error TS2657: JSX expressions must have one parent element.
src/pages/Profile/Profile.tsx(49,17): error TS2657: JSX expressions must have one parent element.
src/pages/Profile/Profile.tsx(55,17): error TS2657: JSX expressions must have one parent element.
src/pages/Profile/Profile.tsx(63,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/BecomePartnerPage.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/BlogPage.tsx(37,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/BlogPage.tsx(50,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/BlogPage.tsx(74,23): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CareersPage.tsx(34,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CareersPage.tsx(54,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CareersPage.tsx(64,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CaterersPage.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CaterersPage.tsx(41,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CaterersPage.tsx(45,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CaterersPage.tsx(63,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CaterersPage.tsx(76,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CaterersPage.tsx(85,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CaterersPage.tsx(90,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CaterersPage.tsx(97,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CaterersPage.tsx(107,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CGUPage.tsx(8,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CGUPage.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(83,3): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(99,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(113,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(119,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(140,20): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(172,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(189,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(205,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(216,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(229,23): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(268,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(275,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(286,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(296,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(307,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(319,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(331,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(345,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(357,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityMemberPage.tsx(366,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityPage.tsx(91,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityPage.tsx(99,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityPage.tsx(118,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityPage.tsx(134,21): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityPage.tsx(149,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/CommunityPage.tsx(157,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(8,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(19,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(25,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(34,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(42,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(54,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(70,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(80,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(90,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(93,21): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ContactPage.tsx(100,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/EventsPage.tsx(77,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/EventsPage.tsx(85,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/EventsPage.tsx(98,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/EventsPage.tsx(138,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/EventsPage.tsx(155,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/EventsPage.tsx(171,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/EventsPage.tsx(187,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/EventsPage.tsx(207,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ExploreRetreatsPage.tsx(26,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FAQPage.tsx(61,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FAQPage.tsx(74,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FAQPage.tsx(88,23): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FAQPage.tsx(119,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FoundationPage.tsx(31,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FoundationPage.tsx(45,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FoundationPage.tsx(49,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FoundationPage.tsx(67,21): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FoundationPage.tsx(74,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FoundationPage.tsx(91,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FoundationPage.tsx(98,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FoundationPage.tsx(102,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/FoundationPage.tsx(106,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/GalleryPage.tsx(35,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/GalleryPage.tsx(49,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/GDPRPage.tsx(106,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/GDPRPage.tsx(131,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/GDPRPage.tsx(141,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/GDPRPage.tsx(147,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/GDPRPage.tsx(153,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/GDPRPage.tsx(190,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/GDPRPage.tsx(200,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HomePage.tsx(35,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HomePage.tsx(166,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HomePage.tsx(184,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HomePage.tsx(198,25): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HomePage.tsx(211,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HomePage.tsx(217,23): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HomePage.tsx(235,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HostsPage.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HostsPage.tsx(41,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HostsPage.tsx(45,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HostsPage.tsx(63,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HostsPage.tsx(76,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HostsPage.tsx(83,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HostsPage.tsx(88,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/HostsPage.tsx(94,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/InsurancePage.tsx(74,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/InsurancePage.tsx(100,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/InsurancePage.tsx(113,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/InsurancePage.tsx(120,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/InsurancePage.tsx(130,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/InsurancePage.tsx(134,21): error TS2657: JSX expressions must have one parent element.
src/pages/Public/InsurancePage.tsx(144,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/InvoicePage.tsx(40,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/InvoicePage.tsx(54,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/LandingPage.tsx(12,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/LandingPage.tsx(39,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/LandingPage.tsx(57,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/LocationPage.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/LocationPage.tsx(39,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/LocationPage.tsx(46,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/LocationPage.tsx(62,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/LoyaltyPage.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/LoyaltyPage.tsx(14,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/MainPage.tsx(107,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(49,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(61,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(65,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(109,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(113,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(119,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(123,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(127,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(131,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NewsPage.tsx(138,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NFTGalleryPage.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NFTGalleryPage.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NFTGalleryPage.tsx(28,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NFTGalleryPage.tsx(48,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/NFTGalleryPage.tsx(58,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/OrganizersPage.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/OrganizersPage.tsx(41,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/OrganizersPage.tsx(45,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/OrganizersPage.tsx(63,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/OrganizersPage.tsx(74,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/OrganizersPage.tsx(83,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/OrganizersPage.tsx(88,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/OrganizersPage.tsx(93,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/PartnersPage.tsx(39,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/PartnersPage.tsx(58,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/PartnersPage.tsx(85,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/PrivacyPolicyPage.tsx(8,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/PrivacyPolicyPage.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ProgramsPage.tsx(55,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ProgramsPage.tsx(74,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ProgramsPage.tsx(93,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ProgramsPage.tsx(123,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ProgramsPage.tsx(127,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ProgramsPage.tsx(139,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ProgramsPage.tsx(149,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/ProgramsPage.tsx(162,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RegisterPage.tsx(30,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RegisterPage.tsx(36,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RegisterPage.tsx(93,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(33,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(38,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(50,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(63,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(72,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(84,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(108,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(118,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(125,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(137,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(147,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(168,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatPage.tsx(193,17): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatsPage.tsx(45,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/RetreatsPage.tsx(67,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SecurityPage.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SecurityPage.tsx(16,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SettingsPage.tsx(8,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SettingsPage.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SupportPage.tsx(35,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SupportPage.tsx(47,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SupportPage.tsx(53,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SupportPage.tsx(60,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SupportPage.tsx(65,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SupportPage.tsx(69,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SupportPage.tsx(81,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SupportPage.tsx(96,11): error TS2657: JSX expressions must have one parent element.
src/pages/Public/SupportPage.tsx(103,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TermsPage.tsx(8,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TermsPage.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TestimonialsPage.tsx(35,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TestimonialsPage.tsx(50,13): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TestimonialsPage.tsx(82,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TokenPage.tsx(13,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TokenPage.tsx(18,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TravelAgenciesPage.tsx(27,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TravelAgenciesPage.tsx(41,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TravelAgenciesPage.tsx(45,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TravelAgenciesPage.tsx(63,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TravelAgenciesPage.tsx(74,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TravelAgenciesPage.tsx(83,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TravelAgenciesPage.tsx(88,19): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TravelAgenciesPage.tsx(94,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/TravelAgenciesPage.tsx(104,15): error TS2657: JSX expressions must have one parent element.
src/pages/Public/WellnessPage.tsx(47,5): error TS2657: JSX expressions must have one parent element.
src/pages/Public/WellnessPage.tsx(52,7): error TS2657: JSX expressions must have one parent element.
src/pages/Public/WellnessPage.tsx(69,9): error TS2657: JSX expressions must have one parent element.
src/pages/Public/WellnessPage.tsx(75,9): error TS2657: JSX expressions must have one parent element.
src/pages/Register/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatCard.tsx(32,5): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatCard.tsx(60,11): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatCard.tsx(70,11): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatCard.tsx(80,11): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatCard.tsx(106,13): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatCard.tsx(119,7): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatCard.tsx(132,11): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatCard.tsx(138,13): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatFilters.tsx(76,5): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatFilters.tsx(87,15): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatFilters.tsx(106,9): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/components/RetreatFilters.tsx(144,11): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/ExploreRetreatsPage.tsx(72,7): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/ExploreRetreatsPage.tsx(80,7): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/ExploreRetreatsPage.tsx(88,5): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/ExploreRetreatsPage.tsx(98,7): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/ExploreRetreatsPage.tsx(125,11): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/ExploreRetreatsPage.tsx(151,17): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/ExploreRetreatsPage.tsx(177,9): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/RetreatPage.tsx(57,7): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/RetreatPage.tsx(65,7): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/RetreatPage.tsx(74,7): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/RetreatPage.tsx(81,5): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/RetreatPage.tsx(94,11): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/RetreatPage.tsx(102,13): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/RetreatPage.tsx(106,15): error TS2657: JSX expressions must have one parent element.
src/pages/retreats/RetreatPage.tsx(118,11): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(57,7): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(67,7): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(78,5): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(81,9): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(91,11): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(113,11): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(143,13): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(155,11): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(170,11): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(185,11): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(208,11): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(212,15): error TS2657: JSX expressions must have one parent element.
src/pages/RoadmapReport.tsx(218,17): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(44,7): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(54,11): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(62,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(69,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(76,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(86,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(93,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(100,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(110,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(118,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(124,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/AffiliatePortal.tsx(133,9): error TS2657: JSX expressions must have one parent element.
src/pages/services/BookingsPage.tsx(8,5): error TS2657: JSX expressions must have one parent element.
src/pages/services/BookingsPage.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/pages/services/BookingsPage.tsx(26,9): error TS2657: JSX expressions must have one parent element.
src/pages/services/BookingsPage.tsx(34,7): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(71,7): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(79,7): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(86,5): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(89,9): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(100,11): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(112,15): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(117,15): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(122,15): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(127,15): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(136,15): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(153,15): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(162,11): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(170,11): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(186,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(204,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(216,15): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarDetailsPage.tsx(221,15): error TS2657: JSX expressions must have one parent element.
src/pages/services/CarRentalPage.tsx(42,5): error TS2657: JSX expressions must have one parent element.
src/pages/services/LocationPage.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/pages/services/LocationPage.tsx(39,9): error TS2657: JSX expressions must have one parent element.
src/pages/services/LocationPage.tsx(46,17): error TS2657: JSX expressions must have one parent element.
src/pages/services/LocationPage.tsx(62,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/LoyaltyPage.tsx(23,5): error TS2657: JSX expressions must have one parent element.
src/pages/services/LoyaltyPage.tsx(29,11): error TS2657: JSX expressions must have one parent element.
src/pages/services/MessagesPage.tsx(57,5): error TS2657: JSX expressions must have one parent element.
src/pages/services/MessagesPage.tsx(81,17): error TS2657: JSX expressions must have one parent element.
src/pages/services/MessagesPage.tsx(88,15): error TS2657: JSX expressions must have one parent element.
src/pages/services/MessagesPage.tsx(93,19): error TS2657: JSX expressions must have one parent element.
src/pages/services/MessagesPage.tsx(126,5): error TS2657: JSX expressions must have one parent element.
src/pages/services/NFTGalleryPage.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/pages/services/NFTGalleryPage.tsx(19,9): error TS2657: JSX expressions must have one parent element.
src/pages/services/NFTGalleryPage.tsx(28,11): error TS2657: JSX expressions must have one parent element.
src/pages/services/NFTGalleryPage.tsx(49,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/NFTGalleryPage.tsx(59,11): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(96,5): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(106,7): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(115,11): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(134,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(141,13): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(172,19): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(188,19): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(210,23): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(242,17): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(266,27): error TS2657: JSX expressions must have one parent element.
src/pages/services/NotificationHistory.tsx(275,25): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/AppearanceSettings.tsx(20,5): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/AppearanceSettings.tsx(25,7): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/AppearanceSettings.tsx(43,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/AppearanceSettings.tsx(48,11): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/AppearanceSettings.tsx(55,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/AppearanceSettings.tsx(60,11): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/NotificationsSettings.tsx(18,5): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/NotificationsSettings.tsx(23,7): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/NotificationsSettings.tsx(43,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/SecuritySettings.tsx(10,5): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/SecuritySettings.tsx(15,7): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/SecuritySettings.tsx(36,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/SecuritySettings.tsx(44,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/components/SecuritySettings.tsx(52,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Profile.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Security.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(39,7): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(53,11): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(58,15): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(71,13): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(77,15): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(90,13): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(96,15): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(106,13): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(112,15): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/Settings.tsx(126,11): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage.tsx(51,5): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage.tsx(61,7): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage.tsx(83,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage.tsx(102,13): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage.tsx(108,13): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage.tsx(119,13): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage.tsx(127,17): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage.tsx(135,19): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage.tsx(142,19): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/components/NotificationsSection.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/components/NotificationsSection.tsx(20,7): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/components/PreferencesSection.tsx(26,7): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/components/PreferencesSection.tsx(31,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/components/PreferencesSection.tsx(42,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/components/PreferencesSection.tsx(52,9): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/components/PrivacySection.tsx(15,5): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/components/PrivacySection.tsx(20,7): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/components/SecuritySection.tsx(16,7): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/SettingsPage.tsx(39,5): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/SettingsPage.tsx(54,13): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/SettingsPage.tsx(60,13): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/SettingsPage.tsx(71,13): error TS2657: JSX expressions must have one parent element.
src/pages/Settings/SettingsPage/SettingsPage.tsx(82,13): error TS2657: JSX expressions must have one parent element.
src/pages/social/CommunityMemberPage.tsx(60,7): error TS2657: JSX expressions must have one parent element.
src/pages/social/CommunityMemberPage.tsx(68,7): error TS2657: JSX expressions must have one parent element.
src/pages/social/CommunityMemberPage.tsx(77,7): error TS2657: JSX expressions must have one parent element.
src/pages/social/CommunityMemberPage.tsx(84,5): error TS2657: JSX expressions must have one parent element.
src/pages/social/CommunityMemberPage.tsx(120,9): error TS2657: JSX expressions must have one parent element.
src/pages/social/CommunityMemberPage.tsx(128,15): error TS2657: JSX expressions must have one parent element.
src/pages/social/MessagesPage.tsx(57,5): error TS2657: JSX expressions must have one parent element.
src/pages/social/MessagesPage.tsx(81,17): error TS2657: JSX expressions must have one parent element.
src/pages/social/MessagesPage.tsx(88,15): error TS2657: JSX expressions must have one parent element.
src/pages/social/MessagesPage.tsx(93,19): error TS2657: JSX expressions must have one parent element.
src/pages/social/MessagesPage.tsx(126,5): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(49,7): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(61,9): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(65,15): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(109,9): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(113,11): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(119,13): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(123,13): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(127,13): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(131,13): error TS2657: JSX expressions must have one parent element.
src/pages/social/NewsPage.tsx(138,9): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(117,5): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(127,7): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(136,11): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(155,13): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(162,13): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(193,19): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(209,19): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(231,23): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(263,17): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(287,27): error TS2657: JSX expressions must have one parent element.
src/pages/social/NotificationHistory.tsx(296,25): error TS2657: JSX expressions must have one parent element.
src/pages/social/SocialFeed/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/pages/social/SocialFeed/SocialFeedPage.tsx(25,5): error TS2657: JSX expressions must have one parent element.
src/pages/social/SocialFeed/SocialFeedPage.tsx(31,13): error TS2657: JSX expressions must have one parent element.
src/pages/social/SocialFeed/SocialFeedPage.tsx(45,17): error TS2657: JSX expressions must have one parent element.
src/pages/TaskListPage.tsx(89,7): error TS2657: JSX expressions must have one parent element.
src/pages/TaskListPage.tsx(108,7): error TS2657: JSX expressions must have one parent element.
src/pages/TaskListPage.tsx(124,5): error TS2657: JSX expressions must have one parent element.
src/pages/TaskListPage.tsx(145,9): error TS2657: JSX expressions must have one parent element.
src/pages/TaskListPage.tsx(153,9): error TS2657: JSX expressions must have one parent element.
src/pages/TaskListPage.tsx(157,13): error TS2657: JSX expressions must have one parent element.
src/pages/TaskListPage.tsx(182,9): error TS2657: JSX expressions must have one parent element.
src/pages/TaskListPage.tsx(192,9): error TS2657: JSX expressions must have one parent element.
src/pages/user/Profile.tsx(10,5): error TS2657: JSX expressions must have one parent element.
src/pages/user/Profile.tsx(20,13): error TS2657: JSX expressions must have one parent element.
src/pages/user/Profile.tsx(30,11): error TS2657: JSX expressions must have one parent element.
src/pages/user/Profile.tsx(38,15): error TS2657: JSX expressions must have one parent element.
src/pages/user/Profile.tsx(43,15): error TS2657: JSX expressions must have one parent element.
src/pages/user/Profile.tsx(51,11): error TS2657: JSX expressions must have one parent element.
src/providers/AppProviders.tsx(16,5): error TS2657: JSX expressions must have one parent element.
src/providers/AppProviders.tsx(55,5): error TS2657: JSX expressions must have one parent element.
src/reports/PerformanceReports.tsx(78,7): error TS2657: JSX expressions must have one parent element.
src/reports/PerformanceReports.tsx(106,9): error TS2657: JSX expressions must have one parent element.
src/reports/PerformanceReports.tsx(158,9): error TS2657: JSX expressions must have one parent element.
src/reports/PerformanceReports.tsx(263,9): error TS2657: JSX expressions must have one parent element.
src/reports/PerformanceReports.tsx(269,9): error TS2657: JSX expressions must have one parent element.
src/router/AdminRoutes.tsx(28,5): error TS2657: JSX expressions must have one parent element.
src/router/AdminRoutes.tsx(34,13): error TS2657: JSX expressions must have one parent element.
src/router/AdminRoutes.tsx(56,15): error TS2657: JSX expressions must have one parent element.
src/router/AdminRoutes.tsx(68,15): error TS2657: JSX expressions must have one parent element.
src/router/AdminRoutes.tsx(80,15): error TS2657: JSX expressions must have one parent element.
src/router/AdminRoutes.tsx(92,15): error TS2657: JSX expressions must have one parent element.
src/router/AdminRoutes.tsx(104,15): error TS2657: JSX expressions must have one parent element.
src/router/index.tsx(8,3): error TS2657: JSX expressions must have one parent element.
src/router/LazyRoute.tsx(14,5): error TS2657: JSX expressions must have one parent element.
src/router/LazyRoutes.tsx(100,5): error TS2657: JSX expressions must have one parent element.
src/router/routes.tsx(41,5): error TS2657: JSX expressions must have one parent element.
src/routes.tsx(6,5): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(97,5): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(103,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(121,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(155,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(181,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(207,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(217,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(227,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(237,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(247,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(257,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(267,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(277,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(287,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(297,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(307,13): error TS2657: JSX expressions must have one parent element.
src/routes/AppRoutes.tsx(317,13): error TS2657: JSX expressions must have one parent element.
src/routes/FinancialRoutes.tsx(10,5): error TS2657: JSX expressions must have one parent element.
src/routes/lazyRoutes.tsx(16,3): error TS2657: JSX expressions must have one parent element.
src/routes/lazyRoutes.tsx(33,7): error TS2657: JSX expressions must have one parent element.
src/routes/lazyRoutes.tsx(41,7): error TS2657: JSX expressions must have one parent element.
src/routes/lazyRoutes.tsx(49,7): error TS2657: JSX expressions must have one parent element.
src/routes/lazyRoutes.tsx(57,7): error TS2657: JSX expressions must have one parent element.
src/routes/lazyRoutes.tsx(65,7): error TS2657: JSX expressions must have one parent element.
src/routes/lazyRoutes.tsx(73,7): error TS2657: JSX expressions must have one parent element.
src/routes/lazyRoutes.tsx(81,7): error TS2657: JSX expressions must have one parent element.
src/routes/PrivateRoutes.tsx(37,3): error TS2657: JSX expressions must have one parent element.
src/routes/PrivateRoutes.tsx(42,11): error TS2657: JSX expressions must have one parent element.
src/routes/PublicRoutes.tsx(13,3): error TS2657: JSX expressions must have one parent element.
src/routes/WebsiteCreatorRoutes.tsx(58,13): error TS2657: JSX expressions must have one parent element.
src/routes/WebsiteCreatorRoutes.tsx(69,13): error TS2657: JSX expressions must have one parent element.
src/routes/WebsiteCreatorRoutes.tsx(80,13): error TS2657: JSX expressions must have one parent element.
src/routes/WebsiteCreatorRoutes.tsx(91,13): error TS2657: JSX expressions must have one parent element.
src/routes/WebsiteCreatorRoutes.tsx(102,13): error TS2657: JSX expressions must have one parent element.
src/routes/WebsiteCreatorRoutes.tsx(114,15): error TS2657: JSX expressions must have one parent element.
src/routes/WebsiteCreatorRoutes.tsx(126,13): error TS2657: JSX expressions must have one parent element.
src/routes/WebsiteCreatorRoutes.tsx(137,13): error TS2657: JSX expressions must have one parent element.
src/services/seo.service.tsx(60,7): error TS2657: JSX expressions must have one parent element.
src/services/seo.service.tsx(70,7): error TS2657: JSX expressions must have one parent element.
src/shared/components/ui/LoadingSpinner.tsx(5,5): error TS2657: JSX expressions must have one parent element.
src/theme/ThemeContext.tsx(46,5): error TS2657: JSX expressions must have one parent element.
src/utils/AppProviders.tsx(9,5): error TS2657: JSX expressions must have one parent element.
src/utils/errorBoundary.tsx(32,11): error TS2657: JSX expressions must have one parent element.
src/utils/icons.tsx(5,3): error TS2657: JSX expressions must have one parent element.
src/utils/icons.tsx(25,3): error TS2657: JSX expressions must have one parent element.
src/utils/icons.tsx(46,3): error TS2657: JSX expressions must have one parent element.
src/utils/icons.tsx(68,3): error TS2657: JSX expressions must have one parent element.
src/utils/icons.tsx(90,3): error TS2657: JSX expressions must have one parent element.
src/utils/icons.tsx(110,3): error TS2657: JSX expressions must have one parent element.
src/utils/icons.tsx(133,3): error TS2657: JSX expressions must have one parent element.
src/utils/lazyImport.tsx(17,5): error TS2657: JSX expressions must have one parent element.
src/utils/Web3Provider.tsx(82,5): error TS2657: JSX expressions must have one parent element.
