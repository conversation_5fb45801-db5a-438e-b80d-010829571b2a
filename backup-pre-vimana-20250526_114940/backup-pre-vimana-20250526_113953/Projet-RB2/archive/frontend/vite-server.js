import { createServer } from 'vite';
import express from 'express';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs/promises';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function startServer() {
  const app = express();
  
  const vite = await createServer({
    server: { middlewareMode: true },
    appType: 'spa',
    root: __dirname,
  });
  
  // Utiliser le middleware Vite en premier pour que Vite gère les fichiers correctement
  app.use(vite.middlewares);
  
  // Middleware personnalisé pour les types MIME
  app.use((req, res, next) => {
    if (req.path.endsWith('.js') || req.path.includes('@react-refresh')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
    } else if (req.path.endsWith('.mjs')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
    } else if (req.path.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css; charset=utf-8');
    } else if (req.path.endsWith('.json')) {
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
    }
    next();
  });

  // Gestion des routes SPA - doit être après les middlewares de type MIME
  app.use('*', async (req, res, next) => {
    const url = req.originalUrl;

    try {
      // Ne pas traiter les fichiers JavaScript comme du HTML
      if (url.endsWith('.js') || url.includes('@react-refresh')) {
        next();
        return;
      }

      let template = await fs.readFile(path.resolve(__dirname, 'index.html'), 'utf-8');
      template = await vite.transformIndexHtml(url, template);
      
      res.status(200).set({ 'Content-Type': 'text/html' }).end(template);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });

  const port = 3001;
  app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
  });
}

startServer();
