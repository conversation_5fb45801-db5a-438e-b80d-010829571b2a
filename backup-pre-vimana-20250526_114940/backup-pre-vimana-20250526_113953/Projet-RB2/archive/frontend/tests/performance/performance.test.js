/**
 * Automated Performance Tests
 * 
 * This file implements automated performance testing as recommended in the audit.
 * It uses Lighthouse CI for measuring Web Vitals and other performance metrics.
 */

const { chromium } = require('playwright');
const lighthouse = require('lighthouse/lighthouse-core/fraggle-rock/api.js');
const { expect } = require('@jest/globals');

// Performance thresholds based on Web Vitals
const PERFORMANCE_THRESHOLDS = {
  'first-contentful-paint': 1800,
  'largest-contentful-paint': 2500,
  'cumulative-layout-shift': 0.1,
  'total-blocking-time': 200,
  'speed-index': 3400,
  'interactive': 3800,
};

describe('Frontend Performance Tests', () => {
  let browser;
  let page;
  let port;
  
  beforeAll(async () => {
    // Start a browser instance
    browser = await chromium.launch();
    page = await browser.newPage();
    
    // Use a random port for testing
    port = 3000 + Math.floor(Math.random() * 1000);
  });
  
  afterAll(async () => {
    await browser.close();
  });
  
  test('Home page meets performance thresholds', async () => {
    // Run Lighthouse audit
    const { lhr } = await lighthouse('http://localhost:' + port, {
      port: new URL(browser.wsEndpoint()).port,
      output: 'json',
      logLevel: 'error',
      onlyCategories: ['performance'],
    });
    
    // Check overall performance score
    expect(lhr.categories.performance.score).toBeGreaterThanOrEqual(0.9);
    
    // Check individual metrics against thresholds
    Object.entries(PERFORMANCE_THRESHOLDS).forEach(([metric, threshold]) => {
      const result = lhr.audits[metric];
      expect(result.numericValue).toBeLessThanOrEqual(
        threshold,
        `${metric} value ${result.numericValue} exceeds threshold ${threshold}`
      );
    });
    
    // Check for render-blocking resources
    const renderBlockingResources = lhr.audits['render-blocking-resources'].details?.items || [];
    expect(renderBlockingResources.length).toBeLessThanOrEqual(2);
    
    // Check for proper image optimization
    const unoptimizedImages = lhr.audits['uses-optimized-images'].details?.items || [];
    expect(unoptimizedImages.length).toBe(0);
    
    // Check for text compression
    expect(lhr.audits['uses-text-compression'].score).toBe(1);
    
    // Check for efficient cache policy
    const inefficientCacheItems = lhr.audits['uses-long-cache-ttl'].details?.items || [];
    expect(inefficientCacheItems.length).toBeLessThanOrEqual(3);
  });
  
  test('Critical user journey performance', async () => {
    // Navigate to home page and measure
    const homeStart = Date.now();
    await page.goto('http://localhost:' + port);
    const homeLoadTime = Date.now() - homeStart;
    expect(homeLoadTime).toBeLessThan(2000);
    
    // Measure time to first interaction
    const interactionStart = Date.now();
    await page.click('button[data-testid="main-cta"]');
    const interactionTime = Date.now() - interactionStart;
    expect(interactionTime).toBeLessThan(100);
    
    // Measure page transition time
    const transitionStart = Date.now();
    await page.click('a[href="/dashboard"]');
    await page.waitForSelector('.dashboard-container');
    const transitionTime = Date.now() - transitionStart;
    expect(transitionTime).toBeLessThan(1000);
  });
});