import { test, expect } from '@playwright/test';

test.describe('NavigationCarousel Component', () => {
  test.beforeEach(async ({ page }) => {
    // Naviguer vers la page contenant le composant
    await page.goto('/');
    
    // Attendre que le composant soit chargé
    await page.waitForSelector('[data-testid="navigation-carousel"]');
  });

  test('should render correctly', async ({ page }) => {
    // Vérifier que le carrousel est visible
    const carousel = await page.locator('[data-testid="navigation-carousel"]');
    await expect(carousel).toBeVisible();
    
    // Vérifier que les éléments du carrousel sont présents
    const items = await page.locator('[data-testid="carousel-item"]').all();
    expect(items.length).toBeGreaterThan(0);
  });

  test('should navigate correctly', async ({ page }) => {
    // Cliquer sur le bouton suivant
    const nextButton = await page.locator('[data-testid="carousel-next"]');
    await nextButton.click();
    
    // Vérifier que l'élément actif a changé
    const activeItem = await page.locator('[data-testid="carousel-item-active"]');
    // Vérifier l'attribut d'état actif
    await expect(activeItem).toHaveAttribute('aria-current', 'true');
    
    // Cliquer sur le bouton précédent
    const prevButton = await page.locator('[data-testid="carousel-prev"]');
    await prevButton.click();
    
    // Vérifier que l'élément actif est revenu
    const firstItem = await page.locator('[data-testid="carousel-item"]').first();
    await expect(firstItem).toHaveAttribute('aria-current', 'true');
  });

  test('should handle item click correctly', async ({ page }) => {
    // Cliquer sur un élément du carrousel
    const items = await page.locator('[data-testid="carousel-item"]').all();
    await items[1].click();
    
    // Vérifier que l'élément cliqué est maintenant actif
    await expect(items[1]).toHaveAttribute('aria-current', 'true');
    
    // Vérifier que la navigation a changé (si applicable)
    // const url = page.url();
    // expect(url).toContain('expected-path');
  });

  test('should be responsive', async ({ page }) => {
    // Tester sur différentes tailles d'écran
    for (const viewport of [
      { width: 1200, height: 800 },
      { width: 768, height: 600 },
      { width: 480, height: 800 }
    ]) {
      await page.setViewportSize(viewport);
      
      // Vérifier que le carrousel s'adapte
      const carousel = await page.locator('[data-testid="navigation-carousel"]');
      await expect(carousel).toBeVisible();
      
      // Attendre que les ajustements responsifs se fassent
      await page.waitForTimeout(200);
      
      // Vérifier que la navigation fonctionne toujours
      const nextButton = await page.locator('[data-testid="carousel-next"]');
      await nextButton.click();
      
      // Vérifier que l'élément actif a changé
      const activeItem = await page.locator('[data-testid="carousel-item-active"]');
      await expect(activeItem).toBeVisible();
    }
  });
});
