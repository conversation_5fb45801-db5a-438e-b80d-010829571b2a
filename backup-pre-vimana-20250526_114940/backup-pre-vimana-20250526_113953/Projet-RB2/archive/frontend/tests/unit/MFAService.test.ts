import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { MFAService } from '../../src/services/auth/mfa.service';
import axios from 'axios';

// Mock axios
vi.mock('axios');

describe('MFAService', () => {
  let mfaService: MFAService;
  const mockBaseUrl = 'http://localhost:3000/api';
  const mockAuthToken = 'test-auth-token';
  
  beforeEach(() => {
    // Reset axios mocks
    vi.resetAllMocks();
    
    // Create a new instance for each test
    mfaService = new MFAService(mockBaseUrl);
    
    // Set the auth token
    localStorage.setItem('authToken', mockAuthToken);
  });
  
  afterEach(() => {
    // Clean up
    localStorage.clear();
  });

  describe('setupTwoFactor', () => {
    it('should successfully set up two-factor authentication', async () => {
      const mockResponse = {
        data: {
          secret: 'ABCDEFGHIJKLMNOP',
          qrCodeUrl: 'data:image/png;base64,iVBORw0KGgo...',
        },
      };
      
      (axios.get as any).mockResolvedValue(mockResponse);
      
      const result = await mfaService.setupTwoFactor();
      
      expect(axios.get).toHaveBeenCalledWith(`${mockBaseUrl}/auth/2fa/setup`, {
        headers: {
          Authorization: `Bearer ${mockAuthToken}`,
        },
      });
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if setup fails', async () => {
      const mockError = new Error('Failed to set up two-factor authentication');
      (axios.get as any).mockRejectedValue(mockError);
      
      await expect(mfaService.setupTwoFactor()).rejects.toThrow('Failed to set up two-factor authentication');
    });
    
    it('should throw an error if auth token is missing', async () => {
      localStorage.removeItem('authToken');
      
      await expect(mfaService.setupTwoFactor()).rejects.toThrow('No authentication token found');
    });
  });
  
  describe('enableTwoFactor', () => {
    it('should successfully enable two-factor authentication', async () => {
      const mockRequest = {
        secret: 'ABCDEFGHIJKLMNOP',
        code: '123456',
      };
      
      const mockResponse = {
        data: {
          message: 'Two-factor authentication enabled successfully',
        },
      };
      
      (axios.post as any).mockResolvedValue(mockResponse);
      
      const result = await mfaService.enableTwoFactor(mockRequest.secret, mockRequest.code);
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockBaseUrl}/auth/2fa/enable`,
        mockRequest,
        {
          headers: {
            Authorization: `Bearer ${mockAuthToken}`,
          },
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if enabling fails', async () => {
      const mockRequest = {
        secret: 'ABCDEFGHIJKLMNOP',
        code: '123456',
      };
      
      const mockError = new Error('Invalid verification code');
      (axios.post as any).mockRejectedValue(mockError);
      
      await expect(mfaService.enableTwoFactor(mockRequest.secret, mockRequest.code)).rejects.toThrow('Invalid verification code');
    });
    
    it('should throw an error if auth token is missing', async () => {
      localStorage.removeItem('authToken');
      
      await expect(mfaService.enableTwoFactor('ABCDEFGHIJKLMNOP', '123456')).rejects.toThrow('No authentication token found');
    });
  });
  
  describe('verifyTwoFactor', () => {
    it('should successfully verify two-factor authentication code', async () => {
      const mockCode = '123456';
      
      const mockResponse = {
        data: {
          message: 'Two-factor authentication verified successfully',
        },
      };
      
      (axios.post as any).mockResolvedValue(mockResponse);
      
      const result = await mfaService.verifyTwoFactor(mockCode);
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockBaseUrl}/auth/2fa/verify`,
        { code: mockCode },
        {
          headers: {
            Authorization: `Bearer ${mockAuthToken}`,
          },
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if verification fails', async () => {
      const mockCode = '111111';
      
      const mockError = new Error('Invalid verification code');
      (axios.post as any).mockRejectedValue(mockError);
      
      await expect(mfaService.verifyTwoFactor(mockCode)).rejects.toThrow('Invalid verification code');
    });
    
    it('should throw an error if auth token is missing', async () => {
      localStorage.removeItem('authToken');
      
      await expect(mfaService.verifyTwoFactor('123456')).rejects.toThrow('No authentication token found');
    });
  });
  
  describe('disableTwoFactor', () => {
    it('should successfully disable two-factor authentication', async () => {
      const mockCode = '123456';
      
      const mockResponse = {
        data: {
          message: 'Two-factor authentication disabled successfully',
        },
      };
      
      (axios.post as any).mockResolvedValue(mockResponse);
      
      const result = await mfaService.disableTwoFactor(mockCode);
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockBaseUrl}/auth/2fa/disable`,
        { code: mockCode },
        {
          headers: {
            Authorization: `Bearer ${mockAuthToken}`,
          },
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if disabling fails', async () => {
      const mockCode = '111111';
      
      const mockError = new Error('Invalid verification code');
      (axios.post as any).mockRejectedValue(mockError);
      
      await expect(mfaService.disableTwoFactor(mockCode)).rejects.toThrow('Invalid verification code');
    });
    
    it('should throw an error if auth token is missing', async () => {
      localStorage.removeItem('authToken');
      
      await expect(mfaService.disableTwoFactor('123456')).rejects.toThrow('No authentication token found');
    });
  });
  
  describe('checkTwoFactorStatus', () => {
    it('should successfully return two-factor authentication status', async () => {
      const mockResponse = {
        data: {
          enabled: true,
          verified: true,
        },
      };
      
      (axios.get as any).mockResolvedValue(mockResponse);
      
      const result = await mfaService.checkTwoFactorStatus();
      
      expect(axios.get).toHaveBeenCalledWith(`${mockBaseUrl}/auth/2fa/status`, {
        headers: {
          Authorization: `Bearer ${mockAuthToken}`,
        },
      });
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if status check fails', async () => {
      const mockError = new Error('Failed to check two-factor authentication status');
      (axios.get as any).mockRejectedValue(mockError);
      
      await expect(mfaService.checkTwoFactorStatus()).rejects.toThrow('Failed to check two-factor authentication status');
    });
    
    it('should throw an error if auth token is missing', async () => {
      localStorage.removeItem('authToken');
      
      await expect(mfaService.checkTwoFactorStatus()).rejects.toThrow('No authentication token found');
    });
  });
  
  describe('mockImplementation', () => {
    beforeEach(() => {
      // Force using mock implementation
      process.env.NODE_ENV = 'test';
    });
    
    it('should return mock data for setupTwoFactor', async () => {
      const result = await mfaService.mockSetup();
      
      expect(result).toEqual({
        secret: expect.any(String),
        qrCodeUrl: expect.any(String),
      });
    });
    
    it('should return success for enableTwoFactor with valid code', async () => {
      const result = await mfaService.mockEnable('TEST_SECRET', '123456');
      
      expect(result).toEqual({
        message: 'Two-factor authentication enabled successfully',
      });
    });
    
    it('should return error for enableTwoFactor with invalid code', async () => {
      await expect(mfaService.mockEnable('TEST_SECRET', 'invalid')).rejects.toThrow('Invalid verification code');
    });
    
    it('should return success for verifyTwoFactor with valid code', async () => {
      const result = await mfaService.mockVerify('123456');
      
      expect(result).toEqual({
        message: 'Two-factor authentication verified successfully',
      });
    });
    
    it('should return error for verifyTwoFactor with invalid code', async () => {
      await expect(mfaService.mockVerify('invalid')).rejects.toThrow('Invalid verification code');
    });
    
    it('should return success for disableTwoFactor with valid code', async () => {
      const result = await mfaService.mockDisable('123456');
      
      expect(result).toEqual({
        message: 'Two-factor authentication disabled successfully',
      });
    });
    
    it('should return error for disableTwoFactor with invalid code', async () => {
      await expect(mfaService.mockDisable('invalid')).rejects.toThrow('Invalid verification code');
    });
    
    it('should return status from mockCheckStatus', async () => {
      const result = await mfaService.mockCheckStatus();
      
      expect(result).toHaveProperty('enabled');
      expect(result).toHaveProperty('verified');
    });
  });
}); 