{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "ignoreDeprecations": "5.0",

    /* Module resolution */
    "moduleResolution": "node",
    /* "allowImportingTsExtensions": true, */
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,

    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },
    "types": ["cypress", "node", "jest"]
  },
  "include": ["src/**/*", "cypress/**/*.ts", "cypress.config.ts", "cypress.d.ts", "src/types/*.d.ts"],
  "exclude": ["node_modules", "build", "dist"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
