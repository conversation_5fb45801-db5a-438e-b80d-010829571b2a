importScripts('https://cdn.jsdelivr.net/npm/pako@2.1.0/dist/pako.min.js');

self.addEventListener('message', async (e) => {
  const { data, action } = e.data;

  try {
    let result;
    if (action === 'compress') {
      const stringData = JSON.stringify(data);
      const uint8Array = new TextEncoder().encode(stringData);
      result = pako.deflate(uint8Array);
    } else if (action === 'decompress') {
      const inflated = pako.inflate(data);
      const stringData = new TextDecoder().decode(inflated);
      result = JSON.parse(stringData);
    }
    self.postMessage(result);
  } catch (error) {
    self.postMessage({ error: error.message });
  }
});
