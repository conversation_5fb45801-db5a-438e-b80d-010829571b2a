import { useState } from 'react'
import './App.css'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="container">
      <h1>Retreat & Be - Application simplifiée</h1>
      <p>Cette application React simplifiée est conçue pour tester si Vite fonctionne correctement.</p>
      
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          Compteur: {count}
        </button>
        <p>
          Modifiez <code>src/App.jsx</code> et sauvegardez pour tester le Hot Module Replacement.
        </p>
      </div>
      
      <p className="read-the-docs">
        Cette application utilise une configuration Vite minimale.
      </p>
    </div>
  )
}

export default App
