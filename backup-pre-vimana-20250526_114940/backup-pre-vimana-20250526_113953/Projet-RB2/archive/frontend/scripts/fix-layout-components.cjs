const fs = require('fs');
const path = require('path');

// Fonction principale pour corriger un fichier
function processFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ File not found: ${filePath}`);
    return 0;
  }
  
  console.log(`📂 Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  let corrections = 0;
  
  // Correction des erreurs communes JSX
  
  // 1. Corriger les fermetures de balises mal placées (</div> -> </span> si <span> est ouvert)
  content = fixMismatchedTags(content);
  
  // 2. Corriger les imports avec des accolades mal placées
  content = content.replace(/};\s*from\s+["'][^"']+["']/g, '} from $&'.substring(2));
  
  // 3. Corriger les expressions JSX avec des parenthèses manquantes
  content = fixMissingParentheses(content);
  
  // 4. Corriger les template literals avec des backticks incorrects
  content = fixTemplateLiterals(content);
  
  // 5. Corriger les appels useAuth; () -> useAuth()
  content = content.replace(/useAuth;\s*\(\)/g, 'useAuth()');
  
  // 6. Supprimer les points-virgules après les accolades fermantes dans les blocs JSX
  content = content.replace(/}\s*;(\s*<)/g, '}$1');
  
  // 7. Remplacer les fermetures de balises </Link> incorrectes par </div> lorsque approprié
  content = content.replace(/<\/Link>(\s*)<\/nav>/g, '</div>$1</nav>');
  content = content.replace(/<\/Link>(\s*)<\/div>/g, '</div>$1</div>');
  
  // 8. Corriger les balises auto-fermantes (pas d'espace avant />)
  content = content.replace(/\s+\/>/g, '/>');
  
  // 9. Corriger la ponctuation après les balises JSX
  content = content.replace(/<\/div>\s*;\s*/g, '</div>\n');
  
  // 10. Corriger les expressions de JSX fragmentées
  content = content.replace(/<>([^<>]*)<\/>/g, '<React.Fragment>$1</React.Fragment>');
  
  // 11. Corriger l'utilisation incorrecte de `;` après les déclarations useEffect
  content = content.replace(/}\s*,\s*\[\]\)\s*\n/g, '}, [])\n');
  
  // 12. Correction des interpolations JSX incorrectes
  content = fixJSXInterpolations(content);
  
  // Corrections spécifiques pour Layout.tsx
  if (filePath.endsWith('Layout.tsx')) {
    // Corriger les fermetures de balises manquantes
    content = content.replace(/<Snackbar[^>]*>[^<]*<\/div><\/div>/g, '<Snackbar>...</Snackbar>');
  }
  
  // Corrections spécifiques pour PublicNavbar.tsx
  if (filePath.endsWith('PublicNavbar.tsx')) {
    // Corriger les mentions S'inscrire qui peuvent causer des problèmes d'échappement
    content = content.replace(/S'inscrire/g, "S\\'inscrire");
  }
  
  // Si des corrections ont été apportées, enregistrez le fichier
  if (content !== originalContent) {
    corrections = countDifferences(originalContent, content);
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed approximately ${corrections} issues in ${filePath}`);
  } else {
    console.log(`ℹ️ No changes made to ${filePath}`);
  }
  
  return corrections;
}

// Fonction pour corriger les balises mal appariées
function fixMismatchedTags(content) {
  // Remplacer les balises de fermeture incorrectes
  const openTagsMap = {
    'div': '</div>',
    'span': '</span>',
    'button': '</button>',
    'IconButton': '</IconButton>',
    'AppBar': '</AppBar>',
    'Toolbar': '</Toolbar>',
    'Typography': '</Typography>',
    'Box': '</Box>',
    'MenuItem': '</MenuItem>',
    'Menu': '</Menu>',
    'Link': '</Link>',
    'StyledLink': '</StyledLink>',
    'Button': '</Button>',
    'Paper': '</Paper>',
    'Snackbar': '</Snackbar>',
    'Container': '</Container>',
    'h3': '</h3>',
    'main': '</main>',
    'nav': '</nav>',
    'User': '</User>',
    'LogOut': '</LogOut>',
    'Outlet': '</Outlet>'
  };
  
  // Trouver toutes les balises ouvrantes et leurs balises fermantes correspondantes
  const lines = content.split('\n');
  const stack = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    // Trouver les balises ouvrantes
    const openMatches = line.match(/<([A-Z][a-zA-Z0-9]*|[a-z]+)(\s+[^>]*)?>/g);
    if (openMatches) {
      for (const match of openMatches) {
        const tagName = match.match(/<([A-Z][a-zA-Z0-9]*|[a-z]+)/)[1];
        stack.push(tagName);
      }
    }
    
    // Trouver les balises fermantes incorrectes
    const closeMatches = line.match(/<\/([A-Z][a-zA-Z0-9]*|[a-z]+)>/g);
    if (closeMatches) {
      for (const match of closeMatches) {
        const tagName = match.match(/<\/([A-Z][a-zA-Z0-9]*|[a-z]+)>/)[1];
        // Si la balise fermante ne correspond pas à la dernière balise ouvrante
        if (stack.length > 0 && stack[stack.length - 1] !== tagName) {
          const expectedTag = stack.pop();
          if (expectedTag && openTagsMap[expectedTag]) {
            // Remplacer la balise fermante incorrecte par la balise attendue
            lines[i] = lines[i].replace(match, openTagsMap[expectedTag]);
          }
        } else if (stack.length > 0) {
          stack.pop(); // Balise fermante correcte
        }
      }
    }
  }
  
  return lines.join('\n');
}

// Fonction pour corriger les parenthèses manquantes dans les expressions JSX
function fixMissingParentheses(content) {
  // Corriger les expressions comme onClick={() => ... sans parenthèse fermante
  return content.replace(/onClick={\s*\(\)\s*=>\s*([^}]+)(?!\)}\s*)/g, 'onClick={() => $1)}');
}

// Fonction pour corriger les template literals mal formés
function fixTemplateLiterals(content) {
  // Corriger les template literals comme `${styles.drawer}` ${isOpen ? ... -> `${styles.drawer} ${isOpen ? ...
  return content.replace(/`\${([^`}]+)}`\s*\${/g, '`${$1} ${');
}

// Fonction pour corriger les interpolations JSX incorrectes
function fixJSXInterpolations(content) {
  // Remplacer les expressions comme {item.label}</div> par {item.label}</div>
  return content.replace(/{([^{}]+)}\s*<\/([a-zA-Z0-9]+)>/g, (match, expr, tag) => {
    // Ne pas modifier si l'expression est correcte
    if (expr.includes('?') && !expr.includes(':')) {
      return match; // Ceci est probablement une expression conditionnelle incomplète
    }
    return `{${expr}}</${tag}>`;
  });
}

// Fonction pour compter approximativement les différences
function countDifferences(original, modified) {
  const originalLines = original.split('\n');
  const modifiedLines = modified.split('\n');
  
  let differences = 0;
  for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
    if (i >= originalLines.length || i >= modifiedLines.length || originalLines[i] !== modifiedLines[i]) {
      differences++;
    }
  }
  
  return differences;
}

// Fonction pour parcourir un répertoire et corriger tous les fichiers
function walkDir(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      fileList = walkDir(filePath, fileList);
    } else if (stat.isFile() && filePath.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Fonction principale pour corriger les composants layout
function fixLayoutComponents() {
  console.log('🔧 Starting to fix layout components...');
  
  const layoutDir = path.join(process.cwd(), 'src', 'components', 'layout');
  
  if (!fs.existsSync(layoutDir)) {
    console.log(`⚠️ Layout directory not found: ${layoutDir}`);
    return 0;
  }
  
  // Liste des fichiers à corriger spécifiquement
  const specificFiles = [
    path.join(layoutDir, 'Layout.tsx'),
    path.join(layoutDir, 'MainLayout.tsx'),
    path.join(layoutDir, 'MobileDrawer', 'MobileDrawer.tsx'),
    path.join(layoutDir, 'MobileOptimizedLayout.tsx'),
    path.join(layoutDir, 'PrivateLayout.tsx'),
    path.join(layoutDir, 'ProfileMenu', 'ProfileMenu.tsx'),
    path.join(layoutDir, 'PublicLayout.tsx'),
    path.join(layoutDir, 'PublicNavbar.tsx')
  ];
  
  let totalCorrections = 0;
  
  // Corriger d'abord les fichiers spécifiques
  for (const file of specificFiles) {
    if (fs.existsSync(file)) {
      totalCorrections += processFile(file);
    } else {
      console.log(`⚠️ File not found: ${file}`);
    }
  }
  
  // Puis parcourir tous les autres fichiers du répertoire layout
  const allLayoutFiles = walkDir(layoutDir);
  
  for (const file of allLayoutFiles) {
    // Vérifier si le fichier n'a pas déjà été traité
    if (!specificFiles.includes(file)) {
      totalCorrections += processFile(file);
    }
  }
  
  console.log(`✨ Total issues fixed in layout components: ${totalCorrections}`);
  return totalCorrections;
}

// Exécuter les corrections
let totalCorrections = fixLayoutComponents();
console.log(`🎉 Script completed! Fixed approximately ${totalCorrections} issues.`); 