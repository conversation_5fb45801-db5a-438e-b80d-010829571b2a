const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const { writeFileSync } = require('fs');
const { join } = require('path');

const THRESHOLD = {
  performance: 90,
  accessibility: 90,
  'best-practices': 90,
  seo: 90,
  pwa: 90,
};

async function runLighthouse(url) {
  // Lancer Chrome
  const chrome = await chromeLauncher.launch({
    chromeFlags: ['--headless', '--no-sandbox']
  });

  // Configuration de Lighthouse
  const options = {
    logLevel: 'info',
    output: 'html',
    port: chrome.port,
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
    strategy: 'mobile',
  };

  try {
    // Exécuter l'audit
    const results = await lighthouse(url, options);

    // Sauvegarder le rapport
    const reportPath = join(__dirname, '../reports/lighthouse');
    writeFileSync(
      `${reportPath}/report-${new Date().toISOString()}.html`,
      results.report
    );

    // Vérifier les scores
    const scores = Object.entries(results.lhr.categories).reduce(
      (acc, [key, category]) => ({
        ...acc,
        [key]: Math.round(category.score * 100),
      }),
      {}
    );

    console.log('\nLighthouse Scores:');
    console.table(scores);

    // Vérifier les seuils
    const failed = Object.entries(THRESHOLD).filter(
      ([key, threshold]) => scores[key] < threshold
    );

    if (failed.length > 0) {
      console.error('\nFailed Thresholds:');
      failed.forEach(([key, threshold]) => {
        console.error(`${key}: ${scores[key]} < ${threshold}`);
      });
      process.exit(1);
    }

    console.log('\nAll performance thresholds passed! 🎉');
  } catch (error) {
    console.error('Error running Lighthouse:', error);
    process.exit(1);
  } finally {
    await chrome.kill();
  }
}

// URLs à tester
const urls = [
  'http://localhost:3000',
  'http://localhost:3000/marketplace',
  'http://localhost:3000/profile',
];

// Exécuter les tests pour chaque URL
(async () => {
  for (const url of urls) {
    console.log(`\nRunning Lighthouse for ${url}`);
    await runLighthouse(url);
  }
})();
