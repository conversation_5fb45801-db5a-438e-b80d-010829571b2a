export interface SecurityIssue {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location?: string;
  recommendation: string;
  category: 'csp' | 'headers' | 'storage' | 'input' | 'authentication' | 'authorization' | 'dependency'
}

export interface SecurityReport {
  timestamp: string;
  issues: SecurityIssue[];
  summary: {
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
    passedChecks: number
  }
  passed: boolean;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}