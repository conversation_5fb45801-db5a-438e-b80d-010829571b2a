#!/bin/bash

echo "Début de la correction des erreurs TypeScript..."

# 1. Corriger les erreurs d'importation basiques
./frontend/fix-import-errors.sh

# 2. Corriger les erreurs de syntaxe
./frontend/fix-syntax-issues.sh

# 3. Corriger les erreurs TypeScript spécifiques
./frontend/scripts/fix-typescript-errors.sh

# 4. Corriger les erreurs finales
node ./frontend/scripts/fix-final-errors.cjs

# 5. Vérifier les erreurs restantes
npx tsc --noEmit
