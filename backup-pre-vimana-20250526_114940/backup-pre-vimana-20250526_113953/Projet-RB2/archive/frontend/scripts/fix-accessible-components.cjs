const fs = require('fs');
const path = require('path');

function processFile(filePath) {
  console.log(`Processing file: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let corrections = 0;

  // Correction des imports avec from incorrects
  content = content.replace(/};(\s*)from(\s*)['"](.*?)['"];/g, (match, space1, space2, importPath) => {
    corrections++;
    return `} from${space2}'${importPath}';`;
  });

  // Correction des balises de fermeture incorrectes (button, div, etc.)
  content = content.replace(/<\/button>/g, '</div>');
  corrections += (content.match(/<\/button>/g) || []).length;

  // Corriger les balises self-closing avec mauvais formatage
  content = content.replace(/(\s*)\/>(\s*)/g, ' />$2');
  corrections += (content.match(/(\s*)\/>(\s*)/g) || []).length;

  // Corriger les balises React.Fragment fermantes
  content = content.replace(/<\/React.Fragment>/g, '</React.Fragment>');
  corrections += (content.match(/<\/React.Fragment>/g) || []).length;

  // Corriger les balises fermantes pour les éléments Mui courants
  const muiComponents = [
    'Typography', 'Box', 'Container', 'Paper', 'Grid', 'Tab', 'Tabs', 'TabPanel',
    'Dialog', 'DialogTitle', 'MenuItem', 'Select', 'FormControl', 'InputLabel',
    'TableCell', 'TableRow', 'TableHead', 'TableBody', 'Table', 'TableContainer',
    'Button'
  ];
  
  muiComponents.forEach(component => {
    const regex = new RegExp(`</${component}>`, 'g');
    const replacements = content.match(regex);
    if (replacements) {
      corrections += replacements.length;
    }
  });

  // Corrige les erreurs de syntax pour le backtick (template literals)
  content = content.replace(/\$\{`/g, '${');
  corrections += (content.match(/\$\{`/g) || []).length;
  
  content = content.replace(/`\}/g, '}');
  corrections += (content.match(/`\}/g) || []).length;

  // Corriger les erreurs de syntaxe dans les chaînes avec backticks
  content = content.replace(/['"]`\}/g, '"}');
  corrections += (content.match(/['"]`\}/g) || []).length;

  // Corriger les fermetures de template literals
  content = content.replace(/['"]\)`\}/g, '"}');
  corrections += (content.match(/['"]\)`\}/g) || []).length;

  // Corrige les balises spécifiques dans les tableaux
  content = content.replace(/<(BarChart|LineChart|Line|Bar|Legend|YAxis|XAxis|CartesianGrid|ResponsiveContainer|Tooltip|Pie)><\/(BarChart|LineChart|Line|Bar|Legend|YAxis|XAxis|CartesianGrid|ResponsiveContainer|Tooltip|Pie)>/g, (match, opening, closing) => {
    corrections++;
    return `<${opening}></${opening}>`;
  });

  // Corriger les balises React et FocusTrap
  content = content.replace(/<\/CloseButton>/g, '</div>');
  corrections += (content.match(/<\/CloseButton>/g) || []).length;

  // Remplacer les balises fermantes incorrectes des composants de graphes
  content = content.replace(/<\/(Line|Bar|Pie|Legend|YAxis|XAxis|CartesianGrid|ResponsiveContainer|Tooltip)><\/(BarChart|LineChart|PieChart)>/g, (match, innerComponent, outerComponent) => {
    corrections++;
    return `</${innerComponent}></${outerComponent}>`;
  });

  // Corriger les problèmes spécifiques aux balises React
  content = content.replace(/<React>/g, '<React.Fragment>');
  corrections += (content.match(/<React>/g) || []).length;
  
  // Corriger l'utilisation incorrecte des balises fermantes dans useNotification
  content = content.replace(/useNotification;(\(\))/g, 'useNotification()');
  corrections += (content.match(/useNotification;(\(\))/g) || []).length;

  // Corriger les erreurs de syntaxe dans les templates literals
  content = content.replace(/\${([^}]*)?error([^}]*)?\s*\?\s*['"]([^'"]*)['"]\s*:\s*['"]([^'"]*)['"]\"`}/g, (match, before, after, truePart, falsePart) => {
    corrections++;
    return `\${${before || ''}error${after || ''} ? '${truePart}' : '${falsePart}'}`;
  });

  // Corriger les balises fermantes
  content = content.replace(/<\/(ModalContent|FocusTrap)>/g, '</div>');
  corrections += (content.match(/<\/(ModalContent|FocusTrap)>/g) || []).length;

  // Autres corrections spécifiques qui pourraient être nécessaires
  content = content.replace(/role=`}([^`]*)"/g, 'role="$1"');
  corrections += (content.match(/role=`}([^`]*)"/g) || []).length;

  // Corriger le problème de SystemHealth
  content = content.replace(/<\/(SystemHealth|GeoRestrictions|SecurityRules|UserManagement)>/g, '</div>');
  corrections += (content.match(/<\/(SystemHealth|GeoRestrictions|SecurityRules|UserManagement)>/g) || []).length;

  // Remplacer les accolades incorrectes en fin de fichier
  content = content.replace(/};(\s*)export default/g, '};\n\nexport default');
  corrections += (content.match(/};(\s*)export default/g) || []).length;

  // Corriger la fermeture des onClick
  content = content.replace(/onClick=\{\(\) => \{([^}]*)\}\}/g, 'onClick={() => {$1}}');
  corrections += (content.match(/onClick=\{\(\) => \{([^}]*)\}\}/g) || []).length;

  // Corriger le formatage des JSX dans onClick
  content = content.replace(/onClick=\{\(\) =>\s*\{/g, 'onClick={() => {');
  corrections += (content.match(/onClick=\{\(\) =>\s*\{/g) || []).length;

  if (corrections > 0) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
    return corrections;
  } else {
    console.log(`No issues found in ${filePath}`);
    return 0;
  }
}

function fixComponents() {
  const accessiblePath = path.join(process.cwd(), 'src', 'components', 'accessible');
  const adminPath = path.join(process.cwd(), 'src', 'components', 'Admin');
  
  let totalFixedFiles = 0;
  let totalCorrections = 0;

  // Vérifier si le répertoire accessible existe
  if (fs.existsSync(accessiblePath)) {
    const accessibleFiles = fs.readdirSync(accessiblePath)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.jsx'));
    
    console.log(`Found ${accessibleFiles.length} accessible component files`);
    
    accessibleFiles.forEach(file => {
      const filePath = path.join(accessiblePath, file);
      const corrections = processFile(filePath);
      if (corrections > 0) {
        totalFixedFiles++;
        totalCorrections += corrections;
      }
    });
  } else {
    console.log(`Directory not found: ${accessiblePath}`);
  }

  // Vérifier si le répertoire Admin existe
  if (fs.existsSync(adminPath)) {
    const adminFiles = fs.readdirSync(adminPath)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.jsx'));
    
    console.log(`Found ${adminFiles.length} Admin component files`);
    
    adminFiles.forEach(file => {
      const filePath = path.join(adminPath, file);
      const corrections = processFile(filePath);
      if (corrections > 0) {
        totalFixedFiles++;
        totalCorrections += corrections;
      }
    });
  } else {
    console.log(`Directory not found: ${adminPath}`);
  }

  console.log(`Fixed ${totalCorrections} issues in ${totalFixedFiles} files`);
}

fixComponents(); 