const fs = require('fs');
const path = require('path');

function processFile(filePath) {
  console.log(`Processing file: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let originalContent = content;
  let corrections = 0;

  // Correction des balises incorrectes et incomplètes
  const tagPairs = {
    'li': 'div',
    'span': 'div',
    'h2': 'h2',
    'h3': 'h3',
    'p': 'p',
    'button': 'Button',
    'label': 'label',
    'option': 'option',
    'select': 'select',
    'form': 'form',
    'input': 'input',
    'Grid': 'Grid',
    'TextField': 'TextField',
    'Box': 'Box',
    'MenuItem': 'MenuItem',
    'CircularProgress': 'CircularProgress',
    'FaCalendar': 'FaCalendar',
    'Typography': 'Typography',
    'DialogTitle': 'DialogTitle',
    'DialogContent': 'DialogContent'
  };
  
  for (const [tag, correctTag] of Object.entries(tagPairs)) {
    // Correction des balises fermantes incorrectes
    const closingRegex = new RegExp(`<\\/(?:${tag}|li|span)>(?:\\s*)?<\\/(?:${correctTag}|div)>`, 'g');
    content = content.replace(closingRegex, `</${correctTag}>`);
    const closingRegexMatches = [...content.matchAll(closingRegex)];
    corrections += closingRegexMatches.length;
    
    // Ajout des balises fermantes manquantes
    const openingTagRegex = new RegExp(`<${tag}([^>]*)>([^<]*?)(?=<)`, 'g');
    let matches = [...content.matchAll(openingTagRegex)];
    
    for (const match of matches) {
      const fullMatch = match[0];
      const tagAttributes = match[1];
      const tagContent = match[2];
      
      // Vérifier si la balise n'a pas de fermeture appropriée
      const closeTagRegex = new RegExp(`</${tag}>|</${correctTag}>`, 'g');
      const hasCloseTag = closeTagRegex.test(content.substring(match.index + fullMatch.length));
      
      if (!hasCloseTag) {
        // Trouver où insérer la balise fermante
        let insertPos = match.index + fullMatch.length;
        while (insertPos < content.length) {
          if (content[insertPos] === '<' && content[insertPos + 1] !== '/') {
            break;
          }
          insertPos++;
        }
        
        // Insérer la balise fermante
        content = content.substring(0, insertPos) + 
                  `</${tag === 'li' || tag === 'span' ? correctTag : tag}>` + 
                  content.substring(insertPos);
        corrections++;
      }
    }
  }

  // Correction des balises auto-fermantes
  content = content.replace(/<([A-Za-z0-9.]+)([^>]*?)\/>/g, '<$1$2 />');
  corrections += (content.match(/<([A-Za-z0-9.]+)([^>]*?)\/>/g) || []).length;

  // Correction des expressions incorrectes dans les attributs et JSX
  content = content.replace(/e;\.target/g, 'e.target');
  corrections += (content.match(/e;\.target/g) || []).length;

  // Correction des backticks
  content = content.replace(/`\}/g, '}');
  corrections += (content.match(/`\}/g) || []).length;

  // Correction des expressions lambda incomplètes
  content = content.replace(/\}\)(\s*)catch/g, '}).catch');
  corrections += (content.match(/\}\)(\s*)catch/g) || []).length;

  // Correction des useEffect manquant de point-virgule
  content = content.replace(/}, \[\]\)(\r\n|\n|\r)/g, '}, []);\n');
  corrections += (content.match(/}, \[\]\)(\r\n|\n|\r)/g) || []).length;

  // Correction des tokens inattendus dans CircularProgress
  content = content.replace(/CircularProgress size={20}\s*\/>s\*:/g, 'CircularProgress size={20} /> :');
  corrections += (content.match(/CircularProgress size={20}\s*\/>s\*:/g) || []).length;

  // Correction de l'erreur avec les parenthèses de fermeture dans les expressions conditionnelles
  content = content.replace(/{(\s*\w+\s*)\s*&&\s*\(/g, '{$1 && (');
  corrections += (content.match(/{(\s*\w+\s*)\s*&&\s*\(/g) || []).length;

  // Correction des fragments JSX incomplets
  content = content.replace(/<\s*\/\s*><\/div>/g, '</></div>');
  corrections += (content.match(/<\s*\/\s*><\/div>/g) || []).length;
  
  content = content.replace(/<\s*\/\s*>/g, '</>');
  corrections += (content.match(/<\s*\/\s*>/g) || []).length;

  // Correction des interpolations numériques incorrectes
  content = content.replace(/\(\{counts\.(\w+)\)\}/g, '({counts.$1})');
  corrections += (content.match(/\(\{counts\.(\w+)\)\}/g) || []).length;

  // Correction des balises mal fermées ou incomplètes
  // Pour les balises Button, etc.
  content = content.replace(/<\/button>/g, '</Button>');
  corrections += (content.match(/<\/button>/g) || []).length;

  // Correction des attributs incorrects des MenuItem et TextField
  content = content.replace(/<MenuItem([^>]*?)>([^<]*?)<\/div>/g, '<MenuItem$1>$2</MenuItem>');
  corrections += (content.match(/<MenuItem([^>]*?)>([^<]*?)<\/div>/g) || []).length;

  // Correction des mauvaises fermetures de balise dialogId
  content = content.replace(/<DialogTitle([^>]*?)>([^<]*?)<\/div>/g, '<DialogTitle$1>$2</DialogTitle>');
  corrections += (content.match(/<DialogTitle([^>]*?)>([^<]*?)<\/div>/g) || []).length;

  // Correction des textes d'option
  content = content.replace(/<option([^>]*?)>([^<]*?)<\/div>/g, '<option$1>$2</option>');
  corrections += (content.match(/<option([^>]*?)>([^<]*?)<\/div>/g) || []).length;

  // Correction des erreurs de select non fermés
  content = content.replace(/<select([^>]*?)>([^<]*?)<\/div>/g, '<select$1>$2</select>');
  corrections += (content.match(/<select([^>]*?)>([^<]*?)<\/div>/g) || []).length;

  // Vérification et ajout de balises manquantes pour des fichiers incomplets
  if (filePath.includes('CarCard.tsx') || filePath.includes('EventReminderDialog.tsx')) {
    if (!content.endsWith('};') && !content.endsWith('}')) {
      content += '\n};';
      corrections++;
    }
  }

  if (corrections > 0 && content !== originalContent) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
    return corrections;
  } else {
    console.log(`No changes made to ${filePath}`);
    return 0;
  }
}

function fixComponents() {
  const rootDir = process.cwd();
  const calendarPath = path.join(rootDir, 'src', 'components', 'Calendar');
  const carRentalPath = path.join(rootDir, 'src', 'components', 'car-rental');
  const chartsPath = path.join(rootDir, 'src', 'components', 'charts');
  const additionalFiles = [
    path.join(rootDir, 'src', 'components', 'CarRentalManager.tsx')
  ];
  
  let totalFixedFiles = 0;
  let totalCorrections = 0;

  // Vérifier et corriger les fichiers du répertoire Calendar
  if (fs.existsSync(calendarPath)) {
    console.log(`Processing Calendar components...`);
    
    fs.readdirSync(calendarPath)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.jsx'))
      .forEach(file => {
        const filePath = path.join(calendarPath, file);
        const corrections = processFile(filePath);
        if (corrections > 0) {
          totalFixedFiles++;
          totalCorrections += corrections;
        }
      });
  } else {
    console.log(`Directory not found: ${calendarPath}`);
  }

  // Vérifier et corriger les fichiers du répertoire car-rental
  if (fs.existsSync(carRentalPath)) {
    console.log(`Processing car-rental components...`);
    
    fs.readdirSync(carRentalPath)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.jsx'))
      .forEach(file => {
        const filePath = path.join(carRentalPath, file);
        const corrections = processFile(filePath);
        if (corrections > 0) {
          totalFixedFiles++;
          totalCorrections += corrections;
        }
      });
  } else {
    console.log(`Directory not found: ${carRentalPath}`);
  }

  // Vérifier et corriger les fichiers du répertoire charts
  if (fs.existsSync(chartsPath)) {
    console.log(`Processing charts components...`);
    
    fs.readdirSync(chartsPath)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.jsx'))
      .forEach(file => {
        const filePath = path.join(chartsPath, file);
        const corrections = processFile(filePath);
        if (corrections > 0) {
          totalFixedFiles++;
          totalCorrections += corrections;
        }
      });
  } else {
    console.log(`Directory not found: ${chartsPath}`);
  }

  // Vérifier et corriger les fichiers additionnels
  console.log(`Processing additional files...`);
  
  additionalFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const corrections = processFile(filePath);
      if (corrections > 0) {
        totalFixedFiles++;
        totalCorrections += corrections;
      }
    } else {
      console.log(`File not found: ${filePath}`);
    }
  });

  console.log(`Fixed ${totalCorrections} issues in ${totalFixedFiles} files`);
}

fixComponents(); 