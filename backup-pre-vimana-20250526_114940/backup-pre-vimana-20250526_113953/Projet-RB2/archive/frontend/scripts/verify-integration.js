/**
 * Script pour vérifier que l'intégration de Front-Audrey-V1-Main-main est complète et fonctionnelle
 */

import { spawn } from 'child_process';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le chemin du répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Fonction pour exécuter une commande
function runCommand(command, args, cwd = rootDir) {
  return new Promise((resolve, reject) => {
    console.log(chalk.blue(`Exécution de la commande: ${command} ${args.join(' ')}`));
    
    const process = spawn(command, args, { stdio: 'inherit', cwd });
    
    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`La commande a échoué avec le code ${code}`));
      }
    });
    
    process.on('error', (error) => {
      reject(error);
    });
  });
}

// Fonction pour vérifier l'existence d'un fichier ou d'un répertoire
function checkFileOrDirectory(path, type = 'file') {
  const exists = fs.existsSync(path);
  const isDirectory = exists && fs.statSync(path).isDirectory();
  const isFile = exists && fs.statSync(path).isFile();
  
  if (type === 'file' && isFile) {
    console.log(chalk.green(`✓ Le fichier ${path} existe.`));
    return true;
  } else if (type === 'directory' && isDirectory) {
    console.log(chalk.green(`✓ Le répertoire ${path} existe.`));
    return true;
  } else {
    console.log(chalk.red(`✗ Le ${type === 'file' ? 'fichier' : 'répertoire'} ${path} n'existe pas.`));
    return false;
  }
}

// Fonction pour vérifier la structure des répertoires
function checkDirectoryStructure() {
  console.log(chalk.yellow('\n=== Vérification de la structure des répertoires ==='));
  
  let success = true;
  
  // Vérifier les répertoires de composants
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend'), 'directory') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/atoms'), 'directory') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/molecules'), 'directory') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/organisms'), 'directory') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/templates'), 'directory') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/ui'), 'directory') && success;
  
  // Vérifier les répertoires de pages
  success = checkFileOrDirectory(path.join(rootDir, 'src/pages/randbefrontend'), 'directory') && success;
  
  // Vérifier les répertoires de styles
  success = checkFileOrDirectory(path.join(rootDir, 'src/styles'), 'directory') && success;
  
  // Vérifier les répertoires de configuration
  success = checkFileOrDirectory(path.join(rootDir, 'src/config'), 'directory') && success;
  
  // Vérifier les répertoires de routes
  success = checkFileOrDirectory(path.join(rootDir, 'src/routes'), 'directory') && success;
  
  return success;
}

// Fonction pour vérifier les fichiers clés
function checkKeyFiles() {
  console.log(chalk.yellow('\n=== Vérification des fichiers clés ==='));
  
  let success = true;
  
  // Vérifier les fichiers de configuration
  success = checkFileOrDirectory(path.join(rootDir, 'src/config/audrey-integration.ts'), 'file') && success;
  
  // Vérifier les fichiers de routes
  success = checkFileOrDirectory(path.join(rootDir, 'src/routes/audreyRoutes.tsx'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/routes/combinedRoutes.tsx'), 'file') && success;
  
  // Vérifier les fichiers de styles
  success = checkFileOrDirectory(path.join(rootDir, 'src/styles/audrey-styles.css'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/styles/audrey-integration.css'), 'file') && success;
  
  // Vérifier les fichiers de documentation
  success = checkFileOrDirectory(path.join(rootDir, 'docs/AUDREY-INTEGRATION.md'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'docs/DEPLOYMENT.md'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'docs/TRAINING.md'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'docs/CONTRIBUTING.md'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'docs/QUICKSTART.md'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'docs/ROADMAP-FUTURE.md'), 'file') && success;
  
  // Vérifier les fichiers de scripts
  success = checkFileOrDirectory(path.join(rootDir, 'scripts/audrey-migration.js'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'scripts/deploy-staging.js'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'scripts/deploy-production.js'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'scripts/run-audrey-tests.js'), 'file') && success;
  
  return success;
}

// Fonction pour vérifier les composants
function checkComponents() {
  console.log(chalk.yellow('\n=== Vérification des composants ==='));
  
  let success = true;
  
  // Vérifier quelques composants atomiques
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/atoms/Button/Button.tsx'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/atoms/Icon/Icon.tsx'), 'file') && success;
  
  // Vérifier quelques composants moléculaires
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/molecules/RetreatCard/RetreatCard.tsx'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/molecules/FeedbackForm/FeedbackForm.tsx'), 'file') && success;
  
  // Vérifier quelques composants organismes
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/organisms/NavBarClient/NavBarClient.tsx'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/organisms/Footer/Footer.tsx'), 'file') && success;
  
  // Vérifier quelques templates
  success = checkFileOrDirectory(path.join(rootDir, 'src/components/randbefrontend/templates/MainLayout/MainLayout.tsx'), 'file') && success;
  
  return success;
}

// Fonction pour vérifier les pages
function checkPages() {
  console.log(chalk.yellow('\n=== Vérification des pages ==='));
  
  let success = true;
  
  // Vérifier quelques pages
  success = checkFileOrDirectory(path.join(rootDir, 'src/pages/randbefrontend/HomePage.tsx'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/pages/randbefrontend/ClientHomePage.tsx'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/pages/randbefrontend/FeedbackPage.tsx'), 'file') && success;
  
  return success;
}

// Fonction pour vérifier les tests
function checkTests() {
  console.log(chalk.yellow('\n=== Vérification des tests ==='));
  
  let success = true;
  
  // Vérifier les fichiers de test
  success = checkFileOrDirectory(path.join(rootDir, 'src/tests/audrey-integration.test.tsx'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/tests/audrey-backend-integration.test.tsx'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/tests/audrey-microservices-integration.test.tsx'), 'file') && success;
  success = checkFileOrDirectory(path.join(rootDir, 'src/tests/audrey-e2e.test.tsx'), 'file') && success;
  
  return success;
}

// Fonction pour vérifier le package.json
function checkPackageJson() {
  console.log(chalk.yellow('\n=== Vérification du package.json ==='));
  
  let success = true;
  
  // Vérifier l'existence du fichier package.json
  if (!checkFileOrDirectory(path.join(rootDir, 'package.json'), 'file')) {
    return false;
  }
  
  // Lire le fichier package.json
  const packageJson = JSON.parse(fs.readFileSync(path.join(rootDir, 'package.json'), 'utf8'));
  
  // Vérifier les scripts
  if (!packageJson.scripts) {
    console.log(chalk.red('✗ Le fichier package.json ne contient pas de section scripts.'));
    success = false;
  } else {
    // Vérifier les scripts spécifiques à l'intégration d'Audrey-V1
    const requiredScripts = ['test:audrey', 'migrate:audrey', 'deploy:staging', 'deploy:prod'];
    
    for (const script of requiredScripts) {
      if (!packageJson.scripts[script]) {
        console.log(chalk.red(`✗ Le script ${script} n'existe pas dans le fichier package.json.`));
        success = false;
      } else {
        console.log(chalk.green(`✓ Le script ${script} existe dans le fichier package.json.`));
      }
    }
  }
  
  return success;
}

// Fonction principale
async function main() {
  try {
    console.log(chalk.green('=== Vérification de l\'intégration de Front-Audrey-V1-Main-main ==='));
    
    // Vérifier la structure des répertoires
    const directoryStructureSuccess = checkDirectoryStructure();
    
    // Vérifier les fichiers clés
    const keyFilesSuccess = checkKeyFiles();
    
    // Vérifier les composants
    const componentsSuccess = checkComponents();
    
    // Vérifier les pages
    const pagesSuccess = checkPages();
    
    // Vérifier les tests
    const testsSuccess = checkTests();
    
    // Vérifier le package.json
    const packageJsonSuccess = checkPackageJson();
    
    // Vérifier si tous les tests ont réussi
    const allSuccess = directoryStructureSuccess && keyFilesSuccess && componentsSuccess && pagesSuccess && testsSuccess && packageJsonSuccess;
    
    if (allSuccess) {
      console.log(chalk.green('\n=== Vérification terminée avec succès ==='));
      console.log(chalk.green('L\'intégration de Front-Audrey-V1-Main-main est complète et fonctionnelle.'));
    } else {
      console.log(chalk.red('\n=== Vérification terminée avec des erreurs ==='));
      console.log(chalk.red('L\'intégration de Front-Audrey-V1-Main-main n\'est pas complète ou fonctionnelle.'));
      console.log(chalk.yellow('Veuillez corriger les erreurs ci-dessus avant de continuer.'));
    }
  } catch (error) {
    console.error(chalk.red(`Erreur lors de la vérification: ${error.message}`));
    process.exit(1);
  }
}

// Exécuter la fonction principale
main();
