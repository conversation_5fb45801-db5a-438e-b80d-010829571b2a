const fs = require('fs');
const path = require('path');

function fixMessageSlice() {
  const rootDir = process.cwd();
  const filePath = path.join(rootDir, 'src', 'store', 'slices', 'messageSlice.ts');
  
  if (!fs.existsSync(filePath)) {
    console.error(`File not found: ${filePath}`);
    return;
  }

  console.log(`Processing file: ${filePath}`);
  
  // Lire le contenu du fichier
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Créer un nouveau contenu corrigé en utilisant une approche ligne par ligne
  const lines = content.split("\n");
  let corrections = 0;
  
  // Rechercher les lignes problématiques et les corriger
  for (let i = 0; i < lines.length; i++) {
    // Correction pour "},;" à la fin d'une ligne
    if (lines[i].trim().match(/},;$/)) {
      lines[i] = lines[i].replace(/},;$/, '}');
      corrections++;
    }
    
    // Correction pour "};" suivi de "});"
    if (lines[i].trim() === "};);") {
      lines[i] = "});";
      corrections++;
    }
    
    // Correction pour la ligne "};);"
    if (lines[i].trim() === "};);") {
      lines[i] = "});";
      corrections++;
    }
  }
  
  // Rejoindre les lignes et écrire le fichier
  const updatedContent = lines.join("\n");
  
  if (content !== updatedContent) {
    fs.writeFileSync(filePath, updatedContent);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
  } else {
    console.log(`No changes made to ${filePath}`);
  }
}

fixMessageSlice(); 