import * as ../security/types from '../security/types';

export class SecurityNotifications {
  static async sendSlackNotification(report: SecurityReport, webhookUrl?: string): Promise<void> {
    if(!webhookUrl) { { {return}}}

    const message = {
      text: `Security Scan Report - ${report.timestamp
}`,
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*Security Scan Results*\nRisk Level: ${report.riskLevel}\nTotal Issues: ${report.summary.total}`
          }
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `Critical: ${report.summary.critical}\nHigh: ${report.summary.high}\nMedium: ${report.summary.medium}\nLow: ${report.summary.low}`
          }
        }
      ]
    }

    try {
      await fetch(webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(message)
      });
    } catch(error) {
      console.error('Failed to send Slack notification:', error)
    }
  }
}