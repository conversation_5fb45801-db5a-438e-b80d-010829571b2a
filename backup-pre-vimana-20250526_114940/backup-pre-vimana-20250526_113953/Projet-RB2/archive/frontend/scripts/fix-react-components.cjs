const fs = require('fs');
const path = require('path');

function fixReactComponents() {
  console.log('Starting React components correction...');
  
  const baseDir = path.join(__dirname, '..');
  const srcDir = path.join(baseDir, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error(`Source directory not found: ${srcDir}`);
    return;
  }
  
  // Traiter le répertoire src et tous ses sous-répertoires
  const stats = processDirectory(srcDir);
  
  console.log(`\nTotal files processed: ${stats.processed}`);
  console.log(`Total files fixed: ${stats.fixed}`);
  console.log(`Number of corrections made: ${stats.corrections}`);
}

function processDirectory(directory) {
  let stats = {
    processed: 0,
    fixed: 0,
    corrections: 0
  };
  
  // Lire tous les fichiers et sous-répertoires
  const items = fs.readdirSync(directory);
  
  // Parcourir tous les éléments du répertoire
  for (const item of items) {
    const fullPath = path.join(directory, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Si c'est un répertoire, traiter récursivement
      if (item !== 'node_modules' && item !== 'dist' && item !== 'build' && item !== '.git') {
        const subStats = processDirectory(fullPath);
        stats.processed += subStats.processed;
        stats.fixed += subStats.fixed;
        stats.corrections += subStats.corrections;
      }
    } else if ((item.endsWith('.tsx') || item.endsWith('.jsx')) && stat.isFile()) {
      // Si c'est un fichier .tsx ou .jsx, le traiter
      stats.processed++;
      try {
        const fileStats = fixFile(fullPath);
        if (fileStats.fixed) {
          stats.fixed++;
          stats.corrections += fileStats.corrections;
        }
      } catch (error) {
        console.error(`Error processing ${fullPath}: ${error.message}`);
      }
    }
  }
  
  return stats;
}

function fixFile(filePath) {
  const fileName = path.basename(filePath);
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  let corrections = 0;
  
  // Appliquer diverses corrections
  const correctionFunctions = [
    fixMismatchedJsxTags,
    fixMissingClosingParentheses,
    fixSelfClosingTags,
    fixTemplateLiterals,
    fixIncorrectFragments,
    fixMissingExportDefault,
    fixImportStatements,
    fixObjectDestructuring
  ];
  
  for (const fixFunction of correctionFunctions) {
    const result = fixFunction(content);
    content = result.content;
    corrections += result.corrections;
  }
  
  // Vérifier si le contenu a été modifié
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed: ${fileName} (${corrections} corrections)`);
    return { fixed: true, corrections };
  } else {
    console.log(`No changes needed for: ${fileName}`);
    return { fixed: false, corrections: 0 };
  }
}

function fixMismatchedJsxTags(content) {
  let corrections = 0;
  let newContent = content;
  
  // Correspondance entre balises d'ouverture et de fermeture
  const openTagsRegex = /<([A-Z][A-Za-z0-9]*|[a-z][a-z0-9]*(?:\.[A-Za-z][A-Za-z0-9]*)?)([^>]*?)>/g;
  const closeTagsRegex = /<\/([A-Z][A-Za-z0-9]*|[a-z][a-z0-9]*(?:\.[A-Za-z][A-Za-z0-9]*)?)[^>]*>/g;
  
  // Récupérer toutes les balises d'ouverture non auto-fermantes
  const openTags = [];
  let match;
  while ((match = openTagsRegex.exec(content)) !== null) {
    const [fullTag, tagName, attributes] = match;
    if (!attributes.trim().endsWith('/') && !fullTag.includes('/>')) {
      openTags.push({
        tagName,
        position: match.index
      });
    }
  }
  
  // Récupérer toutes les balises de fermeture
  const closeTags = [];
  while ((match = closeTagsRegex.exec(content)) !== null) {
    const [, tagName] = match;
    closeTags.push({
      tagName,
      position: match.index
    });
  }
  
  // Corriger les balises incorrectes ou manquantes
  // Correction des balises spécifiques connues pour être problématiques
  const knownCorrections = [
    { from: /<\/Line>/g, to: '</LineChart>' },
    { from: /<\/Bar>/g, to: '</BarChart>' },
    { from: /<\/Pie>/g, to: '</PieChart>' },
    { from: /<\/KPICard>/g, to: '</Card>' },
    { from: /<\/DataGrid>/g, to: '</div>' },
    { from: /<\/Chart>/g, to: '</div>' },
    { from: /<\/Alert>/g, to: '</div>' },
    { from: /<\/Progress>/g, to: '</div>' },
    { from: /<\/Tabs>/g, to: '</div>' },
    { from: /<\/Pagination>/g, to: '</div>' },
    { from: /<\/Select>/g, to: '</div>' },
    { from: /<\/Checkbox>/g, to: '</div>' },
    { from: /<\/Radio>/g, to: '</div>' },
    { from: /<\/Switch>/g, to: '</div>' },
    { from: /<\/Slider>/g, to: '</div>' },
    { from: /<\/Drawer>/g, to: '</div>' },
    { from: /<\/Modal>/g, to: '</div>' },
    { from: /<\/Input>/g, to: '</div>' },
    { from: /<\/Button>/g, to: '</button>' },
    { from: /<\/Typography>/g, to: '</div>' }
  ];
  
  for (const { from, to } of knownCorrections) {
    const oldContent = newContent;
    newContent = newContent.replace(from, to);
    if (oldContent !== newContent) {
      corrections += (oldContent.match(from) || []).length;
    }
  }
  
  // Correction des balises imbriquées incorrectes
  newContent = newContent.replace(/<([A-Z][A-Za-z0-9]*)([^>]*)>([^<]*)<\/([A-Z][A-Za-z0-9]*)>/g, 
    (match, openTag, attributes, content, closeTag) => {
      if (openTag !== closeTag) {
        corrections++;
        return `<${openTag}${attributes}>${content}</${openTag}>`;
      }
      return match;
    });
  
  return { content: newContent, corrections };
}

function fixMissingClosingParentheses(content) {
  let corrections = 0;
  let newContent = content;
  
  // Correction des parenthèses manquantes à la fin des fonctions
  const funcRegex = /export\s+const\s+([A-Za-z][A-Za-z0-9_]*)\s*=\s*\(\s*([^)]*)\s*\)\s*=>\s*\{/g;
  const funcEnds = /\n\}(?!\s*;|\s*\)|\s*,|\s*\n\s*export)/g;
  
  newContent = newContent.replace(funcEnds, (match) => {
    corrections++;
    return '\n};';
  });
  
  // Correction des parenthèses manquantes à la fin des hooks
  const hookRegex = /useEffect\(\(\)\s*=>\s*\{([^}]*)\}(?!\s*,|\s*\))/g;
  newContent = newContent.replace(hookRegex, (match, body) => {
    corrections++;
    return `useEffect(() => {${body}}, [])`;
  });
  
  return { content: newContent, corrections };
}

function fixSelfClosingTags(content) {
  let corrections = 0;
  let newContent = content;
  
  // Correction des balises auto-fermantes
  const selfClosingRegex = /(<[A-Za-z][A-Za-z0-9]*[^>]*[^\/])>(\s*)<\//g;
  newContent = newContent.replace(selfClosingRegex, (match, start, space) => {
    corrections++;
    return `${start}/>${space}</`;
  });
  
  // Correction des balises auto-fermantes impropres
  const improperSelfClosingRegex = /(<[A-Za-z][A-Za-z0-9]*[^>]*[^\/])\/>/g;
  newContent = newContent.replace(improperSelfClosingRegex, (match, start) => {
    corrections++;
    return `${start} />`;
  });
  
  return { content: newContent, corrections };
}

function fixTemplateLiterals(content) {
  let corrections = 0;
  let newContent = content;
  
  // Correction des template literals incorrects dans les attributs className
  const classNameTemplateRegex = /className="([^"]*)\${([^}]*)}([^"]*)"/g;
  newContent = newContent.replace(classNameTemplateRegex, (match, before, expr, after) => {
    corrections++;
    return `className={\`${before}\${${expr}}${after}\`}`;
  });
  
  // Correction des template literals incorrects dans d'autres attributs
  const otherTemplateRegex = /([a-zA-Z0-9]+)="([^"]*)\${([^}]*)}([^"]*)"/g;
  newContent = newContent.replace(otherTemplateRegex, (match, attr, before, expr, after) => {
    if (attr !== 'className') {
      corrections++;
      return `${attr}={\`${before}\${${expr}}${after}\`}`;
    }
    return match;
  });
  
  return { content: newContent, corrections };
}

function fixIncorrectFragments(content) {
  let corrections = 0;
  let newContent = content;
  
  // Correction des React.Fragment incorrects
  const fragmentRegex = /<React\.Fragment([^>]*)>([^<]*)<\/(?!React\.Fragment)/g;
  newContent = newContent.replace(fragmentRegex, (match, attrs, content) => {
    corrections++;
    return `<React.Fragment${attrs}>${content}</React.Fragment>`;
  });
  
  // Correction des fragments courts incorrects
  const shortFragmentRegex = /<>([^<]*)<\/(?!>)/g;
  newContent = newContent.replace(shortFragmentRegex, (match, content) => {
    corrections++;
    return `<>${content}</>`;
  });
  
  return { content: newContent, corrections };
}

function fixMissingExportDefault(content) {
  let corrections = 0;
  let newContent = content;
  
  // Ajout de export default manquant
  const componentRegex = /^const\s+([A-Z][A-Za-z0-9_]*)\s*=/m;
  const hasExportRegex = /export\s+default\s+[A-Za-z0-9_]+/;
  
  if (componentRegex.test(newContent) && !hasExportRegex.test(newContent)) {
    const compMatch = componentRegex.exec(newContent);
    if (compMatch) {
      const componentName = compMatch[1];
      newContent = newContent + `\n\nexport default ${componentName};\n`;
      corrections++;
    }
  }
  
  return { content: newContent, corrections };
}

function fixImportStatements(content) {
  let corrections = 0;
  let newContent = content;
  
  // Correction des import incorrects
  const importRegex = /import\s+([A-Za-z0-9_{}]+)\s+from\s+(['"])(.*)\2;?/g;
  newContent = newContent.replace(importRegex, (match, imports, quote, source) => {
    if (!match.endsWith(';')) {
      corrections++;
      return `import ${imports} from ${quote}${source}${quote};`;
    }
    return match;
  });
  
  return { content: newContent, corrections };
}

function fixObjectDestructuring(content) {
  let corrections = 0;
  let newContent = content;
  
  // Correction de la déstructuration d'objets
  const destructuringRegex = /const\s+\{([^}]*)\}\s*=\s*([A-Za-z0-9_]+)(?!;)/g;
  newContent = newContent.replace(destructuringRegex, (match, props, obj) => {
    corrections++;
    return `const {${props}} = ${obj};`;
  });
  
  return { content: newContent, corrections };
}

// Exécuter la fonction principale
fixReactComponents(); 