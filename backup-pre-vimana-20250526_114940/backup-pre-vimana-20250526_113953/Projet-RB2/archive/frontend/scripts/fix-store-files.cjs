const fs = require('fs');
const path = require('path');

// Fonction pour corriger le contenu d'un fichier
function processFile(filePath) {
  console.log(`Processing file: ${filePath}`);
  const content = fs.readFileSync(filePath, 'utf8');
  let updatedContent = content;
  let corrections = 0;

  // Correction spécifique pour validationMiddleware.ts
  if (filePath.includes('validationMiddleware.ts')) {
    // Corriger les erreurs de syntaxe et les accolades manquantes
    updatedContent = content
      .replace(/\s*\}\);?\s*\}\);?/g, (match) => {
        corrections++;
        return '\n    });\n  });\n};';
      })
      .replace(/\s*\}\);?\s*\};?/g, (match) => {
        corrections++;
        return '\n  });\n};';
      });
  }

  // Correction spécifique pour messageSlice.ts
  if (filePath.includes('messageSlice.ts')) {
    // Corriger les erreurs de syntaxe dans messageSlice.ts
    updatedContent = content
      .replace(/\s*\},\s*\},;/g, (match) => {
        corrections++;
        return '\n    }\n  }';
      })
      .replace(/\s*\};?\s*\}\);?/g, (match) => {
        corrections++;
        return '\n  }\n});';
      });
  }

  // Correction spécifique pour notificationMiddleware.ts
  if (filePath.includes('notificationMiddleware.ts')) {
    // Corriger les erreurs de syntaxe et les accolades manquantes
    updatedContent = content
      .replace(/\s*\}\);?\s*\}\);?/g, (match) => {
        corrections++;
        return '\n    });\n  });\n};';
      })
      .replace(/\s*\}\);?\s*\};?/g, (match) => {
        corrections++;
        return '\n  });\n};';
      });
  }

  // Enregistrer les modifications si des corrections ont été faites
  if (updatedContent !== content) {
    fs.writeFileSync(filePath, updatedContent);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
    return corrections;
  } else {
    console.log(`No changes made to ${filePath}`);
    return 0;
  }
}

// Fonction principale
function fixStoreFiles() {
  const rootDir = process.cwd();
  const storeDir = path.join(rootDir, 'src', 'store');
  
  if (!fs.existsSync(storeDir)) {
    console.error(`Store directory not found: ${storeDir}`);
    return;
  }

  const files = [
    path.join(storeDir, 'middleware', 'validationMiddleware.ts'),
    path.join(storeDir, 'middleware', 'notificationMiddleware.ts'),
    path.join(storeDir, 'slices', 'messageSlice.ts')
  ];

  let totalCorrections = 0;
  let fixedFiles = 0;

  files.forEach(file => {
    if (fs.existsSync(file)) {
      const corrections = processFile(file);
      if (corrections > 0) {
        totalCorrections += corrections;
        fixedFiles++;
      }
    } else {
      console.error(`File not found: ${file}`);
    }
  });

  console.log(`Total: Fixed ${totalCorrections} issues in ${fixedFiles} files`);
}

// Exécuter la fonction principale
fixStoreFiles(); 