/**
 * Script pour déployer l'application dans l'environnement de production
 */

import { spawn } from 'child_process';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le chemin du répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Fonction pour exécuter une commande
function runCommand(command, args, cwd = rootDir) {
  return new Promise((resolve, reject) => {
    console.log(chalk.blue(`Exécution de la commande: ${command} ${args.join(' ')}`));
    
    const process = spawn(command, args, { stdio: 'inherit', cwd });
    
    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`La commande a échoué avec le code ${code}`));
      }
    });
    
    process.on('error', (error) => {
      reject(error);
    });
  });
}

// Fonction pour créer un fichier .env.production
function createProductionEnvFile() {
  const envContent = `
# Variables d'environnement pour l'environnement de production

# API URLs
VITE_API_URL=https://api.retreat-and-be.com
VITE_SECURITY_API_URL=https://security-api.retreat-and-be.com
VITE_FINANCIAL_API_URL=https://financial-api.retreat-and-be.com
VITE_SOCIAL_API_URL=https://social-api.retreat-and-be.com
VITE_EDUCATION_API_URL=https://education-api.retreat-and-be.com
VITE_AGENT_API_URL=https://agent-api.retreat-and-be.com

# Authentication
VITE_AUTH_DOMAIN=auth.retreat-and-be.com
VITE_AUTH_CLIENT_ID=production-client-id
VITE_AUTH_AUDIENCE=https://api.retreat-and-be.com

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_CHAT=true
VITE_ENABLE_NOTIFICATIONS=true

# Misc
VITE_APP_VERSION=${new Date().toISOString()}
VITE_ENVIRONMENT=production
`;

  fs.writeFileSync(path.join(rootDir, '.env.production'), envContent);
  console.log(chalk.green('Fichier .env.production créé avec succès.'));
}

// Fonction pour créer un fichier de configuration Vite pour la production
function createProductionViteConfig() {
  const configContent = `
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          ui: ['@mui/material', '@mui/icons-material'],
        },
      },
    },
  },
  server: {
    port: 5173,
    strictPort: true,
  },
});
`;

  fs.writeFileSync(path.join(rootDir, 'vite.production.config.js'), configContent);
  console.log(chalk.green('Fichier vite.production.config.js créé avec succès.'));
}

// Fonction pour mettre à jour le package.json avec les scripts de production
async function updatePackageJson() {
  const packageJsonPath = path.join(rootDir, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Ajouter les scripts de production s'ils n'existent pas déjà
  if (!packageJson.scripts['build:prod']) {
    packageJson.scripts['build:prod'] = 'vite build --config vite.production.config.js --mode production';
  }
  
  if (!packageJson.scripts['preview:prod']) {
    packageJson.scripts['preview:prod'] = 'vite preview --config vite.production.config.js --port 5173';
  }
  
  if (!packageJson.scripts['deploy:prod']) {
    packageJson.scripts['deploy:prod'] = 'node scripts/deploy-production.js';
  }
  
  // Écrire le package.json mis à jour
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log(chalk.green('package.json mis à jour avec succès.'));
}

// Fonction pour créer une sauvegarde avant le déploiement
function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(rootDir, 'backups', timestamp);
  
  // Créer le répertoire de sauvegarde
  fs.mkdirSync(backupDir, { recursive: true });
  
  // Copier les fichiers importants
  fs.copyFileSync(path.join(rootDir, 'package.json'), path.join(backupDir, 'package.json'));
  
  // Copier le répertoire dist s'il existe
  if (fs.existsSync(path.join(rootDir, 'dist'))) {
    fs.mkdirSync(path.join(backupDir, 'dist'), { recursive: true });
    fs.cpSync(path.join(rootDir, 'dist'), path.join(backupDir, 'dist'), { recursive: true });
  }
  
  console.log(chalk.green(`Sauvegarde créée avec succès dans le répertoire: ${backupDir}`));
}

// Fonction principale
async function main() {
  try {
    console.log(chalk.green('=== Déploiement de l\'application dans l\'environnement de production ==='));
    
    // Créer une sauvegarde
    console.log(chalk.yellow('\n=== Création d\'une sauvegarde ==='));
    createBackup();
    
    // Créer les fichiers de configuration
    console.log(chalk.yellow('\n=== Création des fichiers de configuration ==='));
    createProductionEnvFile();
    createProductionViteConfig();
    await updatePackageJson();
    
    // Installer les dépendances
    console.log(chalk.yellow('\n=== Installation des dépendances ==='));
    await runCommand('npm', ['install', '--production']);
    
    // Linter
    console.log(chalk.yellow('\n=== Vérification du code ==='));
    await runCommand('npm', ['run', 'lint']);
    
    // Tests
    console.log(chalk.yellow('\n=== Exécution des tests ==='));
    await runCommand('npm', ['run', 'test:audrey']);
    
    // Build
    console.log(chalk.yellow('\n=== Build de l\'application ==='));
    await runCommand('npm', ['run', 'build:prod']);
    
    // Déploiement sur AWS S3
    console.log(chalk.yellow('\n=== Déploiement sur AWS S3 ==='));
    console.log(chalk.blue('Exécution de la commande: aws s3 sync dist/ s3://retreat-and-be-prod --delete'));
    
    // Décommenter la ligne suivante pour effectuer le déploiement réel
    // await runCommand('aws', ['s3', 'sync', 'dist/', 's3://retreat-and-be-prod', '--delete']);
    
    console.log(chalk.green('Déploiement sur AWS S3 simulé avec succès.'));
    
    // Invalidation du cache CloudFront
    console.log(chalk.yellow('\n=== Invalidation du cache CloudFront ==='));
    console.log(chalk.blue('Exécution de la commande: aws cloudfront create-invalidation --distribution-id DISTRIBUTION_ID_PROD --paths "/*"'));
    
    // Décommenter la ligne suivante pour effectuer l'invalidation réelle
    // await runCommand('aws', ['cloudfront', 'create-invalidation', '--distribution-id', 'DISTRIBUTION_ID_PROD', '--paths', '/*']);
    
    console.log(chalk.green('Invalidation du cache CloudFront simulée avec succès.'));
    
    console.log(chalk.green('\n=== Déploiement terminé avec succès ==='));
    console.log(chalk.blue('L\'application est maintenant disponible à l\'adresse: https://retreat-and-be.com'));
  } catch (error) {
    console.error(chalk.red(`Erreur lors du déploiement: ${error.message}`));
    process.exit(1);
  }
}

// Exécuter la fonction principale
main();
