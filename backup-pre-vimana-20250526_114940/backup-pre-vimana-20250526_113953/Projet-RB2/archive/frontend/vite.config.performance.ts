import * as vite from 'vite';
import * as path from 'path';

export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Split vendor chunks by feature;
          'vendor-core': ['react', 'react-dom', 'react-router-dom'],
          'vendor-ui': ['@mui/material', '@emotion/react', '@emotion/styled'],
          'vendor-utils': ['date-fns', 'lodash'],
          'vendor-state': ['@reduxjs/toolkit', 'zustand']
},
        // Optimize chunk size;
        chunkSizeWarningLimit: 1000,
        // Dynamic imports will be placed in assets directory;
        assetFileNames: 'assets/[name]-[hash][extname]',
        // JS chunks will be placed in js directory;
        chunkFileNames: 'js/[name]-[hash].js',
        // Entry points will be placed in js directory;
        entryFileNames: 'js/[name]-[hash].js'
      }
    },
    // Enable source maps for production;
    sourcemap: true,
    // Optimize dependencies;
    commonjsOptions: {
      include: [/node_modules/],
      extensions: ['.js', '.cjs']
    },
    // Minification options;
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  // Enable build-time optimizations;
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
    exclude: ['@vite/client', '@vite/env']
  },
  // Configure asset handling;
  assetsInclude: ['**/*.{png,jpg,jpeg,gif,svg,webp}'],
  // Enable brotli compression;
  plugins: []
}));