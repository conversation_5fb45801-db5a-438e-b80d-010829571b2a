// Script pour vérifier la configuration du serveur Vite
// Exécuter avec: node server_check.js

import http from 'http';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le répertoire actuel avec ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Vérifier si le dossier public existe et contient un fichier index.html
const publicDir = path.join(__dirname, 'public');
const indexHtmlPath = path.join(publicDir, 'index.html');

console.log('Vérification de la configuration du serveur Vite...\n');

// Vérifier le dossier public
console.log(`1. Vérification du dossier public: ${publicDir}`);
if (fs.existsSync(publicDir)) {
  console.log('✓ Le dossier public existe.');
} else {
  console.log('✗ Le dossier public n\'existe pas. Créez-le avec: mkdir -p public');
  fs.mkdirSync(publicDir, { recursive: true });
  console.log('  Dossier public créé automatiquement.');
}

// Vérifier le fichier index.html
console.log(`\n2. Vérification du fichier index.html: ${indexHtmlPath}`);
if (fs.existsSync(indexHtmlPath)) {
  console.log('✓ Le fichier index.html existe.');
} else {
  console.log('✗ Le fichier index.html n\'existe pas. Créez-le avec le contenu approprié.');
  const indexHtmlContent = `<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Mon Application</title>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>
</html>`;
  
  fs.writeFileSync(indexHtmlPath, indexHtmlContent);
  console.log('  Fichier index.html créé automatiquement.');
}

// Vérifier le fichier _redirects
const redirectsPath = path.join(publicDir, '_redirects');
console.log(`\n3. Vérification du fichier _redirects: ${redirectsPath}`);
if (fs.existsSync(redirectsPath)) {
  console.log('✓ Le fichier _redirects existe.');
  const content = fs.readFileSync(redirectsPath, 'utf8');
  if (content.includes('/* /index.html 200')) {
    console.log('✓ Le fichier _redirects contient la règle pour SPA routing.');
  } else {
    console.log('✗ Le fichier _redirects ne contient pas la règle pour SPA routing.');
    fs.writeFileSync(redirectsPath, '/* /index.html 200\n');
    console.log('  Règle de SPA routing ajoutée automatiquement.');
  }
} else {
  console.log('✗ Le fichier _redirects n\'existe pas.');
  fs.writeFileSync(redirectsPath, '/* /index.html 200\n');
  console.log('  Fichier _redirects créé automatiquement.');
}

// Vérifier le fichier vite.config.ts
const viteConfigPath = path.join(__dirname, 'vite.config.ts');
console.log(`\n4. Vérification du fichier vite.config.ts: ${viteConfigPath}`);
if (fs.existsSync(viteConfigPath)) {
  console.log('✓ Le fichier vite.config.ts existe.');
  // Pas de modification automatique car trop complexe pour être fait avec un script simple
} else {
  console.log('✗ Le fichier vite.config.ts n\'existe pas.');
}

// Vérifier si le serveur est en cours d'exécution
console.log('\n5. Vérification du serveur de développement Vite:');
const testUrl = 'http://localhost:3000/test';

http.get(testUrl, (res) => {
  console.log(`  Statut de ${testUrl}: ${res.statusCode}`);
  if (res.statusCode === 200) {
    console.log('✓ Le serveur répond correctement à la route /test.');
  } else if (res.statusCode === 404) {
    console.log('✗ Erreur 404 - La route /test n\'a pas été trouvée.');
    console.log('  > Suggestion: Redémarrez le serveur après avoir appliqué les modifications.');
  } else {
    console.log(`✗ Le serveur a répondu avec un code ${res.statusCode}.`);
  }
}).on('error', (err) => {
  console.log('✗ Impossible de se connecter au serveur de développement Vite.');
  console.log(`  > Erreur: ${err.message}`);
  console.log('  > Assurez-vous que le serveur est en cours d\'exécution avec: npm run dev');
});

console.log('\n6. Étapes suivantes recommandées:');
console.log('  - Arrêtez le serveur de développement actuel (Ctrl+C)');
console.log('  - Démarrez-le à nouveau avec: npm run dev');
console.log('  - Ouvrez http://localhost:3000/test dans votre navigateur');
console.log('  - Si le problème persiste, essayez de créer une build et de l\'apercevoir:');
console.log('    npm run build');
console.log('    npm run preview'); 