#!/bin/bash

# =============================================================================
# SPRINT 10 - COMPREHENSIVE SECURITY AUDIT SCRIPT
# =============================================================================
# This script performs a comprehensive security assessment of the entire
# distributed nervous system as part of Sprint 10 finalization.
# Based on: 01_AI-RUN/07b_Security_Assessment.md
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
AUDIT_REPORT_DIR="$PROJECT_ROOT/security-audit-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
AUDIT_REPORT="$AUDIT_REPORT_DIR/security_audit_$TIMESTAMP.md"

# Create audit report directory
mkdir -p "$AUDIT_REPORT_DIR"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$AUDIT_REPORT"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$AUDIT_REPORT"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$AUDIT_REPORT"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$AUDIT_REPORT"
}

# Initialize audit report
init_audit_report() {
    cat > "$AUDIT_REPORT" << EOF
# Security Audit Report - Sprint 10
**Generated:** $(date)
**System:** Retreat And Be - Distributed Nervous System
**Audit Scope:** Comprehensive security assessment for production readiness

## Executive Summary
This report contains the results of a comprehensive security audit conducted as part of Sprint 10 finalization.

## Audit Methodology
Based on OWASP guidelines and 01_AI-RUN/07b_Security_Assessment.md:
- Static Application Security Testing (SAST)
- Dynamic Application Security Testing (DAST)
- Infrastructure Security Assessment
- Configuration Security Review
- Dependency Vulnerability Scanning
- Compliance Verification

---

EOF
}

# 1. STATIC APPLICATION SECURITY TESTING (SAST)
perform_sast() {
    log "🔍 Starting Static Application Security Testing (SAST)..."
    
    echo "## 1. Static Application Security Testing (SAST)" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    # Check if security tools are available
    if command -v npm &> /dev/null; then
        log "Running npm audit for dependency vulnerabilities..."
        echo "### NPM Audit Results" >> "$AUDIT_REPORT"
        echo "\`\`\`" >> "$AUDIT_REPORT"
        npm audit --audit-level=moderate 2>&1 | tee -a "$AUDIT_REPORT" || log_warning "NPM audit found vulnerabilities"
        echo "\`\`\`" >> "$AUDIT_REPORT"
        echo "" >> "$AUDIT_REPORT"
    fi
    
    # ESLint security analysis
    if command -v npx &> /dev/null; then
        log "Running ESLint security analysis..."
        echo "### ESLint Security Analysis" >> "$AUDIT_REPORT"
        echo "\`\`\`" >> "$AUDIT_REPORT"
        find "$PROJECT_ROOT" -name "*.ts" -o -name "*.js" | head -20 | while read -r file; do
            echo "Analyzing: $file" >> "$AUDIT_REPORT"
            npx eslint "$file" --config .eslintrc.js 2>&1 | grep -i "security\|vulnerability\|unsafe" || echo "No security issues found" >> "$AUDIT_REPORT"
        done
        echo "\`\`\`" >> "$AUDIT_REPORT"
        echo "" >> "$AUDIT_REPORT"
    fi
    
    # Check for hardcoded secrets
    log "Scanning for hardcoded secrets and sensitive data..."
    echo "### Secret Scanning Results" >> "$AUDIT_REPORT"
    echo "\`\`\`" >> "$AUDIT_REPORT"
    
    # Common patterns for secrets
    SECRET_PATTERNS=(
        "password\s*=\s*['\"][^'\"]*['\"]"
        "api[_-]?key\s*=\s*['\"][^'\"]*['\"]"
        "secret\s*=\s*['\"][^'\"]*['\"]"
        "token\s*=\s*['\"][^'\"]*['\"]"
        "private[_-]?key"
        "BEGIN\s+(RSA\s+)?PRIVATE\s+KEY"
    )
    
    for pattern in "${SECRET_PATTERNS[@]}"; do
        echo "Checking pattern: $pattern" >> "$AUDIT_REPORT"
        grep -r -i -E "$pattern" "$PROJECT_ROOT" --include="*.ts" --include="*.js" --include="*.json" --exclude-dir=node_modules --exclude-dir=.git 2>/dev/null | head -5 >> "$AUDIT_REPORT" || echo "No matches found" >> "$AUDIT_REPORT"
    done
    
    echo "\`\`\`" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    log_success "SAST analysis completed"
}

# 2. INFRASTRUCTURE SECURITY ASSESSMENT
assess_infrastructure_security() {
    log "🏗️ Assessing Infrastructure Security..."
    
    echo "## 2. Infrastructure Security Assessment" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    # Docker security assessment
    log "Checking Docker security configurations..."
    echo "### Docker Security Configuration" >> "$AUDIT_REPORT"
    echo "\`\`\`" >> "$AUDIT_REPORT"
    
    # Check for Docker daemon security
    if command -v docker &> /dev/null; then
        echo "Docker version:" >> "$AUDIT_REPORT"
        docker --version >> "$AUDIT_REPORT"
        
        echo "Docker security options:" >> "$AUDIT_REPORT"
        docker info --format '{{.SecurityOptions}}' 2>/dev/null >> "$AUDIT_REPORT" || echo "Unable to retrieve Docker security options" >> "$AUDIT_REPORT"
        
        # Check for privileged containers
        echo "Checking for privileged containers:" >> "$AUDIT_REPORT"
        docker ps --format "table {{.Names}}\t{{.Status}}" 2>/dev/null >> "$AUDIT_REPORT" || echo "No running containers found" >> "$AUDIT_REPORT"
    fi
    
    echo "\`\`\`" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    # Kubernetes security assessment
    if command -v kubectl &> /dev/null; then
        log "Checking Kubernetes security configurations..."
        echo "### Kubernetes Security Configuration" >> "$AUDIT_REPORT"
        echo "\`\`\`" >> "$AUDIT_REPORT"
        
        echo "Kubernetes cluster info:" >> "$AUDIT_REPORT"
        kubectl cluster-info 2>/dev/null >> "$AUDIT_REPORT" || echo "Kubernetes cluster not accessible" >> "$AUDIT_REPORT"
        
        echo "Security policies:" >> "$AUDIT_REPORT"
        kubectl get networkpolicies --all-namespaces 2>/dev/null >> "$AUDIT_REPORT" || echo "No network policies found" >> "$AUDIT_REPORT"
        
        echo "\`\`\`" >> "$AUDIT_REPORT"
        echo "" >> "$AUDIT_REPORT"
    fi
    
    log_success "Infrastructure security assessment completed"
}

# 3. CONFIGURATION SECURITY REVIEW
review_configuration_security() {
    log "⚙️ Reviewing Configuration Security..."
    
    echo "## 3. Configuration Security Review" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    # Check environment files
    echo "### Environment Configuration Security" >> "$AUDIT_REPORT"
    echo "\`\`\`" >> "$AUDIT_REPORT"
    
    # Find and analyze .env files
    find "$PROJECT_ROOT" -name ".env*" -type f | while read -r env_file; do
        echo "Analyzing: $env_file" >> "$AUDIT_REPORT"
        if [[ -f "$env_file" ]]; then
            # Check for insecure configurations
            grep -i "debug\|test\|development" "$env_file" 2>/dev/null >> "$AUDIT_REPORT" || echo "No debug/test configurations found" >> "$AUDIT_REPORT"
        fi
    done
    
    echo "\`\`\`" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    # Check Docker Compose security
    echo "### Docker Compose Security" >> "$AUDIT_REPORT"
    echo "\`\`\`" >> "$AUDIT_REPORT"
    
    if [[ -f "$PROJECT_ROOT/docker-compose.v3.8.yml" ]]; then
        echo "Analyzing Docker Compose configuration..." >> "$AUDIT_REPORT"
        # Check for security issues in docker-compose
        grep -i "privileged\|cap_add\|security_opt" "$PROJECT_ROOT/docker-compose.v3.8.yml" 2>/dev/null >> "$AUDIT_REPORT" || echo "No privileged configurations found" >> "$AUDIT_REPORT"
    fi
    
    echo "\`\`\`" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    log_success "Configuration security review completed"
}

# 4. AGENT SECURITY VALIDATION
validate_agent_security() {
    log "🤖 Validating Agent Security..."
    
    echo "## 4. Agent Security Validation" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    # Check if Agent Security is running
    echo "### Agent Security Status" >> "$AUDIT_REPORT"
    echo "\`\`\`" >> "$AUDIT_REPORT"
    
    # Test Agent Security endpoint if available
    if curl -s -f "http://localhost:3012/health" &>/dev/null; then
        echo "Agent Security is running and accessible" >> "$AUDIT_REPORT"
        
        # Get security metrics
        curl -s "http://localhost:3012/api/security/metrics" 2>/dev/null >> "$AUDIT_REPORT" || echo "Unable to retrieve security metrics" >> "$AUDIT_REPORT"
    else
        echo "Agent Security is not accessible at http://localhost:3012" >> "$AUDIT_REPORT"
    fi
    
    echo "\`\`\`" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    log_success "Agent security validation completed"
}

# 5. COMPLIANCE VERIFICATION
verify_compliance() {
    log "📋 Verifying Compliance Standards..."
    
    echo "## 5. Compliance Verification" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    # Check if Agent Compliance is running
    echo "### Compliance Status" >> "$AUDIT_REPORT"
    echo "\`\`\`" >> "$AUDIT_REPORT"
    
    if curl -s -f "http://localhost:3015/health" &>/dev/null; then
        echo "Agent Compliance is running and accessible" >> "$AUDIT_REPORT"
        
        # Get compliance status
        curl -s "http://localhost:3015/api/compliance/status" 2>/dev/null >> "$AUDIT_REPORT" || echo "Unable to retrieve compliance status" >> "$AUDIT_REPORT"
    else
        echo "Agent Compliance is not accessible at http://localhost:3015" >> "$AUDIT_REPORT"
    fi
    
    echo "\`\`\`" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    log_success "Compliance verification completed"
}

# 6. GENERATE SECURITY RECOMMENDATIONS
generate_recommendations() {
    log "📝 Generating Security Recommendations..."
    
    echo "## 6. Security Recommendations" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
    
    cat >> "$AUDIT_REPORT" << EOF
### High Priority Recommendations
1. **Dependency Updates**: Ensure all npm dependencies are updated to latest secure versions
2. **Secret Management**: Implement proper secret management for production deployment
3. **Network Security**: Configure proper network policies for Kubernetes deployment
4. **Monitoring**: Ensure security monitoring and alerting are properly configured

### Medium Priority Recommendations
1. **Code Review**: Conduct thorough code review focusing on security aspects
2. **Access Control**: Implement proper RBAC for all system components
3. **Logging**: Ensure comprehensive security logging is in place
4. **Backup**: Verify backup and recovery procedures include security considerations

### Low Priority Recommendations
1. **Documentation**: Update security documentation with latest configurations
2. **Training**: Provide security training for operations team
3. **Penetration Testing**: Consider external penetration testing for production

EOF
    
    log_success "Security recommendations generated"
}

# Main execution
main() {
    log "🚀 Starting Sprint 10 Comprehensive Security Audit..."
    log "Project Root: $PROJECT_ROOT"
    log "Audit Report: $AUDIT_REPORT"
    
    init_audit_report
    
    perform_sast
    assess_infrastructure_security
    review_configuration_security
    validate_agent_security
    verify_compliance
    generate_recommendations
    
    echo "## Audit Completion" >> "$AUDIT_REPORT"
    echo "**Completed:** $(date)" >> "$AUDIT_REPORT"
    echo "**Status:** Security audit completed successfully" >> "$AUDIT_REPORT"
    
    log_success "🎉 Security audit completed successfully!"
    log "📄 Full report available at: $AUDIT_REPORT"
    
    # Display summary
    echo ""
    echo "=== SECURITY AUDIT SUMMARY ==="
    echo "Report Location: $AUDIT_REPORT"
    echo "Timestamp: $TIMESTAMP"
    echo "Status: COMPLETED"
    echo "=============================="
}

# Execute main function
main "$@"
