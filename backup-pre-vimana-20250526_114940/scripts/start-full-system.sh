#!/bin/bash

# Script de démarrage du système complet d'agents
# Démarre tous les agents et l'infrastructure nécessaire

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

log_info() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"
}

# Configuration
DOCKER_COMPOSE_FILE="docker-compose.v3.8.yml"
AGENTS_DIR="agents"
CORTEX_DIR="cortex-central"
BACKEND_DIR="Projet-RB2/Backend-NestJS"

# Vérifier les prérequis
check_prerequisites() {
    log "🔍 Vérification des prérequis..."
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé"
        exit 1
    fi
    
    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        log_error "npm n'est pas installé"
        exit 1
    fi
    
    log_success "Tous les prérequis sont satisfaits"
}

# Démarrer l'infrastructure
start_infrastructure() {
    log "🏗️  Démarrage de l'infrastructure..."
    
    if [ -f "$DOCKER_COMPOSE_FILE" ]; then
        log_info "Démarrage des services Docker..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d kafka zookeeper weaviate redis prometheus grafana
        
        # Attendre que les services soient prêts
        log_info "Attente de la disponibilité des services..."
        sleep 30
        
        # Vérifier Kafka
        log_info "Vérification de Kafka..."
        timeout 60 bash -c 'until docker-compose -f '"$DOCKER_COMPOSE_FILE"' exec kafka kafka-topics --bootstrap-server localhost:9092 --list &>/dev/null; do sleep 2; done'
        
        # Vérifier Weaviate
        log_info "Vérification de Weaviate..."
        timeout 60 bash -c 'until curl -f http://localhost:8080/v1/meta &>/dev/null; do sleep 2; done'
        
        # Vérifier Redis
        log_info "Vérification de Redis..."
        timeout 60 bash -c 'until docker-compose -f '"$DOCKER_COMPOSE_FILE"' exec redis redis-cli ping &>/dev/null; do sleep 2; done'
        
        log_success "Infrastructure démarrée avec succès"
    else
        log_error "Fichier docker-compose non trouvé: $DOCKER_COMPOSE_FILE"
        exit 1
    fi
}

# Démarrer le Cortex Central
start_cortex_central() {
    log "🧠 Démarrage du Cortex Central..."
    
    if [ -d "$CORTEX_DIR" ]; then
        cd "$CORTEX_DIR"
        
        # Installer les dépendances si nécessaire
        if [ ! -d "node_modules" ]; then
            log_info "Installation des dépendances du Cortex Central..."
            npm install
        fi
        
        # Build si nécessaire
        if [ ! -d "dist" ]; then
            log_info "Build du Cortex Central..."
            npm run build
        fi
        
        # Démarrer en arrière-plan
        log_info "Démarrage du Cortex Central..."
        nohup npm start > ../logs/cortex-central.log 2>&1 &
        echo $! > ../cortex-central.pid
        
        cd ..
        
        # Attendre que le service soit prêt
        log_info "Attente de la disponibilité du Cortex Central..."
        timeout 60 bash -c 'until curl -f http://localhost:3001/health &>/dev/null; do sleep 2; done'
        
        log_success "Cortex Central démarré"
    else
        log_warning "Répertoire Cortex Central non trouvé: $CORTEX_DIR"
    fi
}

# Démarrer les agents
start_agents() {
    log "🤖 Démarrage des agents..."
    
    # Liste des agents à démarrer
    agents=("performance" "qa" "security" "devops" "uiux" "frontend")
    
    for agent in "${agents[@]}"; do
        agent_dir="$AGENTS_DIR/$agent"
        
        if [ -d "$agent_dir" ]; then
            log_info "Démarrage de l'Agent $agent..."
            
            cd "$agent_dir"
            
            # Installer les dépendances si nécessaire
            if [ ! -d "node_modules" ]; then
                log_info "Installation des dépendances de l'Agent $agent..."
                npm install
            fi
            
            # Build si nécessaire
            if [ ! -d "dist" ]; then
                log_info "Build de l'Agent $agent..."
                npm run build
            fi
            
            # Démarrer en arrière-plan
            nohup npm start > "../../logs/agent-$agent.log" 2>&1 &
            echo $! > "../../agent-$agent.pid"
            
            cd ../..
            
            # Attendre un peu entre les démarrages
            sleep 5
            
            log_success "Agent $agent démarré"
        else
            log_warning "Agent $agent non trouvé dans $agent_dir"
        fi
    done
}

# Démarrer le backend NestJS
start_backend() {
    log "🔧 Démarrage du Backend NestJS..."
    
    if [ -d "$BACKEND_DIR" ]; then
        cd "$BACKEND_DIR"
        
        # Installer les dépendances si nécessaire
        if [ ! -d "node_modules" ]; then
            log_info "Installation des dépendances du Backend..."
            npm install
        fi
        
        # Build si nécessaire
        if [ ! -d "dist" ]; then
            log_info "Build du Backend..."
            npm run build
        fi
        
        # Démarrer en arrière-plan
        log_info "Démarrage du Backend NestJS..."
        nohup npm run start:prod > ../../logs/backend-nestjs.log 2>&1 &
        echo $! > ../../backend-nestjs.pid
        
        cd ../..
        
        # Attendre que le service soit prêt
        log_info "Attente de la disponibilité du Backend..."
        timeout 60 bash -c 'until curl -f http://localhost:3000/health &>/dev/null; do sleep 2; done'
        
        log_success "Backend NestJS démarré"
    else
        log_warning "Répertoire Backend non trouvé: $BACKEND_DIR"
    fi
}

# Vérifier la santé du système
check_system_health() {
    log "🏥 Vérification de la santé du système..."
    
    # Services à vérifier
    services=(
        "http://localhost:3001/health:Cortex Central"
        "http://localhost:3000/health:Backend NestJS"
        "http://localhost:3007/health:Agent Performance"
        "http://localhost:3008/health:Agent QA"
        "http://localhost:3009/health:Agent Security"
        "http://localhost:3010/health:Agent DevOps"
        "http://localhost:3011/health:Agent UI/UX"
        "http://localhost:3012/health:Agent Frontend"
    )
    
    healthy_count=0
    total_count=${#services[@]}
    
    for service in "${services[@]}"; do
        url=$(echo "$service" | cut -d: -f1-2)
        name=$(echo "$service" | cut -d: -f3)
        
        if curl -f "$url" &>/dev/null; then
            log_success "$name est en ligne"
            ((healthy_count++))
        else
            log_error "$name n'est pas accessible"
        fi
    done
    
    log_info "Services en ligne: $healthy_count/$total_count"
    
    if [ $healthy_count -eq $total_count ]; then
        log_success "Tous les services sont en ligne!"
        return 0
    else
        log_warning "Certains services ne sont pas accessibles"
        return 1
    fi
}

# Exécuter les tests d'intégration
run_integration_tests() {
    log "🧪 Exécution des tests d'intégration..."
    
    if [ -f "scripts/test-agent-integration.js" ]; then
        log_info "Démarrage des tests d'intégration..."
        node scripts/test-agent-integration.js
        
        if [ $? -eq 0 ]; then
            log_success "Tests d'intégration réussis"
        else
            log_error "Tests d'intégration échoués"
            return 1
        fi
    else
        log_warning "Script de test d'intégration non trouvé"
    fi
}

# Afficher les informations du système
display_system_info() {
    log "📊 Informations du système:"
    echo ""
    echo "🌐 URLs des services:"
    echo "  • Cortex Central:     http://localhost:3001"
    echo "  • Backend NestJS:     http://localhost:3000"
    echo "  • Agent Performance:  http://localhost:3007"
    echo "  • Agent QA:           http://localhost:3008"
    echo "  • Agent Security:     http://localhost:3009"
    echo "  • Agent DevOps:       http://localhost:3010"
    echo "  • Agent UI/UX:        http://localhost:3011"
    echo "  • Agent Frontend:     http://localhost:3012"
    echo ""
    echo "📊 Monitoring:"
    echo "  • Prometheus:         http://localhost:9091"
    echo "  • Grafana:            http://localhost:3000 (admin/admin)"
    echo "  • Weaviate:           http://localhost:8080"
    echo ""
    echo "📝 Logs:"
    echo "  • Répertoire logs:    ./logs/"
    echo "  • PIDs:               ./*.pid"
    echo ""
    echo "🛑 Pour arrêter le système:"
    echo "  ./scripts/stop-full-system.sh"
}

# Créer les répertoires nécessaires
create_directories() {
    log "📁 Création des répertoires nécessaires..."
    
    directories=("logs" "pids" "data" "reports")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "Répertoire créé: $dir"
        fi
    done
}

# Fonction principale
main() {
    echo ""
    echo "🚀 Démarrage du système complet d'agents"
    echo "========================================"
    echo ""
    
    # Créer les répertoires
    create_directories
    
    # Vérifier les prérequis
    check_prerequisites
    
    # Démarrer l'infrastructure
    start_infrastructure
    
    # Démarrer le Cortex Central
    start_cortex_central
    
    # Démarrer le backend
    start_backend
    
    # Démarrer les agents
    start_agents
    
    # Attendre un peu pour que tout se stabilise
    log_info "Attente de la stabilisation du système..."
    sleep 10
    
    # Vérifier la santé du système
    if check_system_health; then
        log_success "Système démarré avec succès!"
        
        # Exécuter les tests d'intégration
        if [ "$1" = "--test" ]; then
            run_integration_tests
        fi
        
        # Afficher les informations
        display_system_info
        
        echo ""
        log_success "🎉 Le système d'agents est prêt!"
        
    else
        log_error "Problèmes détectés lors du démarrage"
        exit 1
    fi
}

# Gestion des signaux pour un arrêt propre
trap 'log_warning "Signal reçu, arrêt en cours..."; ./scripts/stop-full-system.sh; exit 0' SIGINT SIGTERM

# Exécuter la fonction principale
main "$@"
