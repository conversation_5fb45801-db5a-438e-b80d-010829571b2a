#!/bin/bash

# =============================================================================
# Script de déploiement production automatisé
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/deployment.log"
BACKUP_DIR="$PROJECT_ROOT/backups"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Variables de déploiement
ENVIRONMENT=${1:-production}
VERSION=${2:-latest}
ROLLBACK_VERSION=""
DEPLOYMENT_ID="deploy_${TIMESTAMP}"

# Fonction de logging
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} $message"
            ;;
        "DEPLOY")
            echo -e "${PURPLE}[DEPLOY]${NC} $message"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Fonction de vérification des prérequis
check_prerequisites() {
    log "INFO" "Vérification des prérequis..."
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        log "ERROR" "Docker n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log "ERROR" "Docker Compose n'est pas installé"
        exit 1
    fi
    
    # Vérifier les variables d'environnement
    if [[ -z "${DOCKER_REGISTRY:-}" ]]; then
        log "WARN" "DOCKER_REGISTRY non défini, utilisation du registry local"
        export DOCKER_REGISTRY="localhost:5000"
    fi
    
    # Vérifier l'espace disque
    local available_space=$(df / | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 5000000 ]]; then # 5GB
        log "ERROR" "Espace disque insuffisant (< 5GB disponible)"
        exit 1
    fi
    
    log "INFO" "Prérequis vérifiés avec succès"
}

# Fonction de sauvegarde
create_backup() {
    log "INFO" "Création de la sauvegarde..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Sauvegarder la configuration actuelle
    local backup_file="$BACKUP_DIR/backup_${TIMESTAMP}.tar.gz"
    
    # Sauvegarder les volumes Docker
    docker run --rm \
        -v "$(pwd)":/backup \
        -v weaviate_data:/data/weaviate \
        -v kafka_data:/data/kafka \
        -v redis_data:/data/redis \
        alpine:latest \
        tar czf "/backup/$backup_file" \
        /data 2>/dev/null || true
    
    # Sauvegarder les configurations
    tar czf "${backup_file%.tar.gz}_config.tar.gz" \
        docker-compose.yml \
        docker-compose.prod.yml \
        .env.production \
        agents/*/package.json 2>/dev/null || true
    
    log "INFO" "Sauvegarde créée: $backup_file"
    export BACKUP_FILE="$backup_file"
}

# Fonction de construction des images
build_images() {
    log "INFO" "Construction des images Docker..."
    
    local agents=(
        "cortex-central"
        "frontend"
        "backend"
        "devops"
        "qa"
        "security"
        "uiux"
        "web-research"
        "data-analyst"
        "project-manager"
        "documentation"
        "migration"
        "compliance"
        "evolution"
    )
    
    for agent in "${agents[@]}"; do
        log "DEPLOY" "Construction de l'image: $agent"
        
        if [[ -d "agents/$agent" ]]; then
            docker build \
                -t "${DOCKER_REGISTRY}/${agent}:${VERSION}" \
                -t "${DOCKER_REGISTRY}/${agent}:latest" \
                "agents/$agent" || {
                log "ERROR" "Échec de la construction de $agent"
                return 1
            }
            
            # Pousser vers le registry
            docker push "${DOCKER_REGISTRY}/${agent}:${VERSION}"
            docker push "${DOCKER_REGISTRY}/${agent}:latest"
            
            log "INFO" "Image $agent construite et poussée avec succès"
        else
            log "WARN" "Répertoire agents/$agent non trouvé"
        fi
    done
    
    log "INFO" "Construction des images terminée"
}

# Fonction de test de santé
health_check() {
    log "INFO" "Vérification de la santé du système..."
    
    local max_attempts=30
    local attempt=1
    
    local services=(
        "cortex-central:3000"
        "frontend:3001"
        "backend:3002"
        "devops:3003"
        "qa:3004"
        "security:3005"
        "uiux:3006"
        "web-research:3007"
        "data-analyst:3008"
        "project-manager:3009"
        "evolution:3010"
    )
    
    while [[ $attempt -le $max_attempts ]]; do
        log "DEBUG" "Tentative de health check $attempt/$max_attempts"
        
        local all_healthy=true
        
        for service in "${services[@]}"; do
            local service_name=$(echo $service | cut -d':' -f1)
            local port=$(echo $service | cut -d':' -f2)
            
            if ! curl -sf "http://localhost:$port/health" >/dev/null 2>&1; then
                log "DEBUG" "Service $service_name pas encore prêt"
                all_healthy=false
                break
            fi
        done
        
        if $all_healthy; then
            log "INFO" "Tous les services sont en bonne santé"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    log "ERROR" "Échec du health check après $max_attempts tentatives"
    return 1
}

# Fonction de déploiement
deploy() {
    log "DEPLOY" "Démarrage du déploiement $DEPLOYMENT_ID"
    
    # Arrêter les services existants
    log "INFO" "Arrêt des services existants..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml down || true
    
    # Nettoyer les images obsolètes
    log "INFO" "Nettoyage des images obsolètes..."
    docker image prune -f || true
    
    # Démarrer les services d'infrastructure
    log "INFO" "Démarrage des services d'infrastructure..."
    docker-compose -f docker-compose.yml up -d \
        kafka \
        weaviate \
        redis \
        postgres
    
    # Attendre que l'infrastructure soit prête
    sleep 30
    
    # Démarrer les agents
    log "INFO" "Démarrage des agents..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    
    # Vérifier la santé
    if health_check; then
        log "DEPLOY" "Déploiement $DEPLOYMENT_ID réussi"
        return 0
    else
        log "ERROR" "Déploiement $DEPLOYMENT_ID échoué"
        return 1
    fi
}

# Fonction de rollback
rollback() {
    log "WARN" "Démarrage du rollback..."
    
    if [[ -z "$BACKUP_FILE" ]]; then
        log "ERROR" "Aucune sauvegarde disponible pour le rollback"
        return 1
    fi
    
    # Arrêter les services
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
    
    # Restaurer depuis la sauvegarde
    log "INFO" "Restauration depuis la sauvegarde..."
    
    # Restaurer les volumes
    docker run --rm \
        -v "$(pwd)":/backup \
        -v weaviate_data:/data/weaviate \
        -v kafka_data:/data/kafka \
        -v redis_data:/data/redis \
        alpine:latest \
        tar xzf "/backup/$(basename "$BACKUP_FILE")" -C / 2>/dev/null || true
    
    # Redémarrer avec la configuration précédente
    docker-compose -f docker-compose.yml up -d
    
    if health_check; then
        log "INFO" "Rollback réussi"
        return 0
    else
        log "ERROR" "Rollback échoué"
        return 1
    fi
}

# Fonction de surveillance post-déploiement
monitor_deployment() {
    log "INFO" "Surveillance post-déploiement..."
    
    local monitoring_duration=300 # 5 minutes
    local start_time=$(date +%s)
    local end_time=$((start_time + monitoring_duration))
    
    while [[ $(date +%s) -lt $end_time ]]; do
        # Vérifier les métriques critiques
        local error_rate=$(check_error_rate)
        local response_time=$(check_response_time)
        local cpu_usage=$(check_cpu_usage)
        
        log "DEBUG" "Métriques: Erreurs=$error_rate%, Réponse=${response_time}ms, CPU=$cpu_usage%"
        
        # Vérifier les seuils critiques
        if (( $(echo "$error_rate > 5.0" | bc -l) )); then
            log "ERROR" "Taux d'erreur critique: $error_rate%"
            return 1
        fi
        
        if (( $(echo "$response_time > 2000" | bc -l) )); then
            log "ERROR" "Temps de réponse critique: ${response_time}ms"
            return 1
        fi
        
        if (( $(echo "$cpu_usage > 90.0" | bc -l) )); then
            log "ERROR" "Usage CPU critique: $cpu_usage%"
            return 1
        fi
        
        sleep 30
    done
    
    log "INFO" "Surveillance post-déploiement terminée avec succès"
    return 0
}

# Fonctions de métriques
check_error_rate() {
    # Simulation - dans un vrai système, on interrogerait les logs
    echo "$(echo "scale=2; $(shuf -i 0-100 -n 1) / 100" | bc)"
}

check_response_time() {
    # Simulation - dans un vrai système, on mesurerait les vraies réponses
    echo "$(shuf -i 100-500 -n 1)"
}

check_cpu_usage() {
    # Utilisation CPU réelle
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
}

# Fonction de notification
send_notification() {
    local status=$1
    local message=$2
    
    log "INFO" "Envoi de notification: $status - $message"
    
    # Ici, on pourrait envoyer des notifications Slack, email, etc.
    # curl -X POST -H 'Content-type: application/json' \
    #     --data "{\"text\":\"Déploiement $DEPLOYMENT_ID: $status - $message\"}" \
    #     "$SLACK_WEBHOOK_URL"
}

# Fonction de nettoyage
cleanup() {
    log "INFO" "Nettoyage post-déploiement..."
    
    # Nettoyer les images non utilisées
    docker image prune -f
    
    # Nettoyer les volumes orphelins
    docker volume prune -f
    
    # Nettoyer les anciens logs (garder 30 jours)
    find "$PROJECT_ROOT/logs" -name "*.log" -mtime +30 -delete 2>/dev/null || true
    
    # Nettoyer les anciennes sauvegardes (garder 7 jours)
    find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +7 -delete 2>/dev/null || true
    
    log "INFO" "Nettoyage terminé"
}

# Fonction principale
main() {
    local start_time=$(date +%s)
    
    log "DEPLOY" "Démarrage du déploiement automatisé"
    log "INFO" "Environnement: $ENVIRONMENT"
    log "INFO" "Version: $VERSION"
    log "INFO" "ID de déploiement: $DEPLOYMENT_ID"
    
    # Créer les répertoires nécessaires
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "$BACKUP_DIR"
    
    # Piège pour le nettoyage en cas d'erreur
    trap 'log "ERROR" "Déploiement interrompu"; cleanup; exit 1' ERR
    
    # Étapes du déploiement
    check_prerequisites
    create_backup
    build_images
    
    if deploy; then
        if monitor_deployment; then
            log "DEPLOY" "Déploiement $DEPLOYMENT_ID réussi avec succès"
            send_notification "SUCCESS" "Déploiement terminé avec succès"
        else
            log "ERROR" "Surveillance post-déploiement échouée, rollback..."
            if rollback; then
                send_notification "ROLLBACK" "Rollback effectué avec succès"
            else
                send_notification "CRITICAL" "Rollback échoué - intervention manuelle requise"
                exit 1
            fi
        fi
    else
        log "ERROR" "Déploiement échoué, rollback..."
        if rollback; then
            send_notification "ROLLBACK" "Rollback effectué avec succès"
        else
            send_notification "CRITICAL" "Rollback échoué - intervention manuelle requise"
            exit 1
        fi
    fi
    
    cleanup
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log "DEPLOY" "Déploiement terminé en ${duration} secondes"
}

# Gestion des arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        if [[ -n "${2:-}" ]]; then
            BACKUP_FILE="$2"
            rollback
        else
            log "ERROR" "Usage: $0 rollback <backup_file>"
            exit 1
        fi
        ;;
    "health")
        health_check
        ;;
    "monitor")
        monitor_deployment
        ;;
    *)
        echo "Usage: $0 [deploy|rollback|health|monitor] [version]"
        echo "  deploy [version]     - Déploie la version spécifiée (défaut: latest)"
        echo "  rollback <backup>    - Effectue un rollback vers la sauvegarde"
        echo "  health               - Vérifie la santé du système"
        echo "  monitor              - Lance la surveillance post-déploiement"
        exit 1
        ;;
esac
