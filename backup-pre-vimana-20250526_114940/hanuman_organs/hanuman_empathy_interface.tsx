import React, { useState, useEffect, useRef } from 'react';
import { Heart, Users, MessageCircle, Brain, TrendingUp, Activity, Wifi, WifiOff, Smile, Frown, AlertCircle, CheckCircle, Zap, Target } from 'lucide-react';

// Types pour l'empathie
interface UserEmotionalState {
  userId: string;
  userName: string;
  detectedEmotion: 'joy' | 'sadness' | 'anger' | 'fear' | 'surprise' | 'neutral' | 'frustrated' | 'excited';
  confidence: number; // 0-100
  sentiment: 'positive' | 'negative' | 'neutral';
  intensity: number;
  context: string;
  timestamp: Date;
}

interface EmpathicResponse {
  id: string;
  userEmotion: string;
  responseType: 'supportive' | 'encouraging' | 'calming' | 'celebratory' | 'understanding';
  message: string;
  effectiveness: number;
  usageCount: number;
  lastUsed: Date;
}

interface TherapeuticSession {
  id: string;
  userId: string;
  sessionType: 'emotional_support' | 'stress_relief' | 'motivation' | 'guidance';
  duration: number;
  techniques: string[];
  outcome: 'positive' | 'neutral' | 'needs_followup';
  timestamp: Date;
}

interface EmpathyMetrics {
  emotionDetectionAccuracy: number;
  responseRelevance: number;
  userSatisfaction: number;
  therapeuticEffectiveness: number;
  adaptationSpeed: number;
}

const HanumanEmpathyInterface = ({ darkMode = true }) => {
  const [currentUserStates, setCurrentUserStates] = useState<UserEmotionalState[]>([]);
  const [empathicResponses, setEmpathicResponses] = useState<EmpathicResponse[]>([]);
  const [therapeuticSessions, setTherapeuticSessions] = useState<TherapeuticSession[]>([]);
  const [empathyMetrics, setEmpathyMetrics] = useState<EmpathyMetrics>({
    emotionDetectionAccuracy: 91.7,
    responseRelevance: 88.4,
    userSatisfaction: 93.2,
    therapeuticEffectiveness: 86.9,
    adaptationSpeed: 89.5
  });

  const [isConnected, setIsConnected] = useState(false);
  const [activeUsers, setActiveUsers] = useState(12);
  const [realTimeAnalysis, setRealTimeAnalysis] = useState(true);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Connexion aux agents d'empathie
    connectToEmpathyAgents();

    // Initialiser les données d'empathie
    initializeEmpathyData();

    // Simulation d'analyse en temps réel
    const analysisInterval = setInterval(() => {
      if (realTimeAnalysis) {
        simulateEmotionDetection();
      }
    }, 3000);

    return () => {
      clearInterval(analysisInterval);
      wsRef.current?.close();
    };
  }, [realTimeAnalysis]);

  const connectToEmpathyAgents = () => {
    try {
      wsRef.current = new WebSocket('ws://localhost:3001/empathy');

      wsRef.current.onopen = () => {
        console.log('🔗 Connexion établie avec les agents d\'empathie');
        setIsConnected(true);

        wsRef.current?.send(JSON.stringify({
          type: 'GET_EMPATHY_DATA',
          timestamp: Date.now()
        }));
      };

      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleEmpathyMessage(data);
      };

      wsRef.current.onclose = () => {
        console.log('❌ Connexion fermée avec les agents d\'empathie');
        setIsConnected(false);
        setTimeout(connectToEmpathyAgents, 5000);
      };

    } catch (error) {
      console.error('🚨 Erreur de connexion empathique:', error);
      setIsConnected(false);
    }
  };

  const handleEmpathyMessage = (data: any) => {
    switch (data.type) {
      case 'USER_EMOTION_DETECTED':
        updateUserEmotionalState(data.userState);
        break;

      case 'VIRTUAL_COACH_PROFILE':
        // Connexion avec Virtual Coach pour profils utilisateur
        integrateCoachProfile(data.profile);
        break;

      case 'EMPATHIC_RESPONSE_GENERATED':
        addEmpathicResponse(data.response);
        break;

      case 'THERAPEUTIC_SESSION_STARTED':
        startTherapeuticSession(data.session);
        break;

      case 'METRICS_UPDATE':
        setEmpathyMetrics(data.metrics);
        break;

      default:
        console.log('📨 Message empathique non géré:', data);
    }
  };

  const initializeEmpathyData = () => {
    // États émotionnels utilisateurs simulés
    const userStates: UserEmotionalState[] = [
      {
        userId: 'user_001',
        userName: 'Alice',
        detectedEmotion: 'frustrated',
        confidence: 87,
        sentiment: 'negative',
        intensity: 75,
        context: 'Problème technique',
        timestamp: new Date()
      },
      {
        userId: 'user_002',
        userName: 'Bob',
        detectedEmotion: 'excited',
        confidence: 92,
        sentiment: 'positive',
        intensity: 85,
        context: 'Nouveau projet',
        timestamp: new Date()
      },
      {
        userId: 'user_003',
        userName: 'Claire',
        detectedEmotion: 'sadness',
        confidence: 78,
        sentiment: 'negative',
        intensity: 60,
        context: 'Déception personnelle',
        timestamp: new Date()
      }
    ];

    setCurrentUserStates(userStates);

    // Réponses empathiques prédéfinies
    const responses: EmpathicResponse[] = [
      {
        id: '1',
        userEmotion: 'frustrated',
        responseType: 'calming',
        message: 'Je comprends votre frustration. Prenons le temps de résoudre cela ensemble, étape par étape.',
        effectiveness: 89,
        usageCount: 45,
        lastUsed: new Date()
      },
      {
        id: '2',
        userEmotion: 'excited',
        responseType: 'celebratory',
        message: 'Votre enthousiasme est contagieux ! C\'est formidable de voir votre passion pour ce projet.',
        effectiveness: 94,
        usageCount: 32,
        lastUsed: new Date()
      },
      {
        id: '3',
        userEmotion: 'sadness',
        responseType: 'supportive',
        message: 'Je sens que vous traversez un moment difficile. Sachez que je suis là pour vous accompagner.',
        effectiveness: 91,
        usageCount: 28,
        lastUsed: new Date()
      }
    ];

    setEmpathicResponses(responses);

    // Sessions thérapeutiques simulées
    const sessions: TherapeuticSession[] = [
      {
        id: 'session_001',
        userId: 'user_001',
        sessionType: 'stress_relief',
        duration: 15,
        techniques: ['Respiration guidée', 'Restructuration cognitive'],
        outcome: 'positive',
        timestamp: new Date()
      },
      {
        id: 'session_002',
        userId: 'user_003',
        sessionType: 'emotional_support',
        duration: 20,
        techniques: ['Écoute active', 'Validation émotionnelle'],
        outcome: 'needs_followup',
        timestamp: new Date()
      }
    ];

    setTherapeuticSessions(sessions);
  };

  const simulateEmotionDetection = () => {
    const emotions: UserEmotionalState['detectedEmotion'][] = ['joy', 'neutral', 'frustrated', 'excited', 'sadness'];
    const contexts = ['Travail', 'Personnel', 'Apprentissage', 'Créatif', 'Social'];
    const users = ['Alice', 'Bob', 'Claire', 'David', 'Emma'];

    const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)];
    const randomContext = contexts[Math.floor(Math.random() * contexts.length)];
    const randomUser = users[Math.floor(Math.random() * users.length)];

    const newUserState: UserEmotionalState = {
      userId: `user_${Math.random().toString(36).substr(2, 9)}`,
      userName: randomUser,
      detectedEmotion: randomEmotion,
      confidence: 70 + Math.random() * 30,
      sentiment: randomEmotion === 'joy' || randomEmotion === 'excited' ? 'positive' :
                 randomEmotion === 'sadness' || randomEmotion === 'frustrated' ? 'negative' : 'neutral',
      intensity: 40 + Math.random() * 60,
      context: randomContext,
      timestamp: new Date()
    };

    setCurrentUserStates(prev => [newUserState, ...prev.slice(0, 9)]);

    // Générer une réponse empathique automatique
    generateEmpathicResponse(newUserState);
  };

  const updateUserEmotionalState = (userState: UserEmotionalState) => {
    setCurrentUserStates(prev => [userState, ...prev.slice(0, 9)]);
  };

  const integrateCoachProfile = (profile: any) => {
    // Intégration avec Virtual Coach pour enrichir le profil utilisateur
    console.log('🤝 Intégration profil Virtual Coach:', profile);
  };

  const addEmpathicResponse = (response: EmpathicResponse) => {
    setEmpathicResponses(prev => [response, ...prev.slice(0, 19)]);
  };

  const startTherapeuticSession = (session: TherapeuticSession) => {
    setTherapeuticSessions(prev => [session, ...prev.slice(0, 9)]);
  };

  const generateEmpathicResponse = (userState: UserEmotionalState) => {
    const responseTemplates = {
      frustrated: ['Je comprends votre frustration', 'Prenons le temps de résoudre cela', 'Votre patience est appréciée'],
      excited: ['Votre enthousiasme est merveilleux', 'C\'est formidable de voir votre passion', 'Votre énergie positive est inspirante'],
      sadness: ['Je sens votre tristesse', 'Vous n\'êtes pas seul(e)', 'Prenez le temps qu\'il vous faut'],
      joy: ['Votre joie est contagieuse', 'C\'est merveilleux de vous voir heureux', 'Votre bonheur me réjouit'],
      neutral: ['Je suis là pour vous accompagner', 'Comment puis-je vous aider aujourd\'hui', 'Que puis-je faire pour vous'],
      fear: ['Je comprends vos inquiétudes', 'Nous allons traverser cela ensemble', 'Votre courage est admirable'],
      surprise: ['Quelle belle surprise', 'C\'est inattendu et intéressant', 'Votre réaction est tout à fait naturelle']
    };

    const templates = responseTemplates[userState.detectedEmotion] || responseTemplates.neutral;
    const randomTemplate = templates[Math.floor(Math.random() * templates.length)];

    const newResponse: EmpathicResponse = {
      id: `response_${Date.now()}`,
      userEmotion: userState.detectedEmotion,
      responseType: userState.sentiment === 'positive' ? 'celebratory' :
                   userState.sentiment === 'negative' ? 'supportive' : 'understanding',
      message: randomTemplate,
      effectiveness: 80 + Math.random() * 20,
      usageCount: 1,
      lastUsed: new Date()
    };

    addEmpathicResponse(newResponse);
  };

  const getEmotionIcon = (emotion: string) => {
    switch (emotion) {
      case 'joy': case 'excited': return <Smile className="text-yellow-400" size={16} />;
      case 'sadness': case 'frustrated': return <Frown className="text-blue-400" size={16} />;
      case 'fear': return <AlertCircle className="text-red-400" size={16} />;
      case 'surprise': return <CheckCircle className="text-orange-400" size={16} />;
      default: return <MessageCircle className="text-gray-400" size={16} />;
    }
  };

  const getEmotionColor = (emotion: string) => {
    switch (emotion) {
      case 'joy': case 'excited': return 'text-yellow-400';
      case 'sadness': case 'frustrated': return 'text-blue-400';
      case 'fear': return 'text-red-400';
      case 'surprise': return 'text-orange-400';
      default: return 'text-gray-400';
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-400';
      case 'negative': return 'text-red-400';
      case 'neutral': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'emotionDetectionAccuracy': return <Target className="text-blue-400" size={20} />;
      case 'responseRelevance': return <MessageCircle className="text-green-400" size={20} />;
      case 'userSatisfaction': return <Smile className="text-yellow-400" size={20} />;
      case 'therapeuticEffectiveness': return <Heart className="text-pink-400" size={20} />;
      case 'adaptationSpeed': return <Zap className="text-purple-400" size={20} />;
      default: return <Activity className="text-gray-400" size={20} />;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence > 85) return 'text-green-400';
    if (confidence > 70) return 'text-yellow-400';
    if (confidence > 55) return 'text-orange-400';
    return 'text-red-400';
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">

        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center">
              <Heart className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Empathie d'Hanuman
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Interface d'Empathie Adaptative • Virtual Coach, NLP Émotionnel
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>

            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
            }`}>
              <Users size={16} className="text-blue-400" />
              <span className="text-sm font-medium">
                {activeUsers} utilisateurs actifs
              </span>
            </div>

            <button
              onClick={() => setRealTimeAnalysis(!realTimeAnalysis)}
              className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                realTimeAnalysis
                  ? 'bg-green-500 text-white hover:bg-green-600'
                  : 'bg-gray-500 text-white hover:bg-gray-600'
              }`}
            >
              {realTimeAnalysis ? 'Analyse ON' : 'Analyse OFF'}
            </button>
          </div>
        </div>

        {/* Métriques d'Empathie */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques d'Empathie
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {Object.entries(empathyMetrics).map(([key, value]) => (
              <div key={key} className="text-center">
                <div className="flex items-center justify-center mb-2">
                  {getMetricIcon(key)}
                </div>
                <div className={`text-3xl font-bold ${getConfidenceColor(value)}`}>
                  {value.toFixed(1)}%
                </div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {key === 'emotionDetectionAccuracy' && 'Détection'}
                  {key === 'responseRelevance' && 'Pertinence'}
                  {key === 'userSatisfaction' && 'Satisfaction'}
                  {key === 'therapeuticEffectiveness' && 'Thérapie'}
                  {key === 'adaptationSpeed' && 'Adaptation'}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">

          {/* États Émotionnels Utilisateurs */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              👥 États Émotionnels Détectés
            </h3>
            <div className="space-y-3">
              {currentUserStates.map((userState, index) => (
                <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getEmotionIcon(userState.detectedEmotion)}
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {userState.userName}
                      </span>
                    </div>
                    <span className={`text-xs ${getConfidenceColor(userState.confidence)}`}>
                      {userState.confidence.toFixed(0)}% confiance
                    </span>
                  </div>
                  <div className="flex items-center justify-between mb-1">
                    <span className={`text-sm ${getEmotionColor(userState.detectedEmotion)}`}>
                      {userState.detectedEmotion.charAt(0).toUpperCase() + userState.detectedEmotion.slice(1)}
                    </span>
                    <span className={`text-sm ${getSentimentColor(userState.sentiment)}`}>
                      {userState.sentiment}
                    </span>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {userState.context} • Intensité: {userState.intensity.toFixed(0)}%
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {userState.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Réponses Empathiques */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              💬 Réponses Empathiques
            </h3>
            <div className="space-y-3">
              {empathicResponses.slice(0, 6).map((response) => (
                <div key={response.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-xs px-2 py-1 rounded ${
                      response.responseType === 'supportive' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      response.responseType === 'celebratory' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      response.responseType === 'calming' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                    }`}>
                      {response.responseType}
                    </span>
                    <span className={`text-xs ${getConfidenceColor(response.effectiveness)}`}>
                      {response.effectiveness.toFixed(0)}% efficace
                    </span>
                  </div>
                  <div className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                    "{response.message}"
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Pour: {response.userEmotion}
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Utilisé {response.usageCount} fois
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Sessions Thérapeutiques */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🧘 Sessions Thérapeutiques
            </h3>
            <div className="space-y-3">
              {therapeuticSessions.map((session) => (
                <div key={session.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {session.sessionType.replace('_', ' ').charAt(0).toUpperCase() + session.sessionType.replace('_', ' ').slice(1)}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded ${
                      session.outcome === 'positive' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      session.outcome === 'needs_followup' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                    }`}>
                      {session.outcome}
                    </span>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                    Durée: {session.duration} min • Utilisateur: {session.userId}
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Techniques: {session.techniques.join(', ')}
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
                    {session.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanEmpathyInterface;
