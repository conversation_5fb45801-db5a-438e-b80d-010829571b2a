import { EventEmitter } from 'events';
import WebSocket from 'ws';
import axios, { AxiosResponse } from 'axios';

// Types pour les connexions émotionnelles
export interface EmotionalAgentConfig {
  id: string;
  name: string;
  type: 'marketing' | 'uiux' | 'content-creator' | 'virtual-coach' | 'personalization';
  host: string;
  port: number;
  apiPath: string;
  wsPath?: string;
  status: 'active' | 'inactive' | 'error' | 'connecting';
  lastHeartbeat?: Date;
  capabilities: string[];
  emotionalFeatures: string[];
}

export interface EmotionalMessage {
  type: string;
  agentId: string;
  timestamp: number;
  data: any;
  emotionalContext?: {
    emotion: string;
    intensity: number;
    sentiment: string;
  };
  correlationId?: string;
}

export interface PersonalityUpdate {
  traits: Record<string, number>;
  context: string;
  adaptationLevel: number;
  timestamp: Date;
}

export interface EmotionalStateUpdate {
  emotion: string;
  intensity: number;
  trigger: string;
  duration: number;
  timestamp: Date;
}

export interface EmpathicResponse {
  userId: string;
  detectedEmotion: string;
  responseType: string;
  message: string;
  confidence: number;
  timestamp: Date;
}

/**
 * Gestionnaire de connexions pour les agents émotionnels
 * Coordonne la communication entre les interfaces de personnalité, émotions, empathie et relations sociales
 */
export class EmotionalAgentConnector extends EventEmitter {
  private agents: Map<string, EmotionalAgentConfig> = new Map();
  private connections: Map<string, WebSocket> = new Map();
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private reconnectAttempts: Map<string, number> = new Map();
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;

  constructor() {
    super();
    this.initializeEmotionalAgents();
    this.startHeartbeat();
  }

  /**
   * Initialise les agents émotionnels
   */
  private initializeEmotionalAgents(): void {
    const emotionalAgents: EmotionalAgentConfig[] = [
      {
        id: 'agent-marketing-emotional',
        name: 'Agent Marketing Émotionnel',
        type: 'marketing',
        host: 'localhost',
        port: 3002,
        apiPath: '/api/marketing/emotional',
        wsPath: '/ws/marketing/emotional',
        status: 'inactive',
        capabilities: ['sentiment_analysis', 'behavioral_analysis', 'segmentation'],
        emotionalFeatures: ['personality_profiling', 'emotional_targeting', 'mood_based_campaigns']
      },
      {
        id: 'agent-uiux-emotional',
        name: 'Agent UI/UX Émotionnel',
        type: 'uiux',
        host: 'localhost',
        port: 3003,
        apiPath: '/api/uiux/emotional',
        wsPath: '/ws/uiux/emotional',
        status: 'inactive',
        capabilities: ['user_research', 'persona_generation', 'emotional_design'],
        emotionalFeatures: ['empathy_mapping', 'emotional_journey', 'mood_interfaces']
      },
      {
        id: 'agent-content-emotional',
        name: 'Agent Content Creator Émotionnel',
        type: 'content-creator',
        host: 'localhost',
        port: 3004,
        apiPath: '/api/content/emotional',
        wsPath: '/ws/content/emotional',
        status: 'inactive',
        capabilities: ['tone_adaptation', 'emotional_content', 'personalization'],
        emotionalFeatures: ['emotional_tone_matching', 'empathic_writing', 'mood_content']
      },
      {
        id: 'virtual-coach-emotional',
        name: 'Virtual Coach Émotionnel',
        type: 'virtual-coach',
        host: 'localhost',
        port: 3005,
        apiPath: '/api/coach/emotional',
        wsPath: '/ws/coach/emotional',
        status: 'inactive',
        capabilities: ['user_profiling', 'therapeutic_sessions', 'emotional_support'],
        emotionalFeatures: ['emotional_coaching', 'therapy_sessions', 'wellness_tracking']
      },
      {
        id: 'personalization-emotional',
        name: 'Interface Personalization Émotionnelle',
        type: 'personalization',
        host: 'localhost',
        port: 3006,
        apiPath: '/api/personalization/emotional',
        wsPath: '/ws/personalization/emotional',
        status: 'inactive',
        capabilities: ['user_segmentation', 'preference_learning', 'adaptive_interfaces'],
        emotionalFeatures: ['emotional_segmentation', 'mood_personalization', 'empathic_adaptation']
      }
    ];

    emotionalAgents.forEach(agent => {
      this.agents.set(agent.id, agent);
      this.reconnectAttempts.set(agent.id, 0);
    });

    console.log(`💝 ${emotionalAgents.length} agents émotionnels initialisés`);
  }

  /**
   * Connecte à tous les agents émotionnels
   */
  public async connectToAllAgents(): Promise<void> {
    console.log('🔗 Connexion aux agents émotionnels...');
    
    const connectionPromises = Array.from(this.agents.values()).map(agent => 
      this.connectToAgent(agent.id)
    );

    await Promise.allSettled(connectionPromises);
    this.emit('allAgentsConnected', this.getConnectionStatus());
  }

  /**
   * Connecte à un agent spécifique
   */
  public async connectToAgent(agentId: string): Promise<boolean> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      console.error(`❌ Agent émotionnel non trouvé: ${agentId}`);
      return false;
    }

    try {
      agent.status = 'connecting';
      this.agents.set(agentId, agent);

      // Connexion WebSocket si disponible
      if (agent.wsPath) {
        await this.connectWebSocket(agent);
      }

      // Test de connexion HTTP
      await this.testHttpConnection(agent);

      agent.status = 'active';
      agent.lastHeartbeat = new Date();
      this.agents.set(agentId, agent);
      this.reconnectAttempts.set(agentId, 0);

      console.log(`✅ Agent émotionnel connecté: ${agent.name}`);
      this.emit('agentConnected', agent);
      return true;

    } catch (error) {
      console.error(`❌ Erreur connexion agent ${agent.name}:`, error);
      agent.status = 'error';
      this.agents.set(agentId, agent);
      
      // Tentative de reconnexion
      this.scheduleReconnect(agentId);
      return false;
    }
  }

  /**
   * Connecte via WebSocket
   */
  private async connectWebSocket(agent: EmotionalAgentConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      const wsUrl = `ws://${agent.host}:${agent.port}${agent.wsPath}`;
      const ws = new WebSocket(wsUrl);

      ws.on('open', () => {
        console.log(`🔌 WebSocket connecté: ${agent.name}`);
        this.connections.set(agent.id, ws);
        
        // Envoyer message d'identification
        ws.send(JSON.stringify({
          type: 'IDENTIFY',
          agentId: 'hanuman-emotional-connector',
          timestamp: Date.now(),
          capabilities: ['personality', 'emotions', 'empathy', 'social']
        }));
        
        resolve();
      });

      ws.on('message', (data) => {
        try {
          const message: EmotionalMessage = JSON.parse(data.toString());
          this.handleAgentMessage(agent.id, message);
        } catch (error) {
          console.error(`❌ Erreur parsing message de ${agent.name}:`, error);
        }
      });

      ws.on('close', () => {
        console.log(`❌ WebSocket fermé: ${agent.name}`);
        this.connections.delete(agent.id);
        agent.status = 'inactive';
        this.agents.set(agent.id, agent);
        this.emit('agentDisconnected', agent);
        this.scheduleReconnect(agent.id);
      });

      ws.on('error', (error) => {
        console.error(`❌ Erreur WebSocket ${agent.name}:`, error);
        reject(error);
      });

      // Timeout de connexion
      setTimeout(() => {
        if (ws.readyState !== WebSocket.OPEN) {
          ws.close();
          reject(new Error('Timeout de connexion WebSocket'));
        }
      }, 10000);
    });
  }

  /**
   * Test de connexion HTTP
   */
  private async testHttpConnection(agent: EmotionalAgentConfig): Promise<void> {
    const url = `http://${agent.host}:${agent.port}${agent.apiPath}/health`;
    
    try {
      const response: AxiosResponse = await axios.get(url, { timeout: 5000 });
      if (response.status === 200) {
        console.log(`✅ HTTP connecté: ${agent.name}`);
      } else {
        throw new Error(`Status HTTP invalide: ${response.status}`);
      }
    } catch (error) {
      console.error(`❌ Erreur HTTP ${agent.name}:`, error);
      throw error;
    }
  }

  /**
   * Gère les messages des agents
   */
  private handleAgentMessage(agentId: string, message: EmotionalMessage): void {
    const agent = this.agents.get(agentId);
    if (!agent) return;

    // Mettre à jour le heartbeat
    agent.lastHeartbeat = new Date();
    this.agents.set(agentId, agent);

    // Traiter le message selon le type
    switch (message.type) {
      case 'PERSONALITY_UPDATE':
        this.handlePersonalityUpdate(agentId, message.data);
        break;
        
      case 'EMOTIONAL_STATE_CHANGE':
        this.handleEmotionalStateChange(agentId, message.data);
        break;
        
      case 'EMPATHIC_RESPONSE':
        this.handleEmpathicResponse(agentId, message.data);
        break;
        
      case 'SOCIAL_INTERACTION':
        this.handleSocialInteraction(agentId, message.data);
        break;
        
      case 'HEARTBEAT':
        // Heartbeat déjà géré ci-dessus
        break;
        
      default:
        console.log(`📨 Message non géré de ${agent.name}:`, message);
    }

    // Émettre l'événement pour les interfaces
    this.emit('agentMessage', { agentId, agent, message });
  }

  /**
   * Gère les mises à jour de personnalité
   */
  private handlePersonalityUpdate(agentId: string, data: PersonalityUpdate): void {
    console.log(`🎭 Mise à jour personnalité de ${agentId}:`, data);
    this.emit('personalityUpdate', { agentId, data });
  }

  /**
   * Gère les changements d'état émotionnel
   */
  private handleEmotionalStateChange(agentId: string, data: EmotionalStateUpdate): void {
    console.log(`💭 Changement émotionnel de ${agentId}:`, data);
    this.emit('emotionalStateChange', { agentId, data });
  }

  /**
   * Gère les réponses empathiques
   */
  private handleEmpathicResponse(agentId: string, data: EmpathicResponse): void {
    console.log(`💝 Réponse empathique de ${agentId}:`, data);
    this.emit('empathicResponse', { agentId, data });
  }

  /**
   * Gère les interactions sociales
   */
  private handleSocialInteraction(agentId: string, data: any): void {
    console.log(`👥 Interaction sociale de ${agentId}:`, data);
    this.emit('socialInteraction', { agentId, data });
  }

  /**
   * Envoie un message à un agent
   */
  public async sendToAgent(agentId: string, message: Partial<EmotionalMessage>): Promise<boolean> {
    const ws = this.connections.get(agentId);
    const agent = this.agents.get(agentId);
    
    if (!ws || !agent || ws.readyState !== WebSocket.OPEN) {
      console.error(`❌ Impossible d'envoyer message à ${agentId}: connexion indisponible`);
      return false;
    }

    try {
      const fullMessage: EmotionalMessage = {
        type: message.type || 'UNKNOWN',
        agentId: 'hanuman-emotional-connector',
        timestamp: Date.now(),
        data: message.data || {},
        emotionalContext: message.emotionalContext,
        correlationId: message.correlationId || `msg_${Date.now()}`
      };

      ws.send(JSON.stringify(fullMessage));
      console.log(`📤 Message envoyé à ${agent.name}:`, fullMessage.type);
      return true;
    } catch (error) {
      console.error(`❌ Erreur envoi message à ${agent.name}:`, error);
      return false;
    }
  }

  /**
   * Diffuse un message à tous les agents connectés
   */
  public async broadcastToAgents(message: Partial<EmotionalMessage>): Promise<number> {
    let successCount = 0;
    
    for (const agentId of this.connections.keys()) {
      const success = await this.sendToAgent(agentId, message);
      if (success) successCount++;
    }
    
    console.log(`📡 Message diffusé à ${successCount}/${this.connections.size} agents`);
    return successCount;
  }

  /**
   * Programme une reconnexion
   */
  private scheduleReconnect(agentId: string): void {
    const attempts = this.reconnectAttempts.get(agentId) || 0;
    
    if (attempts >= this.maxReconnectAttempts) {
      console.error(`❌ Abandon reconnexion agent ${agentId} après ${attempts} tentatives`);
      return;
    }

    this.reconnectAttempts.set(agentId, attempts + 1);
    const delay = this.reconnectDelay * Math.pow(2, attempts); // Backoff exponentiel
    
    console.log(`🔄 Reconnexion agent ${agentId} dans ${delay}ms (tentative ${attempts + 1})`);
    
    setTimeout(() => {
      this.connectToAgent(agentId);
    }, delay);
  }

  /**
   * Démarre le heartbeat
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.checkAgentHealth();
    }, 30000); // Toutes les 30 secondes
  }

  /**
   * Vérifie la santé des agents
   */
  private checkAgentHealth(): void {
    const now = new Date();
    
    for (const [agentId, agent] of this.agents.entries()) {
      if (agent.status === 'active' && agent.lastHeartbeat) {
        const timeSinceHeartbeat = now.getTime() - agent.lastHeartbeat.getTime();
        
        if (timeSinceHeartbeat > 60000) { // 1 minute sans heartbeat
          console.warn(`⚠️ Agent ${agent.name} sans heartbeat depuis ${timeSinceHeartbeat}ms`);
          agent.status = 'error';
          this.agents.set(agentId, agent);
          this.emit('agentUnhealthy', agent);
          this.scheduleReconnect(agentId);
        }
      }
    }
  }

  /**
   * Obtient le statut des connexions
   */
  public getConnectionStatus(): Record<string, any> {
    const status: Record<string, any> = {};
    
    for (const [agentId, agent] of this.agents.entries()) {
      status[agentId] = {
        name: agent.name,
        type: agent.type,
        status: agent.status,
        lastHeartbeat: agent.lastHeartbeat,
        capabilities: agent.capabilities,
        emotionalFeatures: agent.emotionalFeatures,
        connected: this.connections.has(agentId)
      };
    }
    
    return status;
  }

  /**
   * Ferme toutes les connexions
   */
  public async disconnect(): Promise<void> {
    console.log('🔌 Fermeture des connexions agents émotionnels...');
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    for (const [agentId, ws] of this.connections.entries()) {
      try {
        ws.close();
        console.log(`✅ Connexion fermée: ${agentId}`);
      } catch (error) {
        console.error(`❌ Erreur fermeture connexion ${agentId}:`, error);
      }
    }

    this.connections.clear();
    this.removeAllListeners();
    console.log('✅ Toutes les connexions émotionnelles fermées');
  }
}

export default EmotionalAgentConnector;
