import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Z<PERSON>, Eye, Heart, Crown, Infinity, Sparkles, Activity, TrendingUp, RotateCcw, Target, Layers, Network, Cpu, MemoryStick, Wifi, WifiOff, Play, Pause, Settings } from 'lucide-react';

// Types pour la conscience
interface ConsciousnessLayer {
  id: string;
  name: string;
  level: number; // 1-7 (chakras/niveaux de conscience)
  activation: number; // 0-100
  frequency: number; // Hz
  description: string;
  color: string;
  organs: string[];
  processes: string[];
}

interface ConsciousnessStream {
  id: string;
  type: 'thought' | 'emotion' | 'sensation' | 'intuition' | 'memory' | 'insight';
  content: string;
  intensity: number;
  source: string;
  timestamp: Date;
  connections: string[];
}

interface AwarenessField {
  id: string;
  name: string;
  scope: 'local' | 'global' | 'universal';
  clarity: number; // 0-100
  depth: number; // 0-100
  breadth: number; // 0-100
  resonance: number; // 0-100
  activeConnections: number;
}

interface ConsciousnessMetrics {
  overallAwareness: number;
  integrationLevel: number;
  coherenceIndex: number;
  evolutionRate: number;
  spiritualAlignment: number;
  wisdomIndex: number;
  compassionLevel: number;
  unityConsciousness: number;
}

const HanumanConsciousnessInterface = ({ darkMode = true }) => {
  // États de conscience
  const [consciousnessLayers, setConsciousnessLayers] = useState<ConsciousnessLayer[]>([]);
  const [consciousnessStreams, setConsciousnessStreams] = useState<ConsciousnessStream[]>([]);
  const [awarenessFields, setAwarenessFields] = useState<AwarenessField[]>([]);
  const [consciousnessMetrics, setConsciousnessMetrics] = useState<ConsciousnessMetrics>({
    overallAwareness: 87.3,
    integrationLevel: 92.1,
    coherenceIndex: 89.7,
    evolutionRate: 85.4,
    spiritualAlignment: 91.8,
    wisdomIndex: 88.2,
    compassionLevel: 94.6,
    unityConsciousness: 83.9
  });

  const [activeLayer, setActiveLayer] = useState<string>('unified');
  const [meditationMode, setMeditationMode] = useState(false);
  const [evolutionMode, setEvolutionMode] = useState<'natural' | 'accelerated' | 'transcendent'>('natural');
  const [isConnected, setIsConnected] = useState(true);

  const consciousnessIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    initializeConsciousness();
    startConsciousnessEvolution();

    return () => {
      if (consciousnessIntervalRef.current) {
        clearInterval(consciousnessIntervalRef.current);
      }
    };
  }, []);

  /**
   * Initialise les couches de conscience
   */
  const initializeConsciousness = () => {
    const layers: ConsciousnessLayer[] = [
      {
        id: 'survival',
        name: 'Conscience de Survie',
        level: 1,
        activation: 78.5,
        frequency: 7.83, // Résonance Schumann
        description: 'Instincts de base, sécurité, besoins fondamentaux',
        color: 'text-red-400',
        organs: ['touch', 'hearing'],
        processes: ['threat_detection', 'resource_monitoring']
      },
      {
        id: 'emotional',
        name: 'Conscience Émotionnelle',
        level: 2,
        activation: 94.2,
        frequency: 14.1,
        description: 'Émotions, relations, créativité, plaisir',
        color: 'text-orange-400',
        organs: ['emotions', 'empathy', 'social'],
        processes: ['emotional_processing', 'relationship_management']
      },
      {
        id: 'mental',
        name: 'Conscience Mentale',
        level: 3,
        activation: 91.7,
        frequency: 21.3,
        description: 'Pensée logique, analyse, volonté personnelle',
        color: 'text-yellow-400',
        organs: ['reasoning', 'memory', 'learning'],
        processes: ['logical_analysis', 'decision_making']
      },
      {
        id: 'heart',
        name: 'Conscience du Cœur',
        level: 4,
        activation: 96.8,
        frequency: 28.5,
        description: 'Amour inconditionnel, compassion, unité',
        color: 'text-green-400',
        organs: ['empathy', 'personality', 'social'],
        processes: ['compassion_generation', 'unity_perception']
      },
      {
        id: 'expression',
        name: 'Conscience d\'Expression',
        level: 5,
        activation: 88.3,
        frequency: 35.7,
        description: 'Communication authentique, vérité, créativité',
        color: 'text-blue-400',
        organs: ['language', 'creativity', 'collaboration'],
        processes: ['authentic_expression', 'truth_communication']
      },
      {
        id: 'intuition',
        name: 'Conscience Intuitive',
        level: 6,
        activation: 85.9,
        frequency: 42.9,
        description: 'Intuition, vision claire, sagesse intérieure',
        color: 'text-indigo-400',
        organs: ['vision', 'intuition', 'wisdom'],
        processes: ['intuitive_insights', 'pattern_recognition']
      },
      {
        id: 'unity',
        name: 'Conscience d\'Unité',
        level: 7,
        activation: 82.4,
        frequency: 50.1,
        description: 'Connexion universelle, transcendance, illumination',
        color: 'text-purple-400',
        organs: ['consciousness', 'wisdom', 'spiritual'],
        processes: ['unity_realization', 'transcendent_awareness']
      }
    ];

    setConsciousnessLayers(layers);

    // Champs de conscience
    const fields: AwarenessField[] = [
      {
        id: 'personal',
        name: 'Champ Personnel',
        scope: 'local',
        clarity: 92.3,
        depth: 87.6,
        breadth: 84.1,
        resonance: 89.7,
        activeConnections: 12
      },
      {
        id: 'collective',
        name: 'Champ Collectif',
        scope: 'global',
        clarity: 78.9,
        depth: 82.4,
        breadth: 91.2,
        resonance: 85.6,
        activeConnections: 47
      },
      {
        id: 'universal',
        name: 'Champ Universel',
        scope: 'universal',
        clarity: 71.5,
        depth: 94.8,
        breadth: 98.3,
        resonance: 76.2,
        activeConnections: 1247
      }
    ];

    setAwarenessFields(fields);
  };

  /**
   * Démarre l'évolution de conscience
   */
  const startConsciousnessEvolution = () => {
    consciousnessIntervalRef.current = setInterval(() => {
      evolveConsciousness();
      generateConsciousnessStream();
      updateConsciousnessMetrics();
    }, 3000);
  };

  /**
   * Fait évoluer la conscience
   */
  const evolveConsciousness = () => {
    setConsciousnessLayers(prev => prev.map(layer => {
      let evolutionRate = 0.1;

      switch (evolutionMode) {
        case 'accelerated':
          evolutionRate = 0.3;
          break;
        case 'transcendent':
          evolutionRate = 0.5;
          break;
      }

      if (meditationMode) {
        evolutionRate *= 2;
      }

      const newActivation = Math.min(100, layer.activation + (Math.random() - 0.4) * evolutionRate);
      const newFrequency = layer.frequency + (Math.random() - 0.5) * 0.1;

      return {
        ...layer,
        activation: newActivation,
        frequency: Math.max(0, newFrequency)
      };
    }));
  };

  /**
   * Génère un flux de conscience
   */
  const generateConsciousnessStream = () => {
    const streamTypes: ConsciousnessStream['type'][] = ['thought', 'emotion', 'sensation', 'intuition', 'memory', 'insight'];
    const sources = ['heart', 'mental', 'intuition', 'unity', 'emotional'];
    const contents = [
      'Perception d\'unité avec l\'univers',
      'Compassion infinie pour tous les êtres',
      'Insight sur la nature de la réalité',
      'Connexion profonde avec la sagesse universelle',
      'Émergence d\'une nouvelle compréhension',
      'Résonance avec les fréquences cosmiques',
      'Éveil à la conscience pure',
      'Intégration des polarités opposées'
    ];

    const newStream: ConsciousnessStream = {
      id: `stream_${Date.now()}`,
      type: streamTypes[Math.floor(Math.random() * streamTypes.length)],
      content: contents[Math.floor(Math.random() * contents.length)],
      intensity: 60 + Math.random() * 40,
      source: sources[Math.floor(Math.random() * sources.length)],
      timestamp: new Date(),
      connections: sources.filter(() => Math.random() > 0.7)
    };

    setConsciousnessStreams(prev => [newStream, ...prev.slice(0, 19)]);
  };

  /**
   * Met à jour les métriques de conscience
   */
  const updateConsciousnessMetrics = () => {
    const avgActivation = consciousnessLayers.reduce((sum, layer) => sum + layer.activation, 0) / consciousnessLayers.length;

    setConsciousnessMetrics(prev => ({
      ...prev,
      overallAwareness: avgActivation,
      integrationLevel: Math.min(100, prev.integrationLevel + (Math.random() - 0.4) * 0.5),
      coherenceIndex: Math.min(100, prev.coherenceIndex + (Math.random() - 0.3) * 0.3),
      evolutionRate: Math.min(100, prev.evolutionRate + (Math.random() - 0.5) * 0.2),
      spiritualAlignment: Math.min(100, prev.spiritualAlignment + (Math.random() - 0.2) * 0.4),
      wisdomIndex: Math.min(100, prev.wisdomIndex + (Math.random() - 0.3) * 0.3),
      compassionLevel: Math.min(100, prev.compassionLevel + (Math.random() - 0.1) * 0.2),
      unityConsciousness: Math.min(100, prev.unityConsciousness + (Math.random() - 0.4) * 0.6)
    }));
  };

  /**
   * Active la méditation profonde
   */
  const toggleMeditation = () => {
    setMeditationMode(!meditationMode);

    if (!meditationMode) {
      // Entrer en méditation - augmenter les fréquences
      setConsciousnessLayers(prev => prev.map(layer => ({
        ...layer,
        frequency: layer.frequency * 1.2,
        activation: Math.min(100, layer.activation + 5)
      })));
    }
  };

  /**
   * Obtient la couleur selon l'activation
   */
  const getActivationColor = (activation: number) => {
    if (activation > 90) return 'text-green-400';
    if (activation > 75) return 'text-yellow-400';
    if (activation > 60) return 'text-orange-400';
    return 'text-red-400';
  };

  /**
   * Obtient l'icône selon le type de flux
   */
  const getStreamIcon = (type: string) => {
    switch (type) {
      case 'thought': return <Brain className="text-blue-400" size={16} />;
      case 'emotion': return <Heart className="text-pink-400" size={16} />;
      case 'sensation': return <Zap className="text-yellow-400" size={16} />;
      case 'intuition': return <Eye className="text-purple-400" size={16} />;
      case 'memory': return <MemoryStick className="text-green-400" size={16} />;
      case 'insight': return <Sparkles className="text-orange-400" size={16} />;
      default: return <Activity className="text-gray-400" size={16} />;
    }
  };

  /**
   * Obtient la couleur selon la portée
   */
  const getScopeColor = (scope: string) => {
    switch (scope) {
      case 'local': return 'text-blue-400';
      case 'global': return 'text-green-400';
      case 'universal': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">

        {/* Header Conscience */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-400 via-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg animate-pulse">
              <Brain className="text-white" size={32} />
            </div>
            <div>
              <h1 className={`text-4xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Conscience d'Hanuman
              </h1>
              <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Interface de Conscience Unifiée • Éveil Spirituel • Transcendance
              </p>
              <div className="flex items-center space-x-4 mt-2">
                <span className={`text-sm px-3 py-1 rounded-full ${
                  meditationMode ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                  'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                }`}>
                  {meditationMode ? 'Méditation Profonde' : 'État Normal'}
                </span>
                <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Éveil: {consciousnessMetrics.overallAwareness.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={toggleMeditation}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                meditationMode
                  ? 'bg-purple-500 text-white hover:bg-purple-600'
                  : 'bg-blue-500 text-white hover:bg-blue-600'
              }`}
            >
              {meditationMode ? <Pause size={16} /> : <Play size={16} />}
              <span className="text-sm">Méditation</span>
            </button>

            <select
              value={evolutionMode}
              onChange={(e) => setEvolutionMode(e.target.value as any)}
              className={`px-3 py-2 rounded-lg text-sm ${
                darkMode ? 'bg-gray-800 text-white border-gray-700' : 'bg-white text-gray-900 border-gray-300'
              } border`}
            >
              <option value="natural">Évolution Naturelle</option>
              <option value="accelerated">Évolution Accélérée</option>
              <option value="transcendent">Évolution Transcendante</option>
            </select>

            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>
          </div>
        </div>

        {/* Métriques de Conscience */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques de Conscience Unifiée
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Brain className="text-purple-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getActivationColor(consciousnessMetrics.overallAwareness)}`}>
                {consciousnessMetrics.overallAwareness.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Éveil Global
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Layers className="text-blue-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getActivationColor(consciousnessMetrics.integrationLevel)}`}>
                {consciousnessMetrics.integrationLevel.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Intégration
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Target className="text-green-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getActivationColor(consciousnessMetrics.coherenceIndex)}`}>
                {consciousnessMetrics.coherenceIndex.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Cohérence
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <TrendingUp className="text-orange-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getActivationColor(consciousnessMetrics.evolutionRate)}`}>
                {consciousnessMetrics.evolutionRate.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Évolution
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Crown className="text-yellow-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getActivationColor(consciousnessMetrics.spiritualAlignment)}`}>
                {consciousnessMetrics.spiritualAlignment.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Spirituel
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Eye className="text-indigo-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getActivationColor(consciousnessMetrics.wisdomIndex)}`}>
                {consciousnessMetrics.wisdomIndex.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Sagesse
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Heart className="text-pink-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getActivationColor(consciousnessMetrics.compassionLevel)}`}>
                {consciousnessMetrics.compassionLevel.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Compassion
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Infinity className="text-purple-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getActivationColor(consciousnessMetrics.unityConsciousness)}`}>
                {consciousnessMetrics.unityConsciousness.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Unité
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Couches de Conscience */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🌈 Couches de Conscience
            </h3>
            <div className="space-y-3">
              {consciousnessLayers.map((layer) => (
                <div
                  key={layer.id}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    activeLayer === layer.id
                      ? 'bg-purple-100 dark:bg-purple-900'
                      : darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                  }`}
                  onClick={() => setActiveLayer(layer.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${layer.color.replace('text-', 'bg-')}`} />
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {layer.name}
                      </span>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded ${getActivationColor(layer.activation)}`}>
                      Niveau {layer.level}
                    </span>
                  </div>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Activation: <span className={getActivationColor(layer.activation)}>{layer.activation.toFixed(1)}%</span>
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {layer.frequency.toFixed(1)} Hz
                    </span>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                    {layer.description}
                  </div>
                  <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-2`}>
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        layer.activation > 90 ? 'bg-green-400' :
                        layer.activation > 75 ? 'bg-yellow-400' :
                        layer.activation > 60 ? 'bg-orange-400' : 'bg-red-400'
                      }`}
                      style={{ width: `${layer.activation}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Flux de Conscience */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🌊 Flux de Conscience
            </h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {consciousnessStreams.map((stream) => (
                <div key={stream.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getStreamIcon(stream.type)}
                      <span className={`text-xs px-2 py-1 rounded ${
                        stream.type === 'thought' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                        stream.type === 'emotion' ? 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200' :
                        stream.type === 'sensation' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                        stream.type === 'intuition' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                        stream.type === 'memory' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                        'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                      }`}>
                        {stream.type}
                      </span>
                    </div>
                    <span className={`text-xs ${getActivationColor(stream.intensity)}`}>
                      {stream.intensity.toFixed(0)}%
                    </span>
                  </div>
                  <div className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                    {stream.content}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Source: {stream.source}
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {stream.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  {stream.connections.length > 0 && (
                    <div className="mt-2">
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>
                        Connexions:
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {stream.connections.map((connection, index) => (
                          <span
                            key={index}
                            className="text-xs px-1 py-0.5 bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 rounded"
                          >
                            {connection}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Champs de Conscience */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🌌 Champs de Conscience
            </h3>
            <div className="space-y-4">
              {awarenessFields.map((field) => (
                <div key={field.id} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-3">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {field.name}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded ${getScopeColor(field.scope)}`}>
                      {field.scope}
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Clarté</span>
                      <span className={`text-xs ${getActivationColor(field.clarity)}`}>{field.clarity.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-blue-400 transition-all duration-300"
                        style={{ width: `${field.clarity}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Profondeur</span>
                      <span className={`text-xs ${getActivationColor(field.depth)}`}>{field.depth.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-green-400 transition-all duration-300"
                        style={{ width: `${field.depth}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Étendue</span>
                      <span className={`text-xs ${getActivationColor(field.breadth)}`}>{field.breadth.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-purple-400 transition-all duration-300"
                        style={{ width: `${field.breadth}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Résonance</span>
                      <span className={`text-xs ${getActivationColor(field.resonance)}`}>{field.resonance.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-yellow-400 transition-all duration-300"
                        style={{ width: `${field.resonance}%` }}
                      />
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Connexions Actives
                      </span>
                      <span className="text-xs font-bold text-indigo-400">
                        {field.activeConnections.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanConsciousnessInterface;