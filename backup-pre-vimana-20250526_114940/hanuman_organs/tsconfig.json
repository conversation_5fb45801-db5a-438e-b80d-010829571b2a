{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6", "es2020"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/services/*": ["./services/*"], "@/utils/*": ["./utils/*"], "@/organs/*": ["./*"], "@/divine/*": ["./divine/*"], "@/types/*": ["./types/*"], "@/hooks/*": ["./hooks/*"], "@/contexts/*": ["./contexts/*"], "@/constants/*": ["./constants/*"]}, "typeRoots": ["./node_modules/@types", "./types"], "types": ["node", "react", "react-dom", "jest"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "hanuman_*.tsx", "hanuman_*.ts", "services/**/*.ts", "services/**/*.tsx", "components/**/*.ts", "components/**/*.tsx", "pages/**/*.ts", "pages/**/*.tsx", "utils/**/*.ts", "hooks/**/*.ts", "contexts/**/*.ts", "types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "dist", "build", "coverage", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "ts-node": {"compilerOptions": {"module": "CommonJS"}}}