import React, { useState, useEffect, useRef } from 'react';
import { Brain, Zap, Network, Activity, TrendingUp, Settings, Play, Pause, RotateCcw } from 'lucide-react';

// Types pour l'interface de neuroplasticité
interface SynapticConnection {
  id: string;
  sourceAgent: string;
  targetAgent: string;
  strength: number;
  adaptationType: 'LTP' | 'LTD' | 'formation' | 'pruning';
  lastActivity: Date;
  successRate: number;
  latency: number;
}

interface NeuralAdaptation {
  id: string;
  timestamp: Date;
  agentId: string;
  connectionId: string;
  adaptationType: 'strengthen' | 'weaken' | 'create' | 'remove';
  stimulus: string;
  result: 'success' | 'failure' | 'pending';
  metrics: {
    strengthChange: number;
    latencyChange: number;
    successRateChange: number;
  };
}

interface NeuroplasticityMetrics {
  totalConnections: number;
  activeConnections: number;
  adaptationsPerMinute: number;
  averageStrength: number;
  networkEfficiency: number;
  learningRate: number;
}

interface PlasticityConfig {
  ltpThreshold: number;
  ltdThreshold: number;
  pruningThreshold: number;
  formationThreshold: number;
  learningRate: number;
  adaptationEnabled: boolean;
}

const HanumanNeuroplasticityInterface = ({ darkMode = true }) => {
  const [connections, setConnections] = useState<SynapticConnection[]>([]);
  const [adaptations, setAdaptations] = useState<NeuralAdaptation[]>([]);
  const [metrics, setMetrics] = useState<NeuroplasticityMetrics>({
    totalConnections: 47,
    activeConnections: 42,
    adaptationsPerMinute: 12.3,
    averageStrength: 0.73,
    networkEfficiency: 0.89,
    learningRate: 0.15
  });
  const [config, setConfig] = useState<PlasticityConfig>({
    ltpThreshold: 0.8,
    ltdThreshold: 0.3,
    pruningThreshold: 0.1,
    formationThreshold: 0.9,
    learningRate: 0.15,
    adaptationEnabled: true
  });
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [selectedConnection, setSelectedConnection] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // Simulation des données de connexions synaptiques
  useEffect(() => {
    const mockConnections: SynapticConnection[] = [
      {
        id: 'conn-001',
        sourceAgent: 'agent-frontend',
        targetAgent: 'agent-backend',
        strength: 0.85,
        adaptationType: 'LTP',
        lastActivity: new Date(),
        successRate: 0.92,
        latency: 45
      },
      {
        id: 'conn-002',
        sourceAgent: 'agent-evolution',
        targetAgent: 'agent-security',
        strength: 0.67,
        adaptationType: 'formation',
        lastActivity: new Date(Date.now() - 30000),
        successRate: 0.78,
        latency: 67
      },
      {
        id: 'conn-003',
        sourceAgent: 'cortex-central',
        targetAgent: 'agent-devops',
        strength: 0.91,
        adaptationType: 'LTP',
        lastActivity: new Date(Date.now() - 15000),
        successRate: 0.95,
        latency: 23
      },
      {
        id: 'conn-004',
        sourceAgent: 'agent-qa',
        targetAgent: 'agent-backend',
        strength: 0.23,
        adaptationType: 'LTD',
        lastActivity: new Date(Date.now() - 120000),
        successRate: 0.34,
        latency: 156
      }
    ];
    setConnections(mockConnections);

    const mockAdaptations: NeuralAdaptation[] = [
      {
        id: 'adapt-001',
        timestamp: new Date(),
        agentId: 'agent-evolution',
        connectionId: 'conn-001',
        adaptationType: 'strengthen',
        stimulus: 'successful_deployment',
        result: 'success',
        metrics: {
          strengthChange: +0.05,
          latencyChange: -3,
          successRateChange: +0.02
        }
      },
      {
        id: 'adapt-002',
        timestamp: new Date(Date.now() - 45000),
        agentId: 'agent-evolution',
        connectionId: 'conn-004',
        adaptationType: 'weaken',
        stimulus: 'repeated_failures',
        result: 'success',
        metrics: {
          strengthChange: -0.12,
          latencyChange: +15,
          successRateChange: -0.08
        }
      }
    ];
    setAdaptations(mockAdaptations);
  }, []);

  // Connexion WebSocket pour les mises à jour en temps réel
  useEffect(() => {
    if (isMonitoring) {
      // Simulation de connexion WebSocket avec l'agent evolution
      wsRef.current = new WebSocket('ws://localhost:8080/neuroplasticity');
      
      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'adaptation') {
          setAdaptations(prev => [data.adaptation, ...prev.slice(0, 9)]);
        } else if (data.type === 'metrics') {
          setMetrics(data.metrics);
        }
      };

      return () => {
        wsRef.current?.close();
      };
    }
  }, [isMonitoring]);

  const handleConfigChange = (key: keyof PlasticityConfig, value: number | boolean) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    // Envoyer la configuration à l'agent evolution
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'config_update',
        config: { ...config, [key]: value }
      }));
    }
  };

  const triggerAdaptation = (connectionId: string, stimulus: string) => {
    // Déclencher une adaptation manuelle
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'manual_adaptation',
        connectionId,
        stimulus
      }));
    }
  };

  const resetNetwork = () => {
    // Réinitialiser le réseau neuroplastique
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'reset_network'
      }));
    }
  };

  const getConnectionColor = (strength: number) => {
    if (strength >= 0.8) return 'text-green-400';
    if (strength >= 0.5) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getAdaptationIcon = (type: string) => {
    switch (type) {
      case 'strengthen': return <TrendingUp className="w-4 h-4 text-green-400" />;
      case 'weaken': return <TrendingUp className="w-4 h-4 text-red-400 rotate-180" />;
      case 'create': return <Zap className="w-4 h-4 text-blue-400" />;
      case 'remove': return <RotateCcw className="w-4 h-4 text-gray-400" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* En-tête */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
              <Brain className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Neuroplasticité Hanuman
              </h1>
              <p className="text-gray-400 mt-1">
                Adaptation continue des connexions synaptiques
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setIsMonitoring(!isMonitoring)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isMonitoring 
                  ? 'bg-red-500 hover:bg-red-600 text-white' 
                  : 'bg-green-500 hover:bg-green-600 text-white'
              }`}
            >
              {isMonitoring ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              <span>{isMonitoring ? 'Arrêter' : 'Démarrer'}</span>
            </button>
            <button
              onClick={resetNetwork}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset</span>
            </button>
          </div>
        </div>
      </div>

      {/* Métriques globales */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Connexions Totales</p>
              <p className="text-2xl font-bold text-blue-400">{metrics.totalConnections}</p>
            </div>
            <Network className="w-8 h-8 text-blue-400" />
          </div>
        </div>
        
        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Connexions Actives</p>
              <p className="text-2xl font-bold text-green-400">{metrics.activeConnections}</p>
            </div>
            <Activity className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Adaptations/min</p>
              <p className="text-2xl font-bold text-purple-400">{metrics.adaptationsPerMinute}</p>
            </div>
            <Zap className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Force Moyenne</p>
              <p className="text-2xl font-bold text-yellow-400">{(metrics.averageStrength * 100).toFixed(0)}%</p>
            </div>
            <TrendingUp className="w-8 h-8 text-yellow-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Efficacité Réseau</p>
              <p className="text-2xl font-bold text-cyan-400">{(metrics.networkEfficiency * 100).toFixed(0)}%</p>
            </div>
            <Brain className="w-8 h-8 text-cyan-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Taux Apprentissage</p>
              <p className="text-2xl font-bold text-pink-400">{(metrics.learningRate * 100).toFixed(0)}%</p>
            </div>
            <Settings className="w-8 h-8 text-pink-400" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Configuration de la neuroplasticité */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <Settings className="w-5 h-5 mr-2 text-purple-400" />
            Configuration Neuroplastique
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Seuil LTP (Long-Term Potentiation)
              </label>
              <input
                type="range"
                min="0.5"
                max="1.0"
                step="0.05"
                value={config.ltpThreshold}
                onChange={(e) => handleConfigChange('ltpThreshold', parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <span className="text-sm text-gray-400">{config.ltpThreshold}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Seuil LTD (Long-Term Depression)
              </label>
              <input
                type="range"
                min="0.1"
                max="0.5"
                step="0.05"
                value={config.ltdThreshold}
                onChange={(e) => handleConfigChange('ltdThreshold', parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <span className="text-sm text-gray-400">{config.ltdThreshold}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Taux d'Apprentissage
              </label>
              <input
                type="range"
                min="0.01"
                max="0.5"
                step="0.01"
                value={config.learningRate}
                onChange={(e) => handleConfigChange('learningRate', parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <span className="text-sm text-gray-400">{config.learningRate}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-400">Adaptation Automatique</span>
              <button
                onClick={() => handleConfigChange('adaptationEnabled', !config.adaptationEnabled)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  config.adaptationEnabled ? 'bg-green-500' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    config.adaptationEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Adaptations récentes */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <Activity className="w-5 h-5 mr-2 text-green-400" />
            Adaptations Récentes
          </h3>
          
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {adaptations.map((adaptation) => (
              <div
                key={adaptation.id}
                className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getAdaptationIcon(adaptation.adaptationType)}
                    <span className="font-medium">{adaptation.adaptationType}</span>
                  </div>
                  <span className="text-xs text-gray-400">
                    {adaptation.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <p className="text-sm text-gray-400 mb-1">
                  Agent: {adaptation.agentId} → {adaptation.connectionId}
                </p>
                <p className="text-sm text-gray-400 mb-2">
                  Stimulus: {adaptation.stimulus}
                </p>
                <div className="flex items-center space-x-4 text-xs">
                  <span className={`${adaptation.metrics.strengthChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    Force: {adaptation.metrics.strengthChange > 0 ? '+' : ''}{adaptation.metrics.strengthChange.toFixed(3)}
                  </span>
                  <span className={`${adaptation.metrics.latencyChange <= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    Latence: {adaptation.metrics.latencyChange > 0 ? '+' : ''}{adaptation.metrics.latencyChange}ms
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanNeuroplasticityInterface;
