import React, { useState, useEffect, useRef } from 'react';
import { Ear, Radio, Waves, Volume2, VolumeX, Activity, Zap, AlertTriangle, CheckCircle, Database, TrendingUp, BarChart3, Wifi, WifiOff } from 'lucide-react';

// Interfaces pour l'écoute des données
interface DataStream {
  id: string;
  name: string;
  source: string;
  intensity: number;
  frequency: number;
  status: 'active' | 'inactive' | 'error';
  dataRate: number;
  lastUpdate: Date;
  type: 'kafka' | 'redis' | 'websocket' | 'api' | 'webhook';
}

interface WeakSignal {
  id: string;
  description: string;
  confidence: number;
  source: string;
  timestamp: Date;
  category: 'anomaly' | 'pattern' | 'trend' | 'alert';
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface APIHealth {
  endpoint: string;
  status: 'healthy' | 'degraded' | 'down';
  latency: number;
  uptime: number;
  errorRate: number;
  lastCheck: Date;
}

interface HearingMetrics {
  totalStreams: number;
  activeStreams: number;
  dataProcessed: number;
  averageLatency: number;
  signalsDetected: number;
  alertsTriggered: number;
}

const HanumanHearingInterface = ({ darkMode = true }) => {
  const [dataStreams, setDataStreams] = useState<DataStream[]>([]);
  const [weakSignals, setWeakSignals] = useState<WeakSignal[]>([]);
  const [apiHealth, setApiHealth] = useState<APIHealth[]>([]);
  const [hearingMetrics, setHearingMetrics] = useState<HearingMetrics>({
    totalStreams: 15,
    activeStreams: 12,
    dataProcessed: 2847,
    averageLatency: 89,
    signalsDetected: 23,
    alertsTriggered: 3
  });
  const [isConnected, setIsConnected] = useState(false);
  const [audioVisualization, setAudioVisualization] = useState<number[]>(new Array(20).fill(0));
  const wsRef = useRef<WebSocket | null>(null);

  // Connexion aux services de collecte de données
  useEffect(() => {
    const connectToDataServices = () => {
      try {
        // Connexion WebSocket pour l'écoute en temps réel
        wsRef.current = new WebSocket('ws://localhost:3002/hearing');
        
        wsRef.current.onopen = () => {
          console.log('🔗 Connexion établie avec les services de données');
          setIsConnected(true);
          
          // Demander le statut initial
          wsRef.current?.send(JSON.stringify({
            type: 'GET_STREAMS_STATUS',
            timestamp: Date.now()
          }));
        };

        wsRef.current.onmessage = (event) => {
          const data = JSON.parse(event.data);
          handleDataMessage(data);
        };

        wsRef.current.onclose = () => {
          console.log('❌ Connexion fermée avec les services de données');
          setIsConnected(false);
          
          // Tentative de reconnexion
          setTimeout(connectToDataServices, 5000);
        };

        wsRef.current.onerror = (error) => {
          console.error('🚨 Erreur WebSocket:', error);
          setIsConnected(false);
        };

      } catch (error) {
        console.error('🚨 Erreur de connexion:', error);
        setIsConnected(false);
      }
    };

    connectToDataServices();

    // Simulation de données en attendant la connexion réelle
    const simulationInterval = setInterval(() => {
      if (!isConnected) {
        simulateHearingActivity();
      }
    }, 2000);

    // Mise à jour de la visualisation audio
    const visualizationInterval = setInterval(() => {
      updateAudioVisualization();
    }, 100);

    return () => {
      clearInterval(simulationInterval);
      clearInterval(visualizationInterval);
      wsRef.current?.close();
    };
  }, []);

  const handleDataMessage = (data: any) => {
    switch (data.type) {
      case 'STREAM_DATA':
        updateDataStream(data.stream);
        break;
        
      case 'WEAK_SIGNAL_DETECTED':
        setWeakSignals(prev => [data.signal, ...prev.slice(0, 9)]);
        break;
        
      case 'API_HEALTH_UPDATE':
        setApiHealth(data.healthData);
        break;
        
      case 'METRICS_UPDATE':
        setHearingMetrics(data.metrics);
        break;
        
      case 'STREAMS_STATUS':
        setDataStreams(data.streams);
        break;
        
      default:
        console.log('📨 Message non géré:', data);
    }
  };

  const simulateHearingActivity = () => {
    // Simulation des flux de données
    const mockStreams: DataStream[] = [
      {
        id: 'kafka-events',
        name: 'Kafka Events',
        source: 'kafka:9092',
        intensity: 0.3 + Math.random() * 0.7,
        frequency: 50 + Math.random() * 100,
        status: 'active',
        dataRate: 1200 + Math.random() * 800,
        lastUpdate: new Date(),
        type: 'kafka'
      },
      {
        id: 'redis-cache',
        name: 'Redis Cache',
        source: 'redis:6379',
        intensity: 0.2 + Math.random() * 0.6,
        frequency: 30 + Math.random() * 70,
        status: 'active',
        dataRate: 800 + Math.random() * 600,
        lastUpdate: new Date(),
        type: 'redis'
      },
      {
        id: 'api-webhooks',
        name: 'API Webhooks',
        source: 'webhooks.api',
        intensity: 0.1 + Math.random() * 0.4,
        frequency: 10 + Math.random() * 40,
        status: Math.random() > 0.8 ? 'error' : 'active',
        dataRate: 200 + Math.random() * 300,
        lastUpdate: new Date(),
        type: 'webhook'
      },
      {
        id: 'websocket-live',
        name: 'WebSocket Live',
        source: 'ws://live-data',
        intensity: 0.4 + Math.random() * 0.6,
        frequency: 80 + Math.random() * 120,
        status: 'active',
        dataRate: 1500 + Math.random() * 1000,
        lastUpdate: new Date(),
        type: 'websocket'
      }
    ];

    setDataStreams(mockStreams);

    // Simulation des signaux faibles
    if (Math.random() > 0.7) {
      const categories = ['anomaly', 'pattern', 'trend', 'alert'] as const;
      const severities = ['low', 'medium', 'high', 'critical'] as const;
      
      const newSignal: WeakSignal = {
        id: `signal_${Date.now()}`,
        description: `Signal détecté dans ${mockStreams[Math.floor(Math.random() * mockStreams.length)].name}`,
        confidence: 60 + Math.random() * 40,
        source: 'Pattern Analysis Engine',
        timestamp: new Date(),
        category: categories[Math.floor(Math.random() * categories.length)],
        severity: severities[Math.floor(Math.random() * severities.length)]
      };
      
      setWeakSignals(prev => [newSignal, ...prev.slice(0, 9)]);
    }

    // Simulation de la santé des APIs
    const mockAPIHealth: APIHealth[] = [
      {
        endpoint: '/api/agents/status',
        status: Math.random() > 0.9 ? 'degraded' : 'healthy',
        latency: 50 + Math.random() * 100,
        uptime: 99.5 + Math.random() * 0.5,
        errorRate: Math.random() * 2,
        lastCheck: new Date()
      },
      {
        endpoint: '/api/data/stream',
        status: Math.random() > 0.95 ? 'down' : 'healthy',
        latency: 80 + Math.random() * 120,
        uptime: 98.8 + Math.random() * 1.2,
        errorRate: Math.random() * 3,
        lastCheck: new Date()
      },
      {
        endpoint: '/api/monitoring/metrics',
        status: 'healthy',
        latency: 30 + Math.random() * 50,
        uptime: 99.9,
        errorRate: Math.random() * 0.5,
        lastCheck: new Date()
      }
    ];

    setApiHealth(mockAPIHealth);
  };

  const updateDataStream = (streamData: DataStream) => {
    setDataStreams(prev => {
      const index = prev.findIndex(s => s.id === streamData.id);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = streamData;
        return updated;
      }
      return [streamData, ...prev];
    });
  };

  const updateAudioVisualization = () => {
    setAudioVisualization(prev => {
      const newVisualization = prev.map((_, index) => {
        const baseIntensity = dataStreams.reduce((sum, stream) => 
          sum + (stream.status === 'active' ? stream.intensity : 0), 0) / Math.max(dataStreams.length, 1);
        
        return Math.max(0, Math.min(1, baseIntensity + (Math.random() - 0.5) * 0.3));
      });
      return newVisualization;
    });
  };

  const getStreamTypeIcon = (type: string) => {
    switch (type) {
      case 'kafka': return <Database className="text-purple-400" size={16} />;
      case 'redis': return <Zap className="text-red-400" size={16} />;
      case 'websocket': return <Activity className="text-blue-400" size={16} />;
      case 'api': return <Radio className="text-green-400" size={16} />;
      case 'webhook': return <Waves className="text-orange-400" size={16} />;
      default: return <Radio className="text-gray-400" size={16} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'healthy': return 'text-green-400';
      case 'degraded': return 'text-yellow-400';
      case 'inactive': case 'down': case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-blue-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-orange-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'anomaly': return <AlertTriangle className="text-orange-400" size={16} />;
      case 'pattern': return <BarChart3 className="text-blue-400" size={16} />;
      case 'trend': return <TrendingUp className="text-green-400" size={16} />;
      case 'alert': return <AlertTriangle className="text-red-400" size={16} />;
      default: return <Activity className="text-gray-400" size={16} />;
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">
        
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
              <Ear className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Ouïe d'Hanuman
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Organe d'Écoute • Collecte de Données Temps Réel
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected 
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' 
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Écoute Active' : 'Hors Ligne'}
              </span>
            </div>
          </div>
        </div>

        {/* Métriques Globales */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques d'Écoute
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400">
                {hearingMetrics.totalStreams}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Flux Totaux
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400">
                {hearingMetrics.activeStreams}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Flux Actifs
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400">
                {hearingMetrics.dataProcessed.toLocaleString()}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Données Traitées
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400">
                {hearingMetrics.averageLatency}ms
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Latence Moyenne
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-400">
                {hearingMetrics.signalsDetected}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Signaux Détectés
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-red-400">
                {hearingMetrics.alertsTriggered}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Alertes Déclenchées
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          
          {/* Visualisation Audio */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              👂 Écoute Active
            </h3>
            <div className="flex items-end justify-center space-x-1 h-32 mb-4">
              {audioVisualization.map((intensity, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-t from-blue-500 to-purple-500 rounded-t transition-all duration-100"
                  style={{
                    height: `${Math.max(4, intensity * 100)}%`,
                    width: '12px'
                  }}
                />
              ))}
            </div>
            <div className="text-center">
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Intensité des flux de données en temps réel
              </div>
            </div>
          </div>

          {/* Flux de Données */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              📊 Flux de Données
            </h3>
            <div className="space-y-3">
              {dataStreams.map((stream) => (
                <div key={stream.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      {getStreamTypeIcon(stream.type)}
                      <div>
                        <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {stream.name}
                        </span>
                        <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {stream.source}
                        </div>
                      </div>
                    </div>
                    <div className={`text-xs font-medium ${getStatusColor(stream.status)}`}>
                      {stream.status}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
                      {stream.dataRate.toFixed(0)} msg/s
                    </span>
                    <div className="w-16 bg-gray-300 dark:bg-gray-600 rounded-full h-1">
                      <div 
                        className="h-1 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"
                        style={{ width: `${stream.intensity * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Signaux Faibles */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              📡 Signaux Faibles
            </h3>
            <div className="space-y-3">
              {weakSignals.map((signal) => (
                <div key={signal.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center space-x-3 mb-2">
                    {getCategoryIcon(signal.category)}
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {signal.description}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {signal.source}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded ${getSeverityColor(signal.severity)} bg-opacity-20`}>
                        {signal.severity}
                      </span>
                    </div>
                    <span className={`text-xs font-medium ${
                      signal.confidence > 80 ? 'text-green-400' :
                      signal.confidence > 60 ? 'text-yellow-400' : 'text-red-400'
                    }`}>
                      {signal.confidence.toFixed(0)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Santé des APIs */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-2`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🔗 Santé des APIs
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {apiHealth.map((api, index) => (
                <div key={index} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-3">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {api.endpoint}
                    </span>
                    <div className="flex items-center space-x-2">
                      {api.status === 'healthy' ? <Volume2 className="text-green-400" size={16} /> : 
                       api.status === 'degraded' ? <Volume2 className="text-yellow-400" size={16} /> :
                       <VolumeX className="text-red-400" size={16} />}
                      <span className={`text-xs font-medium ${getStatusColor(api.status)}`}>
                        {api.status}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div>
                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Latence:</span>
                      <div className="font-medium">{api.latency.toFixed(0)}ms</div>
                    </div>
                    <div>
                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Uptime:</span>
                      <div className="font-medium">{api.uptime.toFixed(1)}%</div>
                    </div>
                    <div>
                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Erreurs:</span>
                      <div className="font-medium">{api.errorRate.toFixed(1)}%</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanHearingInterface;
