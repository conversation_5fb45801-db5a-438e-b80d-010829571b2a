import React, { useState, useEffect, useRef } from 'react';
import { Activity, ArrowRight, GitBranch, Package, Play, Pause, RotateCcw, CheckCircle, AlertCircle, Clock, BarChart3, Wifi, WifiOff, Zap, Upload } from 'lucide-react';

// Interfaces pour l'Agent Migration (Cortex Moteur)
interface MigrationTask {
  id: string;
  name: string;
  type: 'code-migration' | 'database-migration' | 'infrastructure-migration' | 'dependency-upgrade' | 'framework-migration';
  source: string;
  target: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  progress: number;
  startTime?: Date;
  endTime?: Date;
  estimatedDuration: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  dependencies: string[];
  logs: string[];
}

interface DeploymentAction {
  id: string;
  name: string;
  type: 'deploy' | 'rollback' | 'scale' | 'restart' | 'backup';
  environment: 'development' | 'staging' | 'production';
  status: 'pending' | 'running' | 'completed' | 'failed';
  timestamp: Date;
  duration?: number;
  result?: string;
}

interface MotorMetrics {
  totalMigrations: number;
  activeMigrations: number;
  successRate: number;
  averageDuration: number;
  deploymentsToday: number;
  systemLoad: number;
}

interface CodeTransformation {
  id: string;
  filePath: string;
  fromLanguage: string;
  toLanguage: string;
  linesChanged: number;
  complexity: number;
  status: 'analyzing' | 'transforming' | 'testing' | 'completed' | 'failed';
  confidence: number;
}

const HanumanMotorInterface = ({ darkMode = true }) => {
  const [migrations, setMigrations] = useState<MigrationTask[]>([]);
  const [deployments, setDeployments] = useState<DeploymentAction[]>([]);
  const [transformations, setTransformations] = useState<CodeTransformation[]>([]);
  const [metrics, setMetrics] = useState<MotorMetrics>({
    totalMigrations: 156,
    activeMigrations: 3,
    successRate: 94.2,
    averageDuration: 847,
    deploymentsToday: 12,
    systemLoad: 67.3
  });
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [isConnected, setIsConnected] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);

  // Connexion à l'Agent Migration
  useEffect(() => {
    const connectToMigrationAgent = () => {
      try {
        wsRef.current = new WebSocket('ws://localhost:3006/migration');
        
        wsRef.current.onopen = () => {
          console.log('🔗 Connexion établie avec Agent Migration');
          setIsConnected(true);
          
          wsRef.current?.send(JSON.stringify({
            type: 'GET_MIGRATION_STATUS',
            timestamp: Date.now()
          }));
        };

        wsRef.current.onmessage = (event) => {
          const data = JSON.parse(event.data);
          handleMigrationMessage(data);
        };

        wsRef.current.onclose = () => {
          console.log('❌ Connexion fermée avec Agent Migration');
          setIsConnected(false);
          setTimeout(connectToMigrationAgent, 5000);
        };

        wsRef.current.onerror = (error) => {
          console.error('❌ Erreur WebSocket migration:', error);
        };

      } catch (error) {
        console.error('❌ Erreur de connexion migration:', error);
        setTimeout(connectToMigrationAgent, 5000);
      }
    };

    connectToMigrationAgent();
    return () => wsRef.current?.close();
  }, []);

  // Simulation de données en temps réel
  useEffect(() => {
    const interval = setInterval(() => {
      simulateMigrationActivity();
      updateMetrics();
      updateProgress();
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const handleMigrationMessage = (data: any) => {
    switch (data.type) {
      case 'MIGRATION_STARTED':
        updateMigrationStatus(data.migrationId, 'running');
        break;
      case 'MIGRATION_COMPLETED':
        updateMigrationStatus(data.migrationId, 'completed');
        break;
      case 'MIGRATION_FAILED':
        updateMigrationStatus(data.migrationId, 'failed');
        break;
      case 'DEPLOYMENT_COMPLETED':
        addDeploymentAction(data.deployment);
        break;
      case 'METRICS_UPDATE':
        setMetrics(data.metrics);
        break;
    }
  };

  const simulateMigrationActivity = () => {
    // Simulation de nouvelles migrations
    const migrationTypes = ['code-migration', 'database-migration', 'infrastructure-migration', 'dependency-upgrade', 'framework-migration'] as const;
    const priorities = ['low', 'medium', 'high'] as const;
    
    if (Math.random() < 0.3) {
      const mockMigration: MigrationTask = {
        id: `mig_${Date.now()}`,
        name: generateMigrationName(),
        type: migrationTypes[Math.floor(Math.random() * migrationTypes.length)],
        source: generateSource(),
        target: generateTarget(),
        status: 'pending',
        progress: 0,
        estimatedDuration: 300 + Math.random() * 1200,
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        dependencies: [],
        logs: []
      };

      setMigrations(prev => [mockMigration, ...prev.slice(0, 19)]);
    }

    // Simulation de déploiements
    if (Math.random() < 0.2) {
      const deploymentTypes = ['deploy', 'rollback', 'scale', 'restart', 'backup'] as const;
      const environments = ['development', 'staging', 'production'] as const;
      
      const mockDeployment: DeploymentAction = {
        id: `dep_${Date.now()}`,
        name: generateDeploymentName(),
        type: deploymentTypes[Math.floor(Math.random() * deploymentTypes.length)],
        environment: environments[Math.floor(Math.random() * environments.length)],
        status: 'completed',
        timestamp: new Date(),
        duration: 30 + Math.random() * 300,
        result: 'Success'
      };

      setDeployments(prev => [mockDeployment, ...prev.slice(0, 19)]);
    }
  };

  const generateMigrationName = (): string => {
    const names = [
      'Migration React 17 vers React 18',
      'Mise à jour Node.js 16 vers 20',
      'Migration PostgreSQL 13 vers 15',
      'Upgrade TypeScript 4.x vers 5.x',
      'Migration Docker vers Kubernetes',
      'Upgrade Express.js vers Fastify',
      'Migration MongoDB vers PostgreSQL',
      'Upgrade Tailwind CSS v3'
    ];
    return names[Math.floor(Math.random() * names.length)];
  };

  const generateSource = (): string => {
    const sources = ['React 17', 'Node.js 16', 'PostgreSQL 13', 'TypeScript 4.x', 'Docker', 'Express.js', 'MongoDB', 'Tailwind v2'];
    return sources[Math.floor(Math.random() * sources.length)];
  };

  const generateTarget = (): string => {
    const targets = ['React 18', 'Node.js 20', 'PostgreSQL 15', 'TypeScript 5.x', 'Kubernetes', 'Fastify', 'PostgreSQL', 'Tailwind v3'];
    return targets[Math.floor(Math.random() * targets.length)];
  };

  const generateDeploymentName = (): string => {
    const names = [
      'Déploiement API v2.1.0',
      'Rollback Frontend v1.8.3',
      'Scale Backend Pods',
      'Restart Database Service',
      'Backup Production Data',
      'Deploy Security Patch',
      'Update Load Balancer',
      'Migrate Static Assets'
    ];
    return names[Math.floor(Math.random() * names.length)];
  };

  const updateMigrationStatus = (id: string, status: string) => {
    setMigrations(prev => prev.map(mig => 
      mig.id === id ? { 
        ...mig, 
        status: status as any,
        startTime: status === 'running' ? new Date() : mig.startTime,
        endTime: status === 'completed' || status === 'failed' ? new Date() : undefined
      } : mig
    ));
  };

  const addDeploymentAction = (deployment: DeploymentAction) => {
    setDeployments(prev => [deployment, ...prev]);
  };

  const updateProgress = () => {
    setMigrations(prev => prev.map(mig => {
      if (mig.status === 'running') {
        const newProgress = Math.min(100, mig.progress + Math.random() * 15);
        if (newProgress >= 100) {
          return { ...mig, progress: 100, status: 'completed', endTime: new Date() };
        }
        return { ...mig, progress: newProgress };
      }
      return mig;
    }));
  };

  const updateMetrics = () => {
    setMetrics(prev => ({
      ...prev,
      totalMigrations: prev.totalMigrations + (Math.random() < 0.1 ? 1 : 0),
      activeMigrations: migrations.filter(m => m.status === 'running').length,
      systemLoad: Math.max(30, Math.min(90, prev.systemLoad + (Math.random() - 0.5) * 10)),
      deploymentsToday: prev.deploymentsToday + (Math.random() < 0.05 ? 1 : 0)
    }));
  };

  const startMigration = (id: string) => {
    updateMigrationStatus(id, 'running');
    setIsExecuting(true);
    
    if (wsRef.current && isConnected) {
      wsRef.current.send(JSON.stringify({
        type: 'START_MIGRATION',
        migrationId: id
      }));
    }
    
    setTimeout(() => setIsExecuting(false), 2000);
  };

  const pauseMigration = (id: string) => {
    updateMigrationStatus(id, 'paused');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle size={14} className="text-green-400" />;
      case 'running': return <Play size={14} className="text-blue-400 animate-pulse" />;
      case 'failed': return <AlertCircle size={14} className="text-red-400" />;
      case 'paused': return <Pause size={14} className="text-yellow-400" />;
      case 'pending': return <Clock size={14} className="text-gray-400" />;
      default: return <Activity size={14} className="text-gray-400" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'code-migration': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'database-migration': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'infrastructure-migration': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'dependency-upgrade': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'framework-migration': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'border-l-red-500';
      case 'high': return 'border-l-orange-500';
      case 'medium': return 'border-l-yellow-500';
      case 'low': return 'border-l-green-500';
      default: return 'border-l-gray-500';
    }
  };

  const getEnvironmentColor = (env: string) => {
    switch (env) {
      case 'production': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'staging': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'development': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const filteredMigrations = migrations.filter(mig => {
    if (selectedFilter === 'all') return true;
    return mig.status === selectedFilter;
  });

  return (
    <div className={`h-full ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} overflow-hidden`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 border-b border-gray-200 dark:border-gray-700`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-orange-500 rounded-full flex items-center justify-center">
              <Activity className="text-white" size={24} />
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-red-400 to-orange-500 bg-clip-text text-transparent">
                🏃 Cortex Moteur • Actions Divines
              </h1>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Migrations et transformations via Agent Migration
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 
              'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                Agent Migration {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>
            
            {isExecuting && (
              <div className="flex items-center space-x-2 px-3 py-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-lg">
                <Zap size={16} className="animate-pulse" />
                <span className="text-sm font-medium">Exécution en cours...</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex h-full">
        {/* Sidebar Métriques et Contrôles */}
        <div className={`w-80 ${darkMode ? 'bg-gray-800' : 'bg-white'} border-r border-gray-200 dark:border-gray-700 p-6 overflow-y-auto`}>
          {/* Métriques Globales */}
          <div className="mb-6">
            <h3 className="text-lg font-bold mb-4 flex items-center">
              <BarChart3 className="text-red-400 mr-2" size={20} />
              Métriques Moteur
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Migrations</div>
                <div className="text-lg font-bold text-red-400">{metrics.totalMigrations}</div>
              </div>
              
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Actives</div>
                <div className="text-lg font-bold text-blue-400">{metrics.activeMigrations}</div>
              </div>
              
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Succès</div>
                <div className="text-lg font-bold text-green-400">{metrics.successRate.toFixed(1)}%</div>
              </div>
              
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Charge</div>
                <div className="text-lg font-bold text-orange-400">{metrics.systemLoad.toFixed(1)}%</div>
              </div>
            </div>
          </div>

          {/* Actions Rapides */}
          <div className="mb-6">
            <h3 className="text-sm font-bold mb-3 flex items-center">
              <Zap className="text-red-400 mr-2" size={16} />
              Actions Rapides
            </h3>
            
            <div className="space-y-2">
              <button
                onClick={() => console.log('Nouvelle migration')}
                className="w-full p-2 text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded hover:bg-blue-200 dark:hover:bg-blue-800"
              >
                <Upload size={14} className="inline mr-2" />
                Nouvelle Migration
              </button>
              <button
                onClick={() => console.log('Déploiement rapide')}
                className="w-full p-2 text-sm bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded hover:bg-green-200 dark:hover:bg-green-800"
              >
                <Package size={14} className="inline mr-2" />
                Déploiement Rapide
              </button>
              <button
                onClick={() => console.log('Rollback')}
                className="w-full p-2 text-sm bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded hover:bg-orange-200 dark:hover:bg-orange-800"
              >
                <RotateCcw size={14} className="inline mr-2" />
                Rollback d'Urgence
              </button>
            </div>
          </div>

          {/* Filtres */}
          <div className="mb-6">
            <h3 className="text-sm font-bold mb-3">Filtres</h3>
            
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className={`w-full p-2 rounded border text-sm ${
                darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
              }`}
            >
              <option value="all">Toutes les migrations</option>
              <option value="pending">En attente</option>
              <option value="running">En cours</option>
              <option value="completed">Terminées</option>
              <option value="failed">Échouées</option>
              <option value="paused">En pause</option>
            </select>
          </div>

          {/* Déploiements Récents */}
          <div className="mb-6">
            <h3 className="text-sm font-bold mb-3 flex items-center">
              <Package className="text-red-400 mr-2" size={16} />
              Déploiements Récents
            </h3>
            
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {deployments.slice(0, 5).map((deployment) => (
                <div
                  key={deployment.id}
                  className={`p-2 rounded text-xs ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium truncate">{deployment.name}</span>
                    {getStatusIcon(deployment.status)}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`px-1 py-0.5 rounded text-xs ${getEnvironmentColor(deployment.environment)}`}>
                      {deployment.environment}
                    </span>
                    <span className="text-gray-500">
                      {deployment.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Zone Migrations */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-bold">Migrations et Transformations</h2>
            <div className="text-sm text-gray-500">
              {filteredMigrations.length} migrations
            </div>
          </div>

          {filteredMigrations.length === 0 ? (
            <div className="text-center text-gray-500 mt-20">
              <GitBranch size={48} className="mx-auto mb-4 opacity-50" />
              <p>Aucune migration trouvée</p>
            </div>
          ) : (
            filteredMigrations.map((migration) => (
              <div
                key={migration.id}
                className={`p-4 rounded-lg border-l-4 ${getPriorityColor(migration.priority)} ${
                  darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
                } hover:shadow-lg transition-shadow`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(migration.status)}
                    <div>
                      <h3 className="font-medium text-lg">{migration.name}</h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className={`text-xs px-2 py-1 rounded ${getTypeColor(migration.type)}`}>
                          {migration.type}
                        </span>
                        <span className="text-xs text-gray-500 flex items-center">
                          {migration.source} <ArrowRight size={12} className="mx-1" /> {migration.target}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {migration.status === 'pending' && (
                      <button
                        onClick={() => startMigration(migration.id)}
                        className="p-2 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded hover:bg-green-200 dark:hover:bg-green-800"
                      >
                        <Play size={16} />
                      </button>
                    )}
                    {migration.status === 'running' && (
                      <button
                        onClick={() => pauseMigration(migration.id)}
                        className="p-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded hover:bg-yellow-200 dark:hover:bg-yellow-800"
                      >
                        <Pause size={16} />
                      </button>
                    )}
                  </div>
                </div>
                
                {migration.status === 'running' && (
                  <div className="mb-3">
                    <div className="flex items-center justify-between text-xs mb-1">
                      <span>Progression</span>
                      <span>{migration.progress.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${migration.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
                
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-500">
                      Durée estimée: {Math.floor(migration.estimatedDuration / 60)}min {migration.estimatedDuration % 60}s
                    </span>
                    {migration.startTime && (
                      <span className="text-gray-500">
                        Démarré: {migration.startTime.toLocaleTimeString()}
                      </span>
                    )}
                  </div>
                  
                  <span className={`px-2 py-1 rounded ${
                    migration.priority === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                    migration.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                    migration.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                    'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  }`}>
                    {migration.priority}
                  </span>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default HanumanMotorInterface;
